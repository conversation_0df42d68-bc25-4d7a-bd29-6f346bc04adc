dubbo:
  application:
    name: script-dubbo
    #dubbo qos服务端口
    qosPort: 11270
  registry:
    #注册中心地址，可以从maven profile变量获取，为了支持多注册中心
    #address: zookeeper://************:2181
    address: nacos://************:8848?namespace=123456
  protocol:
    #dubbo服务端口
    port: 21270
    #dubbo线程池大小
    threads: 300
  provider:
    timeout: 200000
    #多网卡情况需要指定本机注册到注册中心的IP是哪个
    # host: *********
    validation: true
  consumer:
    timeout: 200000
    retries: 5
    check: false
server:
  #web服务端口
  port: 9420
spring:
  jackson:
    time-zone: GMT+8
    date-format: yyyy-MM-dd HH:mm:ss
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    url: *****************************************************
    #    url: **********************************************************************************************************************
    # url: *********************************************************************************************************************************
    username: postgres
    # username: root
    password: ideal123
    #password: ENC(4c4d6cdcc26fd9371a9bcf9f56d15019)
    druid:
      # 初始化时建立物理连接的个数
      initial-size: 5
      # 连接池的最小空闲数量
      min-idle: 5
      # 连接池最大连接数量
      maxActive: 20
      # 获取连接时最大等待时间，单位毫秒
      maxWait: 3000
      # 既作为检测的间隔时间又作为testWhileIdel执行的依据
      timeBetweenEvictionRunsMillis: 60000
      # 销毁线程时检测当前连接的最后活动时间和当前时间差大于该值时，关闭当前连接(配置连接在池中的最小生存时间)
      minEvictableIdleTimeMillis: 300000
      # 用来检测数据库连接是否有效的sql 必须是一个查询语句
      validationQuery: SELECT 'x'
      # 申请连接的时候检测，如果空闲时间大于timeBetweenEvictionRunsMillis，执行validationQuery检测连接是否有效。
      testWhileIdle: true
      # 申请连接时会执行validationQuery检测连接是否有效,开启会降低性能,默认为true
      testOnBorrow: false
      # 归还连接时会执行validationQuery检测连接是否有效,开启会降低性能,默认为true
      testOnReturn: false
      # 是否缓存preparedStatement, 也就是PSCache,PSCache对支持游标的数据库性能提升巨大，比如说oracle,在mysql下建议关闭。
      poolPreparedStatements: true
      # 要启用PSCache，必须配置大于0，当大于0时，poolPreparedStatements自动触发修改为true。在Druid中，不会存在Oracle下PSCache占用内存过多的问题，可以把这个数值配置大一些，比如说100
      maxPoolPreparedStatementPerConnectionSize: 20
      # 通过connectProperties属性来打开mergeSql功能；慢SQL记录
      #connection-properties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB
  redis:
    cluster:
      #redis集群地址
      nodes: ************:27001,************:27001,************:27001
      max-redirects: 5
    database: 0
    password: ideal123
    timeout: 180s
  liquibase:
    contexts: dev,all
    enabled: true
    drop-first: false
    change-log: classpath:db/changelog/db.changelog-master.xml
  cloud:
    #  nacos:
    #   discovery:
    #      ip: *********
    function:
      definition: scriptExecuteResult;scriptErrorResult;scriptSendResult;toolBoxReceiveTaskSendResult;receiveTaskExecuteResult;toolboxReceiveToolsChangeResult;toolboxAgentManagementUpdateAgentInfo;toolboxSystemSyncComputerList;scriptReferrerInfo;systemSyncOrganization
    stream:
      rocketmq:
        binder:
          #rocketmq nameServer地址
          name-server: *************:9876;*************:9876
      bindings:
        scriptExecuteResult-in-0:
          destination: script-execute-result
          content-type: application/json
          group: script-execute-result-group
        scriptErrorResult-in-0:
          destination: script-error-result
          content-type: application/json
          group: script-error-result-group
        scriptSendResult-in-0:
          destination: script-send-result
          content-type: application/json
          group: script-send-result-group
        scriptDefaultVersionChange-out-0:
          destination: script-default-version-change
          content-type: application/json
          group: script-default-version-change
        #中信作业中心任务开始、结束，任务统计mq管道配置
        scriptJobStart-out-0:
          destination: job-start
          content-type: application/json
          group: job-center-job-start
        scriptJobEnd-out-0:
          destination: job-end
          content-type: application/json
          group: job-center-job-end
        #定时任务调用脚本任务后，脚本服务化推送mq管道、topic配置=====start
        crontabsExecuteResult-out-0:
          destination: crontabsExecuteResult-dev
          content-type: application/json
          group: crontabsExecuteResult-group-dev
        crontabsSendResult-out-0:
          destination: crontabsSendResult-dev
          content-type: application/json
          group: crontabsSendResult-group-dev
        crontabsErrorResult-out-0:
          destination: crontabsErrorResult-dev
          content-type: application/json
          group: crontabsErrorResult-group-dev
        #定时任务调用脚本任务后，脚本服务化推送mq管道、topic配置=====end
        sendToolboxToEngine-out-0:
          destination: sendToolboxToEngine
          content-type: application/json
          group: sendToolboxToEngine
          binder: rocketmq
        toolBoxReceiveTaskSendResult-in-0:
          destination: toolBoxReceiveTaskSendResult
          content-type: application/json
          group: toolBoxReceiveTaskSendResultGroup
          binder: rocketmq
        receiveTaskExecuteResult-in-0:
          destination: receiveTaskExecuteResult
          content-type: application/json
          group: receiveTaskExecuteResult
          binder: rocketmq
        toolboxReceiveToolsChangeResult-in-0:
          destination: toolboxReceiveToolsChangeResult
          content-type: application/json
          group: toolboxReceiveToolsChangeResult
          binder: rocketmq
        toolboxReceiveToolsChangeResult-out-0:
          destination: toolboxReceiveToolsChangeResult
          content-type: application/json
          group: toolboxReceiveToolsChangeResult
          binder: rocketmq
        toolboxAgentManagementUpdateAgentInfo-in-0:
          destination: agent-management-update-agent
          content-type: application/json
          group: agent-management-update-agent-toolbox-group
          binder: rocketmq
        toolboxSystemSyncComputerList-in-0:
          destination: system-sync-computer-list
          content-type: application/json
          group: system-sync-computer-list-toolbox-group
          binder: rocketmq
        #审计管道
        auditSaveData-out-0:
          destination: audit-save-data
          content-type: application/json
          group: audit-save-data-group
        #其它模块引用脚本服务化脚本情况mq管道
        scriptReferrerInfo-in-0:
          destination: script-referrer-info
          content-type: application/json
          group: script-referrer-info-group
        #基础数据服务部门组织机构层级变更管道
        systemSyncOrganization-in-0:
          destination: system-sync-organization
          content-type: application/json
          group: system-sync-organization-group
messagine-rocketmq:
  namesrvAddr: *************:9876
  consumerGroup: toolBoxReceiveMonitorResultGroup
  topic: toolBoxReceiveMonitorResult
pagehelper:
  reasonable: false
  supportMethodsArguments: true
  params: count=countSql
mybatis:
  mapper-locations:
    - classpath*:mapper/*.xml
logging:
  level:
    com.ideal.script.mapper: debug
    com.ideal.tools.mapper: debug
#    liquibase.executor: debug
#    liquibase.command: debug
#    liquibase.changelog: debug

xxl:
  job:
    admin:
      #xxljob地址
      addresses: http://*************:8899/xxl-job-admin
    executor:
      appname: script-executor
      #脚本服务化xxljob执行器端口
      port: 9421
      logpath: ${user.dir}/logs
      logretentiondays: 30
    accessToken: default_token

ideal:
  communication:
    secret:
      salt: "ideal"
      user-info-key: "user"
      secret-token-key: "secretToken"
  #个性化业务配置，有个性化功能的才需要开启配置，无个性化不要配置，必须按照差异报告配置
#  customer:
#    #客户标识 必须和下面的个性化配置的key一致
#    name: citic
#    #此值是个性化配置客户标识缩写，和上面name的值必须一致，下级的配置为此客户的个性化配置
#    citic:
#      #itsm-repoKey
#      itsm-repo-key: xxxx
#      #itsm-apiKey
#      itsm-api-key: xxxx
#      #itsm获取工单接口url
#      itsm-get-ordernumber-url: http://xxxx.xxx.xxx:1234/itsm/getOrderNumber
#      #itsm上传制品接口url
#      itsm-upload-product-file-url: http://xxxx.xxx.xxx:1234/itsm
#      #itsm下载制品接口
#      itsm-download-product-file-url: http://xxxx.xxx.xxx:1234/itsm/download
#    #此值是个性化配置客户标识缩写，和上面name的值必须一致，下级的配置为此客户的个性化配置
#    psbc:
#      itsm-check-order-url: https://xxxx.xxx.xxx:1234/xxxx
#      itsm-create-order-url: https://xxxx.xxx.xxx:1234/xxxx
#      itsm-authentication-key: qwertyu
#      push-task-message-to-itsm-url: https://xxxx.xxx.xxx:1234/xxxx
#      #是否推送itsm，默认false，配置true才推送
#      enable-push-itsm: true
#      #长期未修改脚本扫描天数配置，默认不配置180天
#      stale-limit: 180
  script:
    #agent-gateway的web服务ip
    agent-gateway-ip: ************
    #agent-gateway的web服务端口
    agent-gateway-port: 9210
    #脚本服务化每个脚本中每个附件大小上限
    each-script-attachment-limit-size: 6MB
    #脚本服务化每个脚本中所有附件大小上限
    total-script-attachment-limit-size: 15MB
    #任务执行agent并发数限制，默认100,最大上限300
    #task-each-num-limit: 100
    #我的脚本筛选权限（creator:按照创建用户，dept:按照部门和分类）
    #    permission-policy: creator
    #脚本服务化任务申请按照周期内创建的任务量排序配置周期（天）,上限为30
    #    script-taskApply-cycle: 30
    #白名单分类列表，配置的分类不校验脚本执行用户(分类的全路径，多级分类之间"/"拼接)
    #    white-category-list:
    #      - 通用/采集
    #      - 一级分类/二级分类
    #    #脚本需要校验的高危执行用户列表（白名单分类不需要校验）
    #    danger-user-list:
    #      - root
    #      - test
    #    #脚本带有高危执行用户发布时可以审批的部门（#分割上下级，要求全路径）
    #    special-review-org-list:
    #      - 研发部#研发四部
    #      - 研发部#开发二部
    #    #脚本带有高危执行用户发布时可以审批的角色
    #    special-review-role-list:
    #      - 脚本服务化角色
    #      - 测试角色
    #查询权限，使用部门-分类(deptCategory)   还是使用   角色-分类(roleCategory)  默认使用部门-分类
    #query-script-permission: roleCategory
    #权限控制，查询权限为角色-部门的时候，控制走角色(userRoleGroup)还是部门（dept） ，默认走部门
    #data-permission-policy: userRoleGroup
    #任务监控、定时任务监控是否只查询自己的数据，true只查询自己的，默认false
    #select-self-data-flag: false
    #我的脚本，脚本创建、编辑是否展示sql类型选项开关，true显示，false不显示，默认false
    #sql-show: false
    #citic特有 发布脚本不需要通过双人复核，直接发布
    #publish-script-without-double-check: false
    #发布脚本都是白名单标识，不需要选择脚本风险等级
    #publish-script-white-flag: false
    #其它模块调用脚本服务化任务，脚本服务化接收到mq消息是否推送给其它mq，true推送，false不推送，默认false
    #monitor-message-send-mq-flag: true
    #itsm-repoKey
    #    itsm-repo-key: xxxx
    #itsm-apiKey
    #    itsm-api-key: xxxx
    #itsm获取工单接口url
    #    itsm-get-ordernumber-url: http://*********:8765/itsm/getOrderNumber
    #itsm上传制品接口url
    #    itsm-upload-product-file-url: http://*********:8765/itsm
    #itsm下载制品接口
  #    itsm-download-product-file-url: http://*********:8765/itsm/download
    #任务申请发送短信改为调用平台管理实现，true为改用平台管理，false为不发送，默认false
    #task-application-message-review-flag: false
    #我的脚本新增、修改是否展示“升级类型”下拉框 true展示，false不展示，默认false
    #script-upgrade-type-flag: false
    #我的脚本，是否需要必填脚本执行用户，默认false，非必填
    #script-user-required: false
  common:
    file-path-validate:
      enable: true
    local-validator-message:
      enable: true
      model: ALL_WITH_CLASS_PATH

#审计日志开关
audit:
  enable: true

common:
  security:
    #true ：web api接口需要认证，传token
    authentication: false
    # @MethodPermission注解是否需要认证
    button:
      #默认true开启按钮权限，false不验证按钮权限
      authentication: false
tools:
  execute:
    check: true
  migrate:
    type: SFTP
  evn: DEV环境
  evens:
    main:
      switch: true
ftp:
  #类型
  type: sftp
  #ip地址
  host: *************
  #端口
  port: 22
  #用户名
  user: ftpusr
  #密码
  pw: ideal
  data:
    path: /home/<USER>/guangda/tools/test/
  #批量导出工具路径配置
  toolsbatch:
    path: /home/<USER>/guangda/toolsbatch/data
    errorPath: /home/<USER>/guangda/toolsbatch/errorData
