package com.ideal.script.subscriber;

import com.ideal.message.center.ISubscriber;
import com.ideal.script.exception.AgentGatewayError;
import com.ideal.system.dto.OrgManagementApiDto;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.function.Consumer;
import java.util.stream.Collectors;


/**
 * 脚本服务化MQ消费者注册配置
 */
@Component
public class ScriptMqRegisterConfig {
    private final Map<String, ISubscriber> subscribers;

    public ScriptMqRegisterConfig(Map<String, ISubscriber> subscribers) {
        this.subscribers = subscribers.entrySet()
                                      .stream()
                                      .filter(entry -> entry.getKey().startsWith("script"))
                                      .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
    }
    /**
     * 监听mq中script-execute-result主题中的消息
     *
     * @return {@link Consumer }<{@link String }>
     * <AUTHOR>
     */
    @Bean
    @ConditionalOnBean(name = "scriptExecuteResultHandler")
    public Consumer<List<String>> scriptExecuteResult() {

        return message -> subscribers.get("scriptExecuteResultHandler").notice(message);
    }

    /**
     * 监听mq中script-error-result主题中的消息
     *
     * @return {@link Consumer }<{@link AgentGatewayError }>
     * <AUTHOR>
     */
    @Bean
    @ConditionalOnBean(name = "scriptErrorResultHandler")
    public Consumer<AgentGatewayError> scriptErrorResult(){
        return message -> subscribers.get("scriptErrorResultHandler").notice(message);
    }

    /**
     * 监听mq中script-send-result主题中的消息
     *
     * @return {@link Consumer }<{@link String }>
     * <AUTHOR>
     */
    @Bean
    @ConditionalOnBean(name = "scriptSendResultHandler")
    public Consumer<String> scriptSendResult() {
        return message -> subscribers.get("scriptSendResultHandler").notice(message);
    }

    /**
     * 监听mq中script-referrer-info主题中的消息(其它模块引用脚本信息)
     *
     * @return {@link Consumer }<{@link String }>
     * <AUTHOR>
     */
    @Bean
    @ConditionalOnBean(name = "scriptReferrerInfoHandler")
    public Consumer<List<Map<String,Object>>> scriptReferrerInfo() {
        return message -> subscribers.get("scriptReferrerInfoHandler").notice(message);
    }

    /**
     * 监听mq中system-sync-organization主题中的消息,基础数据组织机构变更消费更新处理脚本服务化基础信息、分类部门关系表数据
     *
     * @return {@link Consumer }<{@link OrgManagementApiDto }>
     */
    @Bean
    @ConditionalOnBean(name = "scriptSystemSyncOrganizationHandler")
    public Consumer<OrgManagementApiDto> systemSyncOrganization() {
        return message -> subscribers.get("scriptSystemSyncOrganizationHandler").notice(message);
    }
}
