<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.ideal</groupId>
        <artifactId>ieai-script-service</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>scriptTools-spring-cloud-stream-subscriber</artifactId>
    <version>1.0.0</version>
    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.ideal</groupId>
            <artifactId>ideal-message-spring-cloud-stream-publisher</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ideal</groupId>
            <artifactId>system-api</artifactId>
            <scope>provided</scope>
        </dependency>
    </dependencies>
</project>