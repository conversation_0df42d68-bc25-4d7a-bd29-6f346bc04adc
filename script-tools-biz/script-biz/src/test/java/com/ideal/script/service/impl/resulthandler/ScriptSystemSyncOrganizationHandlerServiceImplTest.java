package com.ideal.script.service.impl.resulthandler;

import com.ideal.script.service.ICategoryService;
import com.ideal.script.service.IMyScriptService;
import com.ideal.system.dto.OrgManagementApiDto;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.slf4j.Logger;

import java.util.stream.Stream;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * ScriptSystemSyncOrganizationHandlerServiceImpl 单元测试
 * 
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
class ScriptSystemSyncOrganizationHandlerServiceImplTest {

    @Mock
    private IMyScriptService myScriptService;

    @Mock
    private ICategoryService categoryService;

    @Mock
    private Logger logger;

    @InjectMocks
    private ScriptSystemSyncOrganizationHandlerServiceImpl handlerService;

    private OrgManagementApiDto orgManagementApiDto;

    @BeforeEach
    void setUp() {
        orgManagementApiDto = new OrgManagementApiDto();
    }

    /**
     * 提供参数化测试数据
     */
    static Stream<Object[]> handlerTestParams() {
        return Stream.of(
            // originalCode, code, expectedMyScriptCalls, expectedCategoryCalls, description
            new Object[]{"oldCode", "newCode", 1, 1, "正常情况：原编码和新编码都不为空且不相等"},
            new Object[]{null, "newCode", 0, 0, "原编码为null"},
            new Object[]{"", "newCode", 0, 0, "原编码为空字符串"},
            new Object[]{"   ", "newCode", 0, 0, "原编码为空白字符串"},
            new Object[]{"oldCode", null, 0, 0, "新编码为null"},
            new Object[]{"oldCode", "", 0, 0, "新编码为空字符串"},
            new Object[]{"oldCode", "   ", 0, 0, "新编码为空白字符串"},
            new Object[]{"sameCode", "sameCode", 0, 0, "原编码和新编码相同"},
            new Object[]{null, null, 0, 0, "原编码和新编码都为null"},
            new Object[]{"", "", 0, 0, "原编码和新编码都为空字符串"}
        );
    }

    @ParameterizedTest
    @MethodSource("handlerTestParams")
    @DisplayName("测试handler方法的各种参数组合")
    void handler_test(String originalCode, String code, int expectedMyScriptCalls, 
                     int expectedCategoryCalls, String description) {
        // 准备测试数据
        orgManagementApiDto.setOriginalCode(originalCode);
        orgManagementApiDto.setCode(code);
        
        // 只在需要时模拟服务方法返回值
        if (expectedMyScriptCalls > 0) {
            when(myScriptService.updateScriptInfoByOrgCode(originalCode, code)).thenReturn(5);
        }
        if (expectedCategoryCalls > 0) {
            when(categoryService.updateOrgCodeByOrgCode(originalCode, code)).thenReturn(3);
        }

        // 执行测试方法
        handlerService.handler(orgManagementApiDto);

        // 验证方法调用次数
        verify(myScriptService, times(expectedMyScriptCalls))
            .updateScriptInfoByOrgCode(anyString(), anyString());
        verify(categoryService, times(expectedCategoryCalls))
            .updateOrgCodeByOrgCode(anyString(), anyString());

        // 如果期望有调用，验证具体参数
        if (expectedMyScriptCalls > 0) {
            verify(myScriptService).updateScriptInfoByOrgCode(originalCode, code);
        }
        if (expectedCategoryCalls > 0) {
            verify(categoryService).updateOrgCodeByOrgCode(originalCode, code);
        }
    }

    @Test
    @DisplayName("测试正常更新情况下的日志记录")
    void handler_normalUpdate_logsInfo() {
        // 准备测试数据
        String originalCode = "oldOrgCode";
        String newCode = "newOrgCode";
        orgManagementApiDto.setOriginalCode(originalCode);
        orgManagementApiDto.setCode(newCode);
        
        // 模拟服务方法返回值
        int scriptUpdateCount = 5;
        int categoryUpdateCount = 3;
        when(myScriptService.updateScriptInfoByOrgCode(originalCode, newCode)).thenReturn(scriptUpdateCount);
        when(categoryService.updateOrgCodeByOrgCode(originalCode, newCode)).thenReturn(categoryUpdateCount);

        // 执行测试方法
        handlerService.handler(orgManagementApiDto);

        // 验证服务方法被正确调用
        verify(myScriptService).updateScriptInfoByOrgCode(originalCode, newCode);
        verify(categoryService).updateOrgCodeByOrgCode(originalCode, newCode);
    }

    @Test
    @DisplayName("测试无效参数情况下的警告日志")
    void handler_invalidParams_logsWarning() {
        // 准备测试数据 - 原编码为空的情况
        orgManagementApiDto.setOriginalCode("");
        orgManagementApiDto.setCode("newCode");

        // 执行测试方法
        handlerService.handler(orgManagementApiDto);

        // 验证服务方法未被调用
        verify(myScriptService, never()).updateScriptInfoByOrgCode(anyString(), anyString());
        verify(categoryService, never()).updateOrgCodeByOrgCode(anyString(), anyString());
    }

    @Test
    @DisplayName("测试相同编码情况")
    void handler_sameCode_noUpdate() {
        // 准备测试数据
        String sameCode = "sameOrgCode";
        orgManagementApiDto.setOriginalCode(sameCode);
        orgManagementApiDto.setCode(sameCode);

        // 执行测试方法
        handlerService.handler(orgManagementApiDto);

        // 验证服务方法未被调用
        verify(myScriptService, never()).updateScriptInfoByOrgCode(anyString(), anyString());
        verify(categoryService, never()).updateOrgCodeByOrgCode(anyString(), anyString());
    }

    @Test
    @DisplayName("测试空白字符串处理")
    void handler_blankStrings_noUpdate() {
        // 准备测试数据
        orgManagementApiDto.setOriginalCode("   ");
        orgManagementApiDto.setCode("newCode");

        // 执行测试方法
        handlerService.handler(orgManagementApiDto);

        // 验证服务方法未被调用
        verify(myScriptService, never()).updateScriptInfoByOrgCode(anyString(), anyString());
        verify(categoryService, never()).updateOrgCodeByOrgCode(anyString(), anyString());
    }
}
