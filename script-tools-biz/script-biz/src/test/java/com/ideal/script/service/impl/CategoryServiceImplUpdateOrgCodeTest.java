package com.ideal.script.service.impl;

import com.ideal.script.mapper.CategoryMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * CategoryServiceImpl#updateOrgCodeByOrgCode 方法单元测试
 * 
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
class CategoryServiceImplUpdateOrgCodeTest {

    @Mock
    private CategoryMapper categoryMapper;

    @InjectMocks
    private CategoryServiceImpl categoryService;

    @BeforeEach
    void setUp() {
        // 初始化测试环境
    }

    /**
     * 提供参数化测试数据
     */
    static Stream<Object[]> updateOrgCodeByOrgCodeTestParams() {
        return Stream.of(
            // orgCode, newOrgCode, expectedResult, description
            new Object[]{"oldOrgCode", "newOrgCode", 8, "正常情况：更新成功，返回更新行数"},
            new Object[]{"testOrg", "newTestOrg", 5, "正常情况：更新部分记录"},
            new Object[]{"nonExistentOrg", "newOrg", 0, "边界情况：源组织编码不存在，返回0"},
            new Object[]{"", "newOrgCode", 0, "边界情况：源组织编码为空字符串"},
            new Object[]{"orgCode", "", 3, "边界情况：新组织编码为空字符串"},
            new Object[]{"", "", 0, "边界情况：两个参数都为空字符串"},
            new Object[]{null, "newOrgCode", 0, "边界情况：源组织编码为null"},
            new Object[]{"orgCode", null, 2, "边界情况：新组织编码为null"},
            new Object[]{null, null, 0, "边界情况：两个参数都为null"},
            new Object[]{"   ", "newOrgCode", 0, "边界情况：源组织编码为空白字符串"},
            new Object[]{"orgCode", "   ", 1, "边界情况：新组织编码为空白字符串"},
            new Object[]{"sameOrgCode", "sameOrgCode", 0, "边界情况：相同的组织编码"}
        );
    }

    @ParameterizedTest
    @MethodSource("updateOrgCodeByOrgCodeTestParams")
    @DisplayName("测试updateOrgCodeByOrgCode方法的各种参数组合")
    void updateOrgCodeByOrgCode_test(String orgCode, String newOrgCode, int expectedResult, String description) {
        // 模拟 CategoryMapper 的返回值 - 使用具体参数而不是anyString()
        when(categoryMapper.updateOrgCodeByOrgCode(orgCode, newOrgCode)).thenReturn(expectedResult);

        // 执行测试方法
        int actualResult = categoryService.updateOrgCodeByOrgCode(orgCode, newOrgCode);

        // 验证结果
        assertEquals(expectedResult, actualResult, description);

        // 验证方法调用
        verify(categoryMapper).updateOrgCodeByOrgCode(orgCode, newOrgCode);
    }

    @Test
    @DisplayName("测试updateOrgCodeByOrgCode正常更新情况")
    void updateOrgCodeByOrgCode_normalUpdate_success() {
        // 准备测试数据
        String orgCode = "originalCategoryOrgCode";
        String newOrgCode = "updatedCategoryOrgCode";
        int expectedUpdateCount = 15;

        // 模拟依赖方法返回值
        when(categoryMapper.updateOrgCodeByOrgCode(orgCode, newOrgCode)).thenReturn(expectedUpdateCount);

        // 执行测试方法
        int actualResult = categoryService.updateOrgCodeByOrgCode(orgCode, newOrgCode);

        // 验证结果
        assertEquals(expectedUpdateCount, actualResult);

        // 验证方法调用次数和参数
        verify(categoryMapper, times(1)).updateOrgCodeByOrgCode(orgCode, newOrgCode);
    }

    @Test
    @DisplayName("测试updateOrgCodeByOrgCode无记录更新情况")
    void updateOrgCodeByOrgCode_noRecordsUpdated_returnsZero() {
        // 准备测试数据
        String orgCode = "nonExistentCategoryOrgCode";
        String newOrgCode = "newCategoryOrgCode";

        // 模拟没有记录被更新的情况
        when(categoryMapper.updateOrgCodeByOrgCode(orgCode, newOrgCode)).thenReturn(0);

        // 执行测试方法
        int actualResult = categoryService.updateOrgCodeByOrgCode(orgCode, newOrgCode);

        // 验证结果
        assertEquals(0, actualResult);

        // 验证方法调用
        verify(categoryMapper).updateOrgCodeByOrgCode(orgCode, newOrgCode);
    }

    @Test
    @DisplayName("测试updateOrgCodeByOrgCode大量记录更新情况")
    void updateOrgCodeByOrgCode_massUpdate_success() {
        // 准备测试数据
        String orgCode = "commonCategoryOrgCode";
        String newOrgCode = "newCommonCategoryOrgCode";
        int expectedUpdateCount = 500; // 模拟大量记录更新

        // 模拟大量记录更新的情况
        when(categoryMapper.updateOrgCodeByOrgCode(orgCode, newOrgCode)).thenReturn(expectedUpdateCount);

        // 执行测试方法
        int actualResult = categoryService.updateOrgCodeByOrgCode(orgCode, newOrgCode);

        // 验证结果
        assertEquals(expectedUpdateCount, actualResult);

        // 验证方法调用
        verify(categoryMapper).updateOrgCodeByOrgCode(orgCode, newOrgCode);
    }

    @Test
    @DisplayName("测试updateOrgCodeByOrgCode相同组织编码情况")
    void updateOrgCodeByOrgCode_sameOrgCode_stillExecutes() {
        // 准备测试数据
        String sameOrgCode = "sameCategoryOrgCode";
        int expectedUpdateCount = 0; // 相同编码通常不会更新任何记录

        // 模拟相同组织编码的情况
        when(categoryMapper.updateOrgCodeByOrgCode(sameOrgCode, sameOrgCode)).thenReturn(expectedUpdateCount);

        // 执行测试方法
        int actualResult = categoryService.updateOrgCodeByOrgCode(sameOrgCode, sameOrgCode);

        // 验证结果
        assertEquals(expectedUpdateCount, actualResult);

        // 验证方法调用（即使参数相同，方法仍应被调用）
        verify(categoryMapper).updateOrgCodeByOrgCode(sameOrgCode, sameOrgCode);
    }

    @Test
    @DisplayName("测试updateOrgCodeByOrgCode事务性验证")
    void updateOrgCodeByOrgCode_transactional_verification() {
        // 准备测试数据
        String orgCode = "transactionTestCategoryOrg";
        String newOrgCode = "newTransactionTestCategoryOrg";
        int expectedUpdateCount = 7;

        // 模拟正常执行
        when(categoryMapper.updateOrgCodeByOrgCode(orgCode, newOrgCode)).thenReturn(expectedUpdateCount);

        // 执行测试方法
        int actualResult = categoryService.updateOrgCodeByOrgCode(orgCode, newOrgCode);

        // 验证结果
        assertEquals(expectedUpdateCount, actualResult);

        // 验证方法调用顺序和次数
        verify(categoryMapper, times(1)).updateOrgCodeByOrgCode(orgCode, newOrgCode);
        
        // 验证没有其他不必要的方法调用
        verifyNoMoreInteractions(categoryMapper);
    }

    @Test
    @DisplayName("测试updateOrgCodeByOrgCode特殊字符处理")
    void updateOrgCodeByOrgCode_specialCharacters_handled() {
        // 准备测试数据 - 包含特殊字符的组织编码
        String orgCodeWithSpecialChars = "category-org_123.test";
        String newOrgCodeWithSpecialChars = "new-category-org_456.updated";
        int expectedUpdateCount = 4;

        // 模拟特殊字符处理
        when(categoryMapper.updateOrgCodeByOrgCode(orgCodeWithSpecialChars, newOrgCodeWithSpecialChars))
            .thenReturn(expectedUpdateCount);

        // 执行测试方法
        int actualResult = categoryService.updateOrgCodeByOrgCode(orgCodeWithSpecialChars, newOrgCodeWithSpecialChars);

        // 验证结果
        assertEquals(expectedUpdateCount, actualResult);

        // 验证方法调用
        verify(categoryMapper).updateOrgCodeByOrgCode(orgCodeWithSpecialChars, newOrgCodeWithSpecialChars);
    }

    @Test
    @DisplayName("测试updateOrgCodeByOrgCode长字符串处理")
    void updateOrgCodeByOrgCode_longStrings_handled() {
        // 准备测试数据 - 长字符串
        String longOrgCode = "very.long.organization.code.with.many.segments.for.testing.purposes.category";
        String longNewOrgCode = "another.very.long.organization.code.with.many.segments.for.testing.purposes.new.category";
        int expectedUpdateCount = 3;

        // 模拟长字符串处理
        when(categoryMapper.updateOrgCodeByOrgCode(longOrgCode, longNewOrgCode))
            .thenReturn(expectedUpdateCount);

        // 执行测试方法
        int actualResult = categoryService.updateOrgCodeByOrgCode(longOrgCode, longNewOrgCode);

        // 验证结果
        assertEquals(expectedUpdateCount, actualResult);

        // 验证方法调用
        verify(categoryMapper).updateOrgCodeByOrgCode(longOrgCode, longNewOrgCode);
    }

    @Test
    @DisplayName("测试updateOrgCodeByOrgCode数字组织编码处理")
    void updateOrgCodeByOrgCode_numericOrgCodes_handled() {
        // 准备测试数据 - 纯数字组织编码
        String numericOrgCode = "123456789";
        String newNumericOrgCode = "987654321";
        int expectedUpdateCount = 6;

        // 模拟数字组织编码处理
        when(categoryMapper.updateOrgCodeByOrgCode(numericOrgCode, newNumericOrgCode))
            .thenReturn(expectedUpdateCount);

        // 执行测试方法
        int actualResult = categoryService.updateOrgCodeByOrgCode(numericOrgCode, newNumericOrgCode);

        // 验证结果
        assertEquals(expectedUpdateCount, actualResult);

        // 验证方法调用
        verify(categoryMapper).updateOrgCodeByOrgCode(numericOrgCode, newNumericOrgCode);
    }

    @Test
    @DisplayName("测试updateOrgCodeByOrgCode中文字符处理")
    void updateOrgCodeByOrgCode_chineseCharacters_handled() {
        // 准备测试数据 - 包含中文字符的组织编码
        String chineseOrgCode = "组织编码测试";
        String newChineseOrgCode = "新组织编码测试";
        int expectedUpdateCount = 2;

        // 模拟中文字符处理
        when(categoryMapper.updateOrgCodeByOrgCode(chineseOrgCode, newChineseOrgCode))
            .thenReturn(expectedUpdateCount);

        // 执行测试方法
        int actualResult = categoryService.updateOrgCodeByOrgCode(chineseOrgCode, newChineseOrgCode);

        // 验证结果
        assertEquals(expectedUpdateCount, actualResult);

        // 验证方法调用
        verify(categoryMapper).updateOrgCodeByOrgCode(chineseOrgCode, newChineseOrgCode);
    }
}
