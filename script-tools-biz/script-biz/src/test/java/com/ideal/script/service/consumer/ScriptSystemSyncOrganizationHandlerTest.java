package com.ideal.script.service.consumer;

import com.ideal.script.service.resulthandler.IScriptSystemSyncOrganizationHandlerService;
import com.ideal.system.dto.OrgManagementApiDto;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.slf4j.Logger;

import java.util.stream.Stream;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * ScriptSystemSyncOrganizationHandler 单元测试
 * 
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
class ScriptSystemSyncOrganizationHandlerTest {

    @Mock
    private IScriptSystemSyncOrganizationHandlerService iScriptSystemSyncOrganizationHandlerService;

    @Mock
    private Logger logger;

    @InjectMocks
    private ScriptSystemSyncOrganizationHandler handler;

    private OrgManagementApiDto orgManagementApiDto;

    @BeforeEach
    void setUp() {
        orgManagementApiDto = new OrgManagementApiDto();
        orgManagementApiDto.setCode("testCode");
        orgManagementApiDto.setOperateType("update");
        orgManagementApiDto.setOriginalCode("originalCode");
    }

    /**
     * 提供参数化测试数据
     */
    static Stream<Object[]> noticeTestParams() {
        return Stream.of(
            // message, operateType, expectedHandlerCalls, description
            new Object[]{createOrgDto("testCode", "update", "originalCode"), 1, "正常update操作"},
            new Object[]{createOrgDto("testCode", "create", "originalCode"), 0, "create操作不处理"},
            new Object[]{createOrgDto("testCode", "delete", "originalCode"), 0, "delete操作不处理"},
            new Object[]{createOrgDto("testCode", null, "originalCode"), 0, "operateType为null"},
            new Object[]{createOrgDto("testCode", "", "originalCode"), 0, "operateType为空字符串"},
            new Object[]{createOrgDto("testCode", "UPDATE", "originalCode"), 0, "operateType大写不匹配"},
            new Object[]{createOrgDto("testCode", "modify", "originalCode"), 0, "operateType为modify不匹配"},
            new Object[]{"非OrgManagementApiDto对象", 0, "非OrgManagementApiDto类型的消息"},
            new Object[]{null, 0, "消息为null"}
        );
    }

    /**
     * 创建OrgManagementApiDto对象的辅助方法
     */
    private static OrgManagementApiDto createOrgDto(String code, String operateType, String originalCode) {
        OrgManagementApiDto dto = new OrgManagementApiDto();
        dto.setCode(code);
        dto.setOperateType(operateType);
        dto.setOriginalCode(originalCode);
        return dto;
    }

    @ParameterizedTest
    @MethodSource("noticeTestParams")
    @DisplayName("测试notice方法的各种消息类型和操作类型")
    void notice_test(Object message, int expectedHandlerCalls, String description) {
        // 执行测试方法
        handler.notice(message);

        // 验证处理器方法调用次数
        verify(iScriptSystemSyncOrganizationHandlerService, times(expectedHandlerCalls))
            .handler(any(OrgManagementApiDto.class));

        // 如果期望有调用，验证具体参数
        if (expectedHandlerCalls > 0 && message instanceof OrgManagementApiDto) {
            verify(iScriptSystemSyncOrganizationHandlerService).handler((OrgManagementApiDto) message);
        }
    }

    @Test
    @DisplayName("测试正常update操作的处理流程")
    void notice_updateOperation_callsHandler() {
        // 准备测试数据
        OrgManagementApiDto dto = new OrgManagementApiDto();
        dto.setCode("newCode");
        dto.setOperateType("update");
        dto.setOriginalCode("oldCode");

        // 执行测试方法
        handler.notice(dto);

        // 验证处理器被正确调用
        verify(iScriptSystemSyncOrganizationHandlerService).handler(dto);
    }

    @Test
    @DisplayName("测试非update操作不调用处理器")
    void notice_nonUpdateOperation_doesNotCallHandler() {
        // 准备测试数据
        OrgManagementApiDto dto = new OrgManagementApiDto();
        dto.setCode("testCode");
        dto.setOperateType("create");
        dto.setOriginalCode("originalCode");

        // 执行测试方法
        handler.notice(dto);

        // 验证处理器未被调用
        verify(iScriptSystemSyncOrganizationHandlerService, never()).handler(any(OrgManagementApiDto.class));
    }

    @Test
    @DisplayName("测试非OrgManagementApiDto类型消息的错误处理")
    void notice_invalidMessageType_logsError() {
        // 准备测试数据
        String invalidMessage = "这是一个字符串消息";

        // 执行测试方法
        handler.notice(invalidMessage);

        // 验证处理器未被调用
        verify(iScriptSystemSyncOrganizationHandlerService, never()).handler(any(OrgManagementApiDto.class));
    }

    @Test
    @DisplayName("测试null消息的处理")
    void notice_nullMessage_logsError() {
        // 执行测试方法
        handler.notice(null);

        // 验证处理器未被调用
        verify(iScriptSystemSyncOrganizationHandlerService, never()).handler(any(OrgManagementApiDto.class));
    }

    @Test
    @DisplayName("测试operateType为null的情况")
    void notice_nullOperateType_doesNotCallHandler() {
        // 准备测试数据
        OrgManagementApiDto dto = new OrgManagementApiDto();
        dto.setCode("testCode");
        dto.setOperateType(null);
        dto.setOriginalCode("originalCode");

        // 执行测试方法
        handler.notice(dto);

        // 验证处理器未被调用
        verify(iScriptSystemSyncOrganizationHandlerService, never()).handler(any(OrgManagementApiDto.class));
    }

    @Test
    @DisplayName("测试operateType为空字符串的情况")
    void notice_emptyOperateType_doesNotCallHandler() {
        // 准备测试数据
        OrgManagementApiDto dto = new OrgManagementApiDto();
        dto.setCode("testCode");
        dto.setOperateType("");
        dto.setOriginalCode("originalCode");

        // 执行测试方法
        handler.notice(dto);

        // 验证处理器未被调用
        verify(iScriptSystemSyncOrganizationHandlerService, never()).handler(any(OrgManagementApiDto.class));
    }

    @Test
    @DisplayName("测试operateType大小写敏感")
    void notice_operateTypeCaseSensitive_doesNotCallHandler() {
        // 准备测试数据
        OrgManagementApiDto dto = new OrgManagementApiDto();
        dto.setCode("testCode");
        dto.setOperateType("UPDATE"); // 大写
        dto.setOriginalCode("originalCode");

        // 执行测试方法
        handler.notice(dto);

        // 验证处理器未被调用（因为代码中使用equals比较，大小写敏感）
        verify(iScriptSystemSyncOrganizationHandlerService, never()).handler(any(OrgManagementApiDto.class));
    }

    @Test
    @DisplayName("测试完整的正常流程包含日志记录")
    void notice_completeNormalFlow_withLogging() {
        // 准备测试数据
        OrgManagementApiDto dto = new OrgManagementApiDto();
        dto.setCode("newOrgCode");
        dto.setOperateType("update");
        dto.setOriginalCode("oldOrgCode");

        // 执行测试方法
        handler.notice(dto);

        // 验证处理器被正确调用
        verify(iScriptSystemSyncOrganizationHandlerService).handler(dto);
    }
}
