package com.ideal.script.service.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import com.ideal.approval.api.IDoubleCheckApiService;
import com.ideal.approval.dto.DoubleCheckApiDto;
import com.ideal.approval.dto.ResultApiDto;
import com.ideal.common.util.spring.SpringUtil;
import com.ideal.message.center.IPublisher;
import com.ideal.message.center.exception.CommunicationException;
import com.ideal.notification.api.IWarn;
import com.ideal.sc.CustomerProperty;
import com.ideal.sc.util.CurrentUserUtil;
import com.ideal.script.common.constant.Constants;
import com.ideal.script.common.constant.enums.ScriptSource;
import com.ideal.script.common.util.BatchDataUtil;
import com.ideal.script.config.CibProperties;
import com.ideal.script.config.ScriptBusinessConfig;
import com.ideal.script.dto.AttachmentResponseDto;
import com.ideal.script.dto.AttachmentUploadDto;
import com.ideal.script.dto.BindFuncVarDto;
import com.ideal.script.dto.CategoryApiDto;
import com.ideal.script.dto.CurrentUserDto;
import com.ideal.script.dto.ParameterValidationDto;
import com.ideal.script.dto.PublishDto;
import com.ideal.script.dto.ScriptContentDto;
import com.ideal.script.dto.ScriptDubboInfoDto;
import com.ideal.script.dto.ScriptInfoApiDto;
import com.ideal.script.dto.ScriptInfoDto;
import com.ideal.script.dto.ScriptInfoQueryDto;
import com.ideal.script.dto.ScriptVersionDto;
import com.ideal.script.exception.ScriptException;
import com.ideal.script.exception.SystemException;
import com.ideal.script.mapper.AttachmentEphemeralMapper;
import com.ideal.script.mapper.AttachmentMapper;
import com.ideal.script.mapper.AuditRelationMapper;
import com.ideal.script.mapper.BindFuncVarMapper;
import com.ideal.script.mapper.CategoryMapper;
import com.ideal.script.mapper.DangerCmdMapper;
import com.ideal.script.mapper.InfoMapper;
import com.ideal.script.mapper.InfoVersionMapper;
import com.ideal.script.mapper.InfoVersionTextMapper;
import com.ideal.script.mapper.MyScriptMapper;
import com.ideal.script.mapper.ParameterMapper;
import com.ideal.script.model.bean.AuditResultBean;
import com.ideal.script.model.bean.CategoryOrgBean;
import com.ideal.script.model.bean.CategoryRoleBean;
import com.ideal.script.model.bean.DownloadScriptBean;
import com.ideal.script.model.bean.ExecutorValidation;
import com.ideal.script.model.bean.MyScriptBean;
import com.ideal.script.model.bean.OrgBean;
import com.ideal.script.model.bean.ScriptInfoQueryBean;
import com.ideal.script.model.bean.ScriptVersionInfoBean;
import com.ideal.script.model.dto.CreatorTransferDto;
import com.ideal.script.model.dto.DoubleCheckInfoDto;
import com.ideal.script.model.dto.ParameterValidationResultDto;
import com.ideal.script.model.dto.ScriptDeleteDto;
import com.ideal.script.model.dto.ScriptValidationResultDto;
import com.ideal.script.model.dto.ScriptVersionInfoDto;
import com.ideal.script.model.dto.StatementDto;
import com.ideal.script.model.dto.ValidationResultDto;
import com.ideal.script.model.entity.Attachment;
import com.ideal.script.model.entity.AuditRelation;
import com.ideal.script.model.entity.BindFuncVar;
import com.ideal.script.model.entity.Category;
import com.ideal.script.model.entity.DangerCmd;
import com.ideal.script.model.entity.Info;
import com.ideal.script.model.entity.InfoVersion;
import com.ideal.script.model.entity.InfoVersionText;
import com.ideal.script.model.entity.Parameter;
import com.ideal.script.remotecall.RemoteCall;
import com.ideal.script.service.IAfterScriptExecAuditing;
import com.ideal.script.service.IReleaseMediaService;
import com.ideal.script.service.IScriptStatementService;
import com.ideal.script.service.ITaskAuthorityService;
import com.ideal.script.service.impl.builders.MyScriptServiceScripts;
import com.ideal.script.service.impl.builders.MyScriptServiceScriptsBuilder;
import com.ideal.system.api.IRole;
import com.ideal.system.api.IUserInfo;
import com.ideal.system.common.component.model.CurrentUser;
import com.ideal.system.dto.RoleApiDto;
import com.ideal.system.dto.UserInfoApiDto;
import com.ideal.system.dto.UserInfoQueryDto;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.session.SqlSessionFactory;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.MethodSource;
import org.junit.jupiter.params.provider.NullSource;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.redisson.RedissonLock;
import org.redisson.api.RScoredSortedSet;
import org.redisson.api.RScript;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.config.ConfigurableListableBeanFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.lang.Nullable;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.util.unit.DataSize;

import java.io.File;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.nio.charset.StandardCharsets;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Stream;

import static org.apache.commons.lang3.time.DateFormatUtils.format;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.api.Assertions.fail;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.anyList;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.atLeastOnce;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class MyScriptServiceImplTest {

    private static MockedStatic<SpringUtil> springUtilMockedStatic;
    private static MockedStatic<CurrentUserUtil> currentUserUtilMockedStatic;
    @Mock
    private InfoMapper infoMapper;
    @Mock
    private InfoVersionMapper infoVersionMapper;
    @Mock
    private ParameterServiceImpl parameterService;
    @Mock
    private ParameterMapper parameterMapper;
    @Mock
    private BindFuncVarMapper bindFuncVarMapper;
    @Mock
    private InfoVersionTextServiceImpl infoVersionTextService;
    @Mock
    private AttachmentServiceImpl attachmentService;
    @Mock
    private BindFuncVarServiceImpl bindFuncVarService;
    @Mock
    private CategoryServiceImpl categoryService;
    @Mock
    private MyScriptMapper myScriptMapper;
    @Mock
    private InfoVersionTextMapper infoVersionTextMapper;
    @Mock
    private AttachmentMapper attachmentMapper;
    @Mock
    private MyScriptServiceScripts scripts;
    @Mock
    private MyScriptServiceScriptsBuilder mockBuilder;
    @Mock
    private AttachmentEphemeralMapper attachmentEphemeralMapper;
    @Mock
    private MyScriptServiceImpl myScriptServiceImplUnderTest;
    @Mock
    private SqlSessionFactory factory;
    @Mock
    private BatchDataUtil batchDataUtil;
    @Mock
    private RemoteCall remoteCall;
    @Mock
    private AuditRelationServiceImpl auditRelationService;
    @Mock
    private AuditRelationMapper auditRelationMapper;
    @Mock
    private DangerCmdMapper dangerCmdMapper;
    @Mock
    private RedissonClient mockRedissonClient;
    @Mock
    private ScriptBusinessConfig scriptBusinessConfig;
    @Mock
    private IUserInfo iUserInfo;
    @Mock
    private CategoryMapper categoryMapper;
    @Mock
    private InfoVersionServiceImpl infoVersionService;
    @Mock
    private IScriptStatementService scriptStatementService;
    @Mock
    private IDoubleCheckApiService doubleCheckApiService;
    @Mock
    private IRole iRole;
    @Mock
    private IReleaseMediaService releaseMediaService;
    @Mock
    private ITaskAuthorityService taskAuthorityService;
    @Mock
    private IAfterScriptExecAuditing afterScriptExecAuditing;
    @Mock
    private CibProperties cibProperties;

    @BeforeAll
    static void setUpStaticMethod() {
        CustomerProperty customerProperty = new CustomerProperty();
        customerProperty.setName("psbc");
        // 在所有测试方法执行前，Mock 静态方法
        springUtilMockedStatic = Mockito.mockStatic(SpringUtil.class);
        currentUserUtilMockedStatic = Mockito.mockStatic(CurrentUserUtil.class);
        springUtilMockedStatic.when(() -> SpringUtil.getBean(CustomerProperty.class)).thenReturn(customerProperty);
        CurrentUser currentUser = new CurrentUser();
        currentUser.setId(1L);
        currentUser.setLoginName("test");
        currentUser.setFullName("测试用户");
        currentUser.setOrgId(1L);
        currentUser.setOrgCode("orgCode1#");
        currentUser.setSupervisor(false); // 设置非管理员权限
        currentUserUtilMockedStatic.when(CurrentUserUtil::getCurrentUser).thenReturn(currentUser);
    }

    @AfterAll
    static void afterAll() {
        springUtilMockedStatic.close();
        currentUserUtilMockedStatic.close();
    }

    @BeforeEach
    void setUp() throws NoSuchFieldException, IllegalAccessException {
        // 使用反射将infoMapper设置到mockBuilder中
        Field scriptStatementServiceField = MyScriptServiceScriptsBuilder.class.getDeclaredField("scriptStatementService");
        scriptStatementServiceField.setAccessible(true);
        scriptStatementServiceField.set(mockBuilder, scriptStatementService);

        Field infoMapperField = MyScriptServiceScriptsBuilder.class.getDeclaredField("infoMapper");
        infoMapperField.setAccessible(true);
        infoMapperField.set(mockBuilder, infoMapper);

        Field infoVersionMapperField = MyScriptServiceScriptsBuilder.class.getDeclaredField("infoVersionMapper");
        infoVersionMapperField.setAccessible(true);
        infoVersionMapperField.set(mockBuilder, infoVersionMapper);

        Field parameterServiceField = MyScriptServiceScriptsBuilder.class.getDeclaredField("parameterService");
        parameterServiceField.setAccessible(true);
        parameterServiceField.set(mockBuilder, parameterService);

        Field parameterMapperField = MyScriptServiceScriptsBuilder.class.getDeclaredField("parameterMapper");
        parameterMapperField.setAccessible(true);
        parameterMapperField.set(mockBuilder, parameterMapper);

        Field bindFuncVarMapperField = MyScriptServiceScriptsBuilder.class.getDeclaredField("bindFuncVarMapper");
        bindFuncVarMapperField.setAccessible(true);
        bindFuncVarMapperField.set(mockBuilder, bindFuncVarMapper);

        Field infoVersionTextServiceField = MyScriptServiceScriptsBuilder.class.getDeclaredField("infoVersionTextService");
        infoVersionTextServiceField.setAccessible(true);
        infoVersionTextServiceField.set(mockBuilder, infoVersionTextService);

        Field attachmentServiceField = MyScriptServiceScriptsBuilder.class.getDeclaredField("attachmentService");
        attachmentServiceField.setAccessible(true);
        attachmentServiceField.set(mockBuilder, attachmentService);

        Field bindFuncVarServiceField = MyScriptServiceScriptsBuilder.class.getDeclaredField("bindFuncVarService");
        bindFuncVarServiceField.setAccessible(true);
        bindFuncVarServiceField.set(mockBuilder, bindFuncVarService);

        Field categoryServiceField = MyScriptServiceScriptsBuilder.class.getDeclaredField("categoryService");
        categoryServiceField.setAccessible(true);
        categoryServiceField.set(mockBuilder, categoryService);

        Field myScriptMapperField = MyScriptServiceScriptsBuilder.class.getDeclaredField("myScriptMapper");
        myScriptMapperField.setAccessible(true);
        myScriptMapperField.set(mockBuilder, myScriptMapper);

        Field infoVersionTextMapperField = MyScriptServiceScriptsBuilder.class.getDeclaredField("infoVersionTextMapper");
        infoVersionTextMapperField.setAccessible(true);
        infoVersionTextMapperField.set(mockBuilder, infoVersionTextMapper);

        Field attachmentMapperField = MyScriptServiceScriptsBuilder.class.getDeclaredField("attachmentMapper");
        attachmentMapperField.setAccessible(true);
        attachmentMapperField.set(mockBuilder, attachmentMapper);

        Field factoryField = MyScriptServiceScriptsBuilder.class.getDeclaredField("factory");
        factoryField.setAccessible(true);
        factoryField.set(mockBuilder, factory);

        Field batchDataUtilField = MyScriptServiceScriptsBuilder.class.getDeclaredField("batchDataUtil");
        batchDataUtilField.setAccessible(true);
        batchDataUtilField.set(mockBuilder, batchDataUtil);

        Field remoteCallField = MyScriptServiceScriptsBuilder.class.getDeclaredField("remoteCall");
        remoteCallField.setAccessible(true);
        remoteCallField.set(mockBuilder, remoteCall);

        Field RedisClientField = MyScriptServiceScriptsBuilder.class.getDeclaredField("redissonClient");
        RedisClientField.setAccessible(true);
        RedisClientField.set(mockBuilder, mockRedissonClient);

        Field scriptBusinessConfigField = MyScriptServiceScriptsBuilder.class.getDeclaredField("scriptBusinessConfig");
        scriptBusinessConfigField.setAccessible(true);
        scriptBusinessConfigField.set(mockBuilder, scriptBusinessConfig);


        Field iUserInfoField = MyScriptServiceScriptsBuilder.class.getDeclaredField("iUserInfo");
        iUserInfoField.setAccessible(true);
        iUserInfoField.set(mockBuilder, iUserInfo);


        Field categoryMapperrField = MyScriptServiceScriptsBuilder.class.getDeclaredField("categoryMapper");
        categoryMapperrField.setAccessible(true);
        categoryMapperrField.set(mockBuilder, categoryMapper);


        Field roleField = MyScriptServiceScriptsBuilder.class.getDeclaredField("iRole");
        roleField.setAccessible(true);
        roleField.set(mockBuilder, iRole);

        Field infoVersionServiceField = MyScriptServiceScriptsBuilder.class.getDeclaredField("infoVersionService");
        infoVersionServiceField.setAccessible(true);
        infoVersionServiceField.set(mockBuilder, infoVersionService);


        //scripts
        Field infoMapperField1 = MyScriptServiceScripts.class.getDeclaredField("infoMapper");
        infoMapperField1.setAccessible(true);
        infoMapperField1.set(scripts, infoMapper);

        Field infoVersionMapperField1 = MyScriptServiceScripts.class.getDeclaredField("infoVersionMapper");
        infoVersionMapperField1.setAccessible(true);
        infoVersionMapperField1.set(scripts, infoVersionMapper);

        Field parameterServiceField1 = MyScriptServiceScripts.class.getDeclaredField("parameterService");
        parameterServiceField1.setAccessible(true);
        parameterServiceField1.set(scripts, parameterService);

        Field parameterMapperField1 = MyScriptServiceScripts.class.getDeclaredField("parameterMapper");
        parameterMapperField1.setAccessible(true);
        parameterMapperField1.set(scripts, parameterMapper);

        Field bindFuncVarMapperField1 = MyScriptServiceScripts.class.getDeclaredField("bindFuncVarMapper");
        bindFuncVarMapperField1.setAccessible(true);
        bindFuncVarMapperField1.set(scripts, bindFuncVarMapper);

        Field infoVersionTextServiceField1 = MyScriptServiceScripts.class.getDeclaredField("infoVersionTextService");
        infoVersionTextServiceField1.setAccessible(true);
        infoVersionTextServiceField1.set(scripts, infoVersionTextService);

        Field attachmentServiceField1 = MyScriptServiceScripts.class.getDeclaredField("attachmentService");
        attachmentServiceField1.setAccessible(true);
        attachmentServiceField1.set(scripts, attachmentService);

        Field bindFuncVarServiceField1 = MyScriptServiceScripts.class.getDeclaredField("bindFuncVarService");
        bindFuncVarServiceField1.setAccessible(true);
        bindFuncVarServiceField1.set(scripts, bindFuncVarService);


        Field categoryServiceField1 = MyScriptServiceScripts.class.getDeclaredField("categoryService");
        categoryServiceField1.setAccessible(true);
        categoryServiceField1.set(scripts, categoryService);

        Field myScriptMapperField1 = MyScriptServiceScripts.class.getDeclaredField("myScriptMapper");
        myScriptMapperField1.setAccessible(true);
        myScriptMapperField1.set(scripts, myScriptMapper);

        Field infoVersionTextMapperField1 = MyScriptServiceScripts.class.getDeclaredField("infoVersionTextMapper");
        infoVersionTextMapperField1.setAccessible(true);
        infoVersionTextMapperField1.set(scripts, infoVersionTextMapper);

        Field attachmentMapperField1 = MyScriptServiceScripts.class.getDeclaredField("attachmentMapper");
        attachmentMapperField1.setAccessible(true);
        attachmentMapperField1.set(scripts, attachmentMapper);

        Field factoryField1 = MyScriptServiceScripts.class.getDeclaredField("factory");
        factoryField1.setAccessible(true);
        factoryField1.set(scripts, factory);

        Field batchDataUtilField1 = MyScriptServiceScripts.class.getDeclaredField("batchDataUtil");
        batchDataUtilField1.setAccessible(true);
        batchDataUtilField1.set(scripts, batchDataUtil);

        Field remoteCallField1 = MyScriptServiceScripts.class.getDeclaredField("remoteCall");
        remoteCallField1.setAccessible(true);
        remoteCallField1.set(scripts, remoteCall);


        Field RedisClientField1 = MyScriptServiceScripts.class.getDeclaredField("redissonClient");
        RedisClientField1.setAccessible(true);
        RedisClientField1.set(scripts, mockRedissonClient);

        Field scriptBusinessConfigField1 = MyScriptServiceScripts.class.getDeclaredField("scriptBusinessConfig");
        scriptBusinessConfigField1.setAccessible(true);
        scriptBusinessConfigField1.set(scripts, scriptBusinessConfig);

        Field iUserInfoField1 = MyScriptServiceScripts.class.getDeclaredField("iUserInfo");
        iUserInfoField1.setAccessible(true);
        iUserInfoField1.set(scripts, iUserInfo);

        Field categoryMapperField1 = MyScriptServiceScripts.class.getDeclaredField("categoryMapper");
        categoryMapperField1.setAccessible(true);
        categoryMapperField1.set(scripts, categoryMapper);

        Field infoVersionServiceField1 = MyScriptServiceScripts.class.getDeclaredField("infoVersionService");
        infoVersionServiceField1.setAccessible(true);
        infoVersionServiceField1.set(scripts, infoVersionService);

        Field scriptStatementServiceField1 = MyScriptServiceScripts.class.getDeclaredField("scriptStatementService");
        scriptStatementServiceField1.setAccessible(true);
        scriptStatementServiceField1.set(scripts, scriptStatementService);

        Field roleField1 = MyScriptServiceScripts.class.getDeclaredField("iRole");
        roleField1.setAccessible(true);
        roleField1.set(scripts, iRole);

        when(mockBuilder.build()).thenReturn(scripts);
        myScriptServiceImplUnderTest = new MyScriptServiceImpl(doubleCheckApiService, attachmentEphemeralMapper, mockBuilder,releaseMediaService,taskAuthorityService,cibProperties,afterScriptExecAuditing);


    }

    @Test
    void testSaveScript_existShield() throws Exception {
        // Setup
        final ScriptInfoDto scriptInfoDto = new ScriptInfoDto();
        final ScriptVersionDto scriptVersionDto = new ScriptVersionDto();

        final ParameterValidationDto parameterValidationDto = new ParameterValidationDto();
        parameterValidationDto.setParamDefaultValue("hhh");
        parameterValidationDto.setCheckRule("^[A-Za-z]+$");

        List<ParameterValidationDto> parameterValidationDtoList = new ArrayList<>();
        parameterValidationDtoList.add(parameterValidationDto);
        scriptVersionDto.setParameterValidationDtoList(parameterValidationDtoList);

        final ScriptContentDto scriptContentDto = new ScriptContentDto();
        scriptContentDto.setContent("echo 0\\n这是一个关键命令\\nrm");
        scriptVersionDto.setScriptContentDto(scriptContentDto);

        scriptInfoDto.setScriptVersionDto(scriptVersionDto);
        scriptInfoDto.setCategoryId(3L);


        List<DangerCmd> dangerCmds = new ArrayList<>();
        DangerCmd dangerCmd1 = new DangerCmd();
        dangerCmd1.setScriptCmd("这是一个关键命令");
        dangerCmd1.setScriptCmdLevel(0);
        dangerCmds.add(dangerCmd1);
        DangerCmd dangerCmd2 = new DangerCmd();
        dangerCmd2.setScriptCmd("rm");
        dangerCmd2.setScriptCmdLevel(1);
        dangerCmds.add(dangerCmd2);

        RedissonLock mockLock = Mockito.mock(RedissonLock.class);

        when(scripts.getDangerCmdMapper()).thenReturn(dangerCmdMapper);
        when(scripts.getParameterService()).thenReturn(parameterService);
        when(scripts.getRedissonClient()).thenReturn(mockRedissonClient);
        when(scripts.getInfoMapper()).thenReturn(infoMapper);

        when(scripts.getDangerCmdMapper().selectDangerCmdList(any(DangerCmd.class))).thenReturn(dangerCmds);
        when(scripts.getRedissonClient().getLock(anyString())).thenReturn(mockLock);
        when(scripts.getCategoryMapper()).thenReturn(categoryMapper);
        when(scripts.getScriptBusinessConfig()).thenReturn(scriptBusinessConfig);

        final ValidationResultDto result = myScriptServiceImplUnderTest.saveScript(scriptInfoDto);
        assertNotNull(result);
    }

    @Test
    void testSaveScript_existParameterCheck() throws Exception {
        // Setup
        final ScriptInfoDto scriptInfoDto = new ScriptInfoDto();
        final ScriptVersionDto scriptVersionDto = new ScriptVersionDto();

        final ParameterValidationDto parameterValidationDto = new ParameterValidationDto();
        parameterValidationDto.setParamDefaultValue("hhh");
        parameterValidationDto.setCheckRule("^[A-Za-z]+$");
        List<ParameterValidationDto> parameterValidationDtoList = new ArrayList<>();
        parameterValidationDtoList.add(parameterValidationDto);
        scriptVersionDto.setParameterValidationDtoList(parameterValidationDtoList);

        final ScriptContentDto scriptContentDto = new ScriptContentDto();
        scriptContentDto.setContent("echo 0\\n这是一个关键命令\\nrm");
        scriptVersionDto.setScriptContentDto(scriptContentDto);
        scriptInfoDto.setScriptVersionDto(scriptVersionDto);
        scriptInfoDto.setCategoryId(3L);

        List<DangerCmd> dangerCmds = new ArrayList<>();
        DangerCmd dangerCmd1 = new DangerCmd();
        dangerCmd1.setScriptCmd("这是一个关键命令");
        dangerCmd1.setScriptCmdLevel(0);
        dangerCmds.add(dangerCmd1);
        DangerCmd dangerCmd2 = new DangerCmd();
        dangerCmd2.setScriptCmd("rm");
        dangerCmd2.setScriptCmdLevel(1);
        dangerCmds.add(dangerCmd2);

        ParameterValidationResultDto parameterValidationResultDto = new ParameterValidationResultDto();
        parameterValidationResultDto.setLine(1);

        RedissonLock mockLock = Mockito.mock(RedissonLock.class);

        when(scripts.getDangerCmdMapper()).thenReturn(dangerCmdMapper);
        when(scripts.getParameterService()).thenReturn(parameterService);
        when(scripts.getRedissonClient()).thenReturn(mockRedissonClient);
        when(scripts.getInfoMapper()).thenReturn(infoMapper);

        when(scripts.getDangerCmdMapper().selectDangerCmdList(any(DangerCmd.class))).thenReturn(dangerCmds);
        when(scripts.getParameterService().validateParameter(any(ScriptInfoDto.class))).thenReturn(parameterValidationResultDto);
        when(scripts.getRedissonClient().getLock(anyString())).thenReturn(mockLock);
        when(scripts.getCategoryMapper()).thenReturn(categoryMapper);
        when(scripts.getScriptBusinessConfig()).thenReturn(scriptBusinessConfig);

//        final ValidationResultDto result = myScriptServiceImplUnderTest.saveScript(scriptInfoDto);
    }


    @Test
    void testSaveScript_existWarn() throws Exception {
        // Setup
        final ScriptInfoDto scriptInfoDto = new ScriptInfoDto();
        final ScriptVersionDto scriptVersionDto = new ScriptVersionDto();

        final ParameterValidationDto parameterValidationDto = new ParameterValidationDto();
        parameterValidationDto.setParamDefaultValue("hhh");
        parameterValidationDto.setCheckRule("^[A-Za-z]+$");
        List<ParameterValidationDto> parameterValidationDtoList = new ArrayList<>();
        parameterValidationDtoList.add(parameterValidationDto);
        scriptVersionDto.setParameterValidationDtoList(parameterValidationDtoList);

        final ScriptContentDto scriptContentDto = new ScriptContentDto();
        scriptContentDto.setContent("echo 0\\n这是一个关键命令\\nrm");
        scriptVersionDto.setScriptContentDto(scriptContentDto);
        scriptInfoDto.setScriptVersionDto(scriptVersionDto);
        scriptInfoDto.setIgnoreTipCmd(0);
        scriptInfoDto.setCategoryId(3L);
        scriptInfoDto.setScriptSource(0);
        List<DangerCmd> dangerCmds = new ArrayList<>();
        DangerCmd dangerCmd1 = new DangerCmd();
        dangerCmd1.setScriptCmd("这是一个关键命令");
        dangerCmd1.setScriptCmdLevel(0);
        dangerCmds.add(dangerCmd1);
        DangerCmd dangerCmd2 = new DangerCmd();
        dangerCmd2.setScriptCmd("rm");
        dangerCmd2.setScriptCmdLevel(0);
        dangerCmds.add(dangerCmd2);

        // Mock category org relations for checkCategoryValidation method
        List<OrgBean> categoryOrgRelations = new ArrayList<>();
        OrgBean orgBean = new OrgBean();
        orgBean.setOrgId(1L);
        orgBean.setCode("orgCode1#"); // Match the current user's orgCode from setUpStaticMethod
        orgBean.setCategoryId(3L);
        orgBean.setName("部门1");
        categoryOrgRelations.add(orgBean);

        RedissonLock mockLock = Mockito.mock(RedissonLock.class);

        when(scripts.getDangerCmdMapper()).thenReturn(dangerCmdMapper);
        when(scripts.getParameterService()).thenReturn(parameterService);
        when(scripts.getRedissonClient()).thenReturn(mockRedissonClient);
        when(scripts.getInfoMapper()).thenReturn(infoMapper);
        when(scripts.getCategoryMapper()).thenReturn(categoryMapper);
        when(scripts.getCategoryService()).thenReturn(categoryService);

        // Mock the getCategoryOrgRelations method to return our list
        when(scripts.getCategoryMapper().getCategoryOrgRelations(eq(3L))).thenReturn(categoryOrgRelations);

        // Mock parameter validation result
        ParameterValidationResultDto paramValidationResult = null; // 返回null表示验证通过
        when(scripts.getParameterService().validateParameter(any(ScriptInfoDto.class))).thenReturn(paramValidationResult);

        // Mock script business config
        when(scripts.getScriptBusinessConfig()).thenReturn(scriptBusinessConfig);
        when(scriptBusinessConfig.getPermissionPolicy()).thenReturn("creator"); // 设置权限策略

        // Mock getSameScriptNameZhCount to return 0 (no duplicate script name)
        when(scripts.getInfoMapper().getSameScriptNameZhCount(anyString())).thenReturn(0);

        // Mock category validation
        Category category = new Category();
        category.setId(3L);
        category.setName("测试分类");
        List<Category> categoryList = new ArrayList<>();
        categoryList.add(category);
        when(scripts.getCategoryMapper().selectCategoryList(any(Category.class))).thenReturn(categoryList);
        when(scripts.getCategoryService().buildCategoryFullPath(anyLong())).thenReturn("测试分类路径");

        when(scripts.getRedissonClient().getLock(any(String.class))).thenReturn(mockLock);
        when(scripts.getDangerCmdMapper().selectDangerCmdList(any(DangerCmd.class))).thenReturn(dangerCmds);
        when(scripts.getDangerCmdMapper().selectDangerCmdByCategoryIdList(anyLong())).thenReturn(new ArrayList<>());

        final ValidationResultDto result = myScriptServiceImplUnderTest.saveScript(scriptInfoDto);
        assertNotNull(result);
    }

    @Test
    void testSaveScript_newScript() throws Exception {
        // Setup
        final ScriptInfoDto scriptInfoDto = new ScriptInfoDto();
        final ScriptVersionDto scriptVersionDto = new ScriptVersionDto();
        scriptVersionDto.setExpectType(0);
        scriptVersionDto.setExpectLastline("expectLastline");

        final ParameterValidationDto parameterValidationDto = new ParameterValidationDto();
        List<ParameterValidationDto> parameterValidationDtoList = new ArrayList<>();
        parameterValidationDtoList.add(parameterValidationDto);
        scriptVersionDto.setParameterValidationDtoList(parameterValidationDtoList);

        final ScriptContentDto scriptContentDto = new ScriptContentDto();
        scriptContentDto.setContent("content");
        scriptVersionDto.setScriptContentDto(scriptContentDto);

        final BindFuncVarDto bindFuncVarDto = new BindFuncVarDto();
        scriptVersionDto.setVariableList(new BindFuncVarDto[]{bindFuncVarDto});
        final BindFuncVarDto bindFuncVarDto1 = new BindFuncVarDto();
        scriptVersionDto.setFunctionList(new BindFuncVarDto[]{bindFuncVarDto1});
        scriptVersionDto.setDependentList(Collections.singletonList(1L));


        List<AttachmentUploadDto> attachmentUploadDtoList = new ArrayList<>();
        AttachmentUploadDto attachmentUploadDto = new AttachmentUploadDto();
        String content = "content";
        int size = content.getBytes(StandardCharsets.UTF_8).length;
        attachmentUploadDto.setSize(Long.parseLong(String.valueOf(size)));
        attachmentUploadDto.setName("附件");
        attachmentUploadDto.setContents("content".getBytes());
        attachmentUploadDtoList.add(attachmentUploadDto);
        scriptVersionDto.setAttachmentUploadDtoList(attachmentUploadDtoList);
        scriptVersionDto.setSrcScriptUuid("srcScriptUuid");
        scriptInfoDto.setHisVersionCount(0);

        scriptInfoDto.setScriptNameZh("测试中文名");
        scriptInfoDto.setScriptName("test");
        List<String> platFormList = new ArrayList<>();
        platFormList.add("Linux");
        platFormList.add("Unix");
        scriptInfoDto.setCategoryId(1L);
        scriptInfoDto.setScriptType("sh");
        scriptInfoDto.setPlatforms(platFormList);
        scriptInfoDto.setScriptVersionDto(scriptVersionDto);


        List<Category> categoryList = new ArrayList<>();

        Category category = new Category();
        category.setId(1L);
        category.setName("一级分类");

        categoryList.add(category);


        RedissonLock mockLock = Mockito.mock(RedissonLock.class);


        when(scripts.getInfoMapper()).thenReturn(infoMapper);
        when(scripts.getInfoVersionMapper()).thenReturn(infoVersionMapper);
        when(scripts.getParameterService()).thenReturn(parameterService);
        when(scripts.getInfoVersionTextService()).thenReturn(infoVersionTextService);
        when(scripts.getAttachmentService()).thenReturn(attachmentService);
        when(scripts.getBindFuncVarService()).thenReturn(bindFuncVarService);
        when(scripts.getDangerCmdMapper()).thenReturn(dangerCmdMapper);
        when(scripts.getRedissonClient()).thenReturn(mockRedissonClient);
        when(scripts.getScriptBusinessConfig()).thenReturn(scriptBusinessConfig);
        when(scripts.getCategoryService()).thenReturn(categoryService);
        when(scripts.getCategoryMapper()).thenReturn(categoryMapper);

        when(scripts.getRedissonClient().getLock(any(String.class))).thenReturn(mockLock);
        when(scripts.getScriptBusinessConfig().getTotalScriptAttachmentLimitSize()).thenReturn(DataSize.ofMegabytes(15));
        when(scripts.getCategoryService().buildCategoryFullPath(any())).thenReturn("一级/二级");

        when(scripts.getCategoryMapper().selectCategoryList(any(Category.class))).thenReturn(categoryList);

        // Run the test
        final ValidationResultDto result = myScriptServiceImplUnderTest.saveScript(scriptInfoDto);


        // Verify the results
        verify(scripts.getInfoMapper()).insertInfo(any(Info.class));
        verify(scripts.getInfoVersionMapper()).insertInfoVersion(any(InfoVersion.class));
        verify(scripts.getParameterService()).createParameters(scriptInfoDto);
        verify(scripts.getInfoVersionTextService()).createInfoVersionText(scriptInfoDto);
        verify(scripts.getAttachmentService()).createAttachments(scriptInfoDto, true);
        verify(scripts.getBindFuncVarService()).createBindFuncVars(scriptInfoDto);

    }

    @Test
    void testSaveScript_hasPublishScript() throws Exception {
        // Setup
        final ScriptInfoDto scriptInfoDto = new ScriptInfoDto();
        final ScriptVersionDto scriptVersionDto = new ScriptVersionDto();
        scriptVersionDto.setExpectType(0);
        scriptVersionDto.setExpectLastline("expectLastline");

        final ParameterValidationDto parameterValidationDto = new ParameterValidationDto();
        parameterValidationDto.setParamOrder(2);
        List<ParameterValidationDto> parameterValidationDtoList = new ArrayList<>();
        parameterValidationDtoList.add(parameterValidationDto);
        scriptVersionDto.setParameterValidationDtoList(parameterValidationDtoList);

        final ScriptContentDto scriptContentDto = new ScriptContentDto();
        scriptContentDto.setContent("content");
        scriptVersionDto.setScriptContentDto(scriptContentDto);

        final BindFuncVarDto bindFuncVarDto = new BindFuncVarDto();
        scriptVersionDto.setVariableList(new BindFuncVarDto[]{bindFuncVarDto});
        final BindFuncVarDto bindFuncVarDto1 = new BindFuncVarDto();
        scriptVersionDto.setFunctionList(new BindFuncVarDto[]{bindFuncVarDto1});
        scriptVersionDto.setDependentList(Collections.singletonList(1L));

        List<AttachmentUploadDto> attachmentUploadDtoList = new ArrayList<>();
        scriptVersionDto.setAttachmentUploadDtoList(attachmentUploadDtoList);
        scriptVersionDto.setDescription("description");
        scriptVersionDto.setSrcScriptUuid("srcScriptUuid");

        scriptInfoDto.setHisVersionCount(0);

        scriptInfoDto.setUniqueUuid("infoUniqueUuid");
        scriptInfoDto.setScriptNameZh("测试中文名");
        scriptInfoDto.setScriptName("test");
        scriptInfoDto.setCategoryId(3L);

        List<String> platFormList = new ArrayList<>();
        platFormList.add("Linux");
        platFormList.add("Unix");
        scriptInfoDto.setPlatforms(platFormList);
        scriptInfoDto.setScriptVersionDto(scriptVersionDto);


        Info info = new Info();
        info.setUniqueUuid("infoUniqueUuid");
        info.setEditState(0);

        RedissonLock mockLock = Mockito.mock(RedissonLock.class);

        when(scripts.getInfoMapper()).thenReturn(infoMapper);
        when(scripts.getRedissonClient()).thenReturn(mockRedissonClient);
        when(scripts.getRedissonClient().getLock(any(String.class))).thenReturn(mockLock);
        when(scripts.getInfoMapper().selectInfoById(any())).thenReturn(info);
        when(scripts.getInfoMapper().selectInfoByUniqueUuid("infoUniqueUuid")).thenReturn(info);
        when(scripts.getCategoryMapper()).thenReturn(categoryMapper);
        when(scripts.getScriptBusinessConfig()).thenReturn(scriptBusinessConfig);
        when(scripts.getCategoryService()).thenReturn(categoryService);

        CategoryOrgBean categoryOrgRelations = new CategoryOrgBean();
        OrgBean orgBean = new OrgBean();
        orgBean.setOrgId(1L);
        orgBean.setCode("orgCode1");
        orgBean.setCategoryId(1L);
        orgBean.setName("部门1");
        ArrayList<OrgBean> list = new ArrayList<>();
        list.add(orgBean);
        categoryOrgRelations.setOrgList(list);
        categoryOrgRelations.setLevel(1);
        when(scripts.getCategoryService().getCategoryOrgRelations(scriptInfoDto.getCategoryId())).thenReturn(categoryOrgRelations);

        // Run the test
        final ValidationResultDto result = myScriptServiceImplUnderTest.saveScript(scriptInfoDto);
        // Verify the results
        assertNotNull(result);
    }


//    @ParameterizedTest
//    @ValueSource(strings = {"测试中文名", "测试中文名不重复"})
//    void testSaveScript_ThrowsScriptException(String scriptZhName) throws ScriptException {
//        // Setup
//        final ScriptInfoDto scriptInfoDto = new ScriptInfoDto();
//        final ScriptVersionDto scriptVersionDto = new ScriptVersionDto();
//        scriptVersionDto.setExpectType(0);
//        scriptVersionDto.setExpectLastline("expectLastline");
//        final ParameterValidationDto parameterValidationDto = new ParameterValidationDto();
//        List<ParameterValidationDto> parameterValidationDtoList = new ArrayList<>();
//        parameterValidationDtoList.add(parameterValidationDto);
//        scriptVersionDto.setParameterValidationDtoList(parameterValidationDtoList);
//        final ScriptContentDto scriptContentDto = new ScriptContentDto();
//        scriptContentDto.setContent("content");
//        scriptVersionDto.setScriptContentDto(scriptContentDto);
//        final BindFuncVarDto bindFuncVarDto = new BindFuncVarDto();
//        scriptVersionDto.setVariableList(new BindFuncVarDto[]{bindFuncVarDto});
//        final BindFuncVarDto bindFuncVarDto1 = new BindFuncVarDto();
//        scriptVersionDto.setFunctionList(new BindFuncVarDto[]{bindFuncVarDto1});
//        scriptVersionDto.setDependentList(Collections.singletonList(1L));
//        List<AttachmentUploadDto> attachmentUploadDtoList = new ArrayList<>();
//        scriptVersionDto.setAttachmentUploadDtoList(attachmentUploadDtoList);
//        scriptVersionDto.setDescription("description");
//        scriptVersionDto.setSrcScriptUuid("srcScriptUuid");
//        scriptInfoDto.setHisVersionCount(0);
//        scriptInfoDto.setScriptNameZh(scriptZhName);
//        scriptInfoDto.setScriptName("test");
//        scriptInfoDto.setCategoryId(3L);
//        List<String> platFormList = new ArrayList<>();
//        platFormList.add("Linux");
//        platFormList.add("Unix");
//        scriptInfoDto.setPlatforms(platFormList);
//        scriptInfoDto.setScriptVersionDto(scriptVersionDto);
//
//
//        RedissonLock mockLock = Mockito.mock(RedissonLock.class);
//
//        when(scripts.getInfoMapper()).thenReturn(infoMapper);
////        when(scripts.getDangerCmdMapper()).thenReturn(dangerCmdMapper);
////        when(scripts.getParameterService()).thenReturn(parameterService);
//        when(scripts.getRedissonClient()).thenReturn(mockRedissonClient);
//
//
//        when(scripts.getRedissonClient().getLock(any(String.class))).thenReturn(mockLock);
//
//
//        List<DangerCmd> dangerCmds = new ArrayList<>();
//        DangerCmd dangerCmd1 = new DangerCmd();
//        dangerCmd1.setScriptCmd("这是一个关键命令");
//        dangerCmd1.setScriptCmdLevel(0);
//        dangerCmds.add(dangerCmd1);
//        DangerCmd dangerCmd2 = new DangerCmd();
//        dangerCmd2.setScriptCmd("rm");
//        dangerCmd2.setScriptCmdLevel(1);
//        dangerCmds.add(dangerCmd2);
//        when(scripts.getDangerCmdMapper()).thenReturn(dangerCmdMapper);
//        lenient().when(scripts.getDangerCmdMapper().selectDangerCmdList(any(DangerCmd.class))).thenReturn(dangerCmds);
//        when(scripts.getScriptBusinessConfig()).thenReturn(scriptBusinessConfig);
//
//        ParameterValidationResultDto parameterValidationResultDto = new ParameterValidationResultDto();
//        parameterValidationResultDto.setLine(1);
//        lenient().when(scripts.getParameterService()).thenReturn(parameterService);
//        lenient().when(scripts.getParameterService().validateParameter(any(ScriptInfoDto.class))).thenReturn(parameterValidationResultDto);
//        lenient().when(scripts.getCategoryMapper()).thenReturn(categoryMapper);
//
//        when(infoMapper.getSameScriptNameZhCount(anyString())).thenAnswer(invocation -> {
//            String scriptName = invocation.getArgument(0); // 获取方法参数
//            if ("测试中文名".equals(scriptName)) {
//                return 1; // 当参数是 "测试中文名" 时返回 1
//            } else {
//                return 0; // 其他情况返回 0
//            }
//        });
//
//        if (scriptZhName.equals("测试中文名")) {
//
//            // Run the test
//            assertThatThrownBy(() -> myScriptServiceImplUnderTest.saveScript(scriptInfoDto))
//                    .isInstanceOf(ScriptException.class).hasMessage("script.name.zh.exists");
//        } else {
//            myScriptServiceImplUnderTest.saveScript(scriptInfoDto);
//        }
//
//    }

    @Test
    void testUpdateMyScript() throws ScriptException {
        // Setup
        final ScriptInfoDto scriptInfoDto = new ScriptInfoDto();
        final ScriptVersionDto scriptVersionDto = new ScriptVersionDto();
        scriptVersionDto.setExpectType(0);
        scriptVersionDto.setExpectLastline("expectLastline");
        final ParameterValidationDto parameterValidationDto = new ParameterValidationDto();
        parameterValidationDto.setParamOrder(1);
        List<ParameterValidationDto> parameterValidationDtoList = new ArrayList<>();
        parameterValidationDtoList.add(parameterValidationDto);
        scriptVersionDto.setParameterValidationDtoList(parameterValidationDtoList);
        final ScriptContentDto scriptContentDto = new ScriptContentDto();
        scriptContentDto.setContent("content");
        scriptVersionDto.setScriptContentDto(scriptContentDto);
        final BindFuncVarDto bindFuncVarDto = new BindFuncVarDto();
        scriptVersionDto.setVariableList(new BindFuncVarDto[]{bindFuncVarDto});
        final BindFuncVarDto bindFuncVarDto1 = new BindFuncVarDto();
        scriptVersionDto.setFunctionList(new BindFuncVarDto[]{bindFuncVarDto1});
        scriptVersionDto.setDependentList(Collections.singletonList(1L));
        List<AttachmentUploadDto> attachmentUploadDtoList = new ArrayList<>();
        AttachmentUploadDto attachmentUploadDto = new AttachmentUploadDto();
        attachmentUploadDto.setResponse(new AttachmentResponseDto());
        attachmentUploadDto.getResponse().setId(1L);

        String content = "content";
        int size = content.getBytes(StandardCharsets.UTF_8).length;
        attachmentUploadDto.setSize(Long.parseLong(String.valueOf(size)));
        attachmentUploadDto.setName("附件");
        attachmentUploadDto.setContents("content".getBytes());
        attachmentUploadDtoList.add(attachmentUploadDto);
        scriptVersionDto.setAttachmentUploadDtoList(attachmentUploadDtoList);
        scriptVersionDto.setDescription("description");
        scriptVersionDto.setSrcScriptUuid("srcScriptUuid");
        scriptInfoDto.setHisVersionCount(0);
        scriptInfoDto.setUniqueUuid("infoUniqueUuid");
        List<String> platFormList = new ArrayList<>();
        platFormList.add("Linux");
        platFormList.add("Unix");
        scriptInfoDto.setPlatforms(platFormList);
        scriptInfoDto.setCategoryId(3L);

        scriptInfoDto.setScriptVersionDto(scriptVersionDto);


        InfoVersion infoVersion = new InfoVersion();
        infoVersion.setSrcScriptUuid("srcScriptUuid");
        infoVersion.setInfoUniqueUuid("infoUniqueUuid");


        InfoVersionText infoVersionText = new InfoVersionText();
        infoVersionText.setContent("content");
        infoVersionText.setId(1L);

        List<Parameter> parameterList = new ArrayList<>();
        Parameter parameter = new Parameter();
        parameter.setId(1L);
        parameter.setParamName("aaa");
        parameter.setParamOrder(1);
        parameterList.add(parameter);


        List<Attachment> attachmentList = new ArrayList<>();
        Attachment attachment = new Attachment();
        attachment.setContents(new byte[]{123});
        attachment.setId(1L);
        attachmentList.add(attachment);

        Info info = new Info();
        info.setUniqueUuid("infoUniqueUuid");


        List<Category> categoryList = new ArrayList<>();

        Category category = new Category();
        category.setId(1L);
        category.setName("一级分类");

        categoryList.add(category);

        when(scripts.getInfoMapper()).thenReturn(infoMapper);
        when(scripts.getParameterService()).thenReturn(parameterService);
        when(scripts.getInfoVersionTextService()).thenReturn(infoVersionTextService);
        when(scripts.getAttachmentService()).thenReturn(attachmentService);
        when(scripts.getBindFuncVarService()).thenReturn(bindFuncVarService);
        when(scripts.getParameterMapper()).thenReturn(parameterMapper);
        when(scripts.getBindFuncVarMapper()).thenReturn(bindFuncVarMapper);
        when(scripts.getInfoVersionMapper()).thenReturn(infoVersionMapper);
        when(scripts.getDangerCmdMapper()).thenReturn(dangerCmdMapper);
        when(scripts.getParameterMapper()).thenReturn(parameterMapper);
        when(scripts.getAttachmentMapper()).thenReturn(attachmentMapper);
        when(scripts.getInfoVersionTextMapper()).thenReturn(infoVersionTextMapper);
        when(scripts.getCategoryService()).thenReturn(categoryService);
        when(scripts.getScriptBusinessConfig()).thenReturn(scriptBusinessConfig);
        when(scripts.getCategoryMapper()).thenReturn(categoryMapper);


        when(scripts.getInfoVersionMapper().selectInfoVersionBysrcScriptUuid("srcScriptUuid")).thenReturn(infoVersion);
        when(scripts.getParameterMapper().getParameterByUuid("srcScriptUuid")).thenReturn(parameterList);
        when(scripts.getAttachmentMapper().getAttachmentByUuid("srcScriptUuid")).thenReturn(attachmentList);
        when(scripts.getInfoVersionTextMapper().selectInfoVersionTextByScriptUuid("srcScriptUuid")).thenReturn(infoVersionText);
        when(scripts.getInfoMapper().selectInfoByUniqueUuid("infoUniqueUuid")).thenReturn(info);
        when(scripts.getScriptBusinessConfig().getTotalScriptAttachmentLimitSize()).thenReturn(DataSize.ofMegabytes(15));

        when(scripts.getCategoryService().buildCategoryFullPath(any())).thenReturn("一级/二级");
        when(scripts.getCategoryMapper().selectCategoryList(any(Category.class))).thenReturn(categoryList);


        when(scripts.getCategoryMapper()).thenReturn(categoryMapper);
        when(scripts.getScriptBusinessConfig()).thenReturn(scriptBusinessConfig);
        when(scripts.getInfoVersionTextService().updateInfoVersionText(scriptInfoDto)).thenReturn(true);
        when(scripts.getAttachmentService().updateAttachments(scriptInfoDto)).thenReturn(true);

        CategoryOrgBean categoryOrgRelations = new CategoryOrgBean();
        OrgBean orgBean = new OrgBean();
        orgBean.setOrgId(1L);
        orgBean.setCode("orgCode1");
        orgBean.setCategoryId(3L);
        orgBean.setName("部门1");
        ArrayList<OrgBean> list = new ArrayList<>();
        list.add(orgBean);
        categoryOrgRelations.setOrgList(list);
        categoryOrgRelations.setLevel(1);
        when(scripts.getCategoryService().getCategoryOrgRelations(scriptInfoDto.getCategoryId())).thenReturn(categoryOrgRelations);


        // Run the test
//        ValidationResultDto validationResultDto = myScriptServiceImplUnderTest.updateMyScript(scriptInfoDto);
//
//
//        verify(scripts.getInfoMapper()).updateInfo(any(Info.class));
//        verify(scripts.getInfoVersionTextService()).updateInfoVersionText(scriptInfoDto);
//        verify(scripts.getParameterMapper()).deleteParameterByScriptUuid(any(String.class));
//        verify(scripts.getParameterService()).createParameters(scriptInfoDto);
//        verify(scripts.getAttachmentService()).updateAttachments(scriptInfoDto);
//        verify(scripts.getBindFuncVarMapper()).deleteBindFuncVarByScriptUuid(any(String.class));
//        verify(scripts.getBindFuncVarService()).createBindFuncVars(scriptInfoDto);
    }

    @Test
    void testUpdateMyScript_verify() throws ScriptException {
        // Setup
        final ScriptInfoDto scriptInfoDto = new ScriptInfoDto();
        final ScriptVersionDto scriptVersionDto = new ScriptVersionDto();
        scriptVersionDto.setExpectType(0);
        scriptVersionDto.setExpectLastline("expectLastline");
        scriptVersionDto.setTimeout(60);
        final ParameterValidationDto parameterValidationDto = new ParameterValidationDto();
        parameterValidationDto.setParamOrder(1);
        List<ParameterValidationDto> parameterValidationDtoList = new ArrayList<>();
        parameterValidationDtoList.add(parameterValidationDto);
        scriptVersionDto.setParameterValidationDtoList(parameterValidationDtoList);
        final ScriptContentDto scriptContentDto = new ScriptContentDto();
        scriptContentDto.setContent("content");
        scriptVersionDto.setScriptContentDto(scriptContentDto);
        final BindFuncVarDto bindFuncVarDto = new BindFuncVarDto();
        scriptVersionDto.setVariableList(new BindFuncVarDto[]{bindFuncVarDto});
        final BindFuncVarDto bindFuncVarDto1 = new BindFuncVarDto();
        scriptVersionDto.setFunctionList(new BindFuncVarDto[]{bindFuncVarDto1});
        scriptVersionDto.setDependentList(Collections.singletonList(1L));
        List<AttachmentUploadDto> attachmentUploadDtoList = new ArrayList<>();

        // 添加附件信息
        AttachmentUploadDto attachmentUploadDto = new AttachmentUploadDto();
        AttachmentResponseDto responseDto = new AttachmentResponseDto();
        responseDto.setId(1L);
        attachmentUploadDto.setResponse(responseDto);
        attachmentUploadDtoList.add(attachmentUploadDto);

        scriptVersionDto.setAttachmentUploadDtoList(attachmentUploadDtoList);
        scriptVersionDto.setDescription("description");
        scriptVersionDto.setSrcScriptUuid("srcScriptUuid");
        scriptInfoDto.setHisVersionCount(0);
        scriptInfoDto.setUniqueUuid("infoUniqueUuid");
        scriptInfoDto.setScriptName("script_name");
        scriptInfoDto.setScriptNameZh("脚本名称");
        scriptInfoDto.setScriptType("sh");
        scriptInfoDto.setExecuser("root");
        scriptInfoDto.setScriptLabel("标签1,标签2");

        List<String> platFormList = new ArrayList<>();
        platFormList.add("Linux");
        platFormList.add("Unix");
        scriptInfoDto.setPlatforms(platFormList);
        scriptInfoDto.setScriptVersionDto(scriptVersionDto);
        scriptInfoDto.setCategoryId(1L);

        // 设置当前用户为超级管理员，跳过权限检查
        CurrentUser currentUser = new CurrentUser();
        currentUser.setId(1L);
        currentUser.setSupervisor(true); // 设置为管理员
        currentUser.setFullName("测试用户");
        currentUserUtilMockedStatic.when(CurrentUserUtil::getCurrentUser).thenReturn(currentUser);

        // 配置脚本信息
        InfoVersion infoVersion = new InfoVersion();
        infoVersion.setSrcScriptUuid("srcScriptUuid");
        infoVersion.setInfoUniqueUuid("infoUniqueUuid");
        infoVersion.setExpectType(0);
        infoVersion.setExpectLastline("expectLastline");
        infoVersion.setDescription("description");
        infoVersion.setTimeout(60);

        // 设置脚本版本信息模拟
        ScriptVersionDto mockScriptVersionDto = new ScriptVersionDto();
        mockScriptVersionDto.setCreatorName("测试用户"); // 与当前用户名匹配
        when(scripts.getInfoVersionService()).thenReturn(infoVersionService);
        when(infoVersionService.selectInfoVersionBySrcScriptUuid("srcScriptUuid")).thenReturn(mockScriptVersionDto);
        when(scripts.getScriptBusinessConfig()).thenReturn(scriptBusinessConfig);

        // 设置权限策略为creator
        when(scriptBusinessConfig.getPermissionPolicy()).thenReturn("creator");

        InfoVersionText infoVersionText = new InfoVersionText();
        infoVersionText.setContent("content");
        infoVersionText.setId(1L);

        List<Parameter> parameterList = new ArrayList<>();
        Parameter parameter = new Parameter();
        parameter.setId(1L);
        parameter.setParamName("param");
        parameter.setParamOrder(1);
        parameterList.add(parameter);

        List<Attachment> attachmentList = new ArrayList<>();
        Attachment attachment = new Attachment();
        attachment.setContents(new byte[]{123});
        attachment.setId(1L);
        attachmentList.add(attachment);

        Info info = new Info();
        info.setUniqueUuid("infoUniqueUuid");
        info.setScriptNameZh("新脚本名称"); // 修改脚本名称，确保checkScript检测到变化
        info.setScriptName("new_script_name");
        info.setExecuser("other");
        info.setScriptLabel("标签3,标签4");
        info.setCategoryId(1L);
        info.setPlatform("Linux,Unix");
        info.setScriptType("sh");

        // 为核心依赖设置mock
        when(scripts.getParameterService()).thenReturn(parameterService);
        when(scripts.getDangerCmdMapper()).thenReturn(dangerCmdMapper);
        when(scripts.getParameterMapper()).thenReturn(parameterMapper);
        when(scripts.getAttachmentMapper()).thenReturn(attachmentMapper);
        when(scripts.getInfoVersionTextMapper()).thenReturn(infoVersionTextMapper);
        when(scripts.getInfoVersionMapper()).thenReturn(infoVersionMapper);
        when(scripts.getInfoMapper()).thenReturn(infoMapper);
        when(scripts.getBindFuncVarMapper()).thenReturn(bindFuncVarMapper);
        when(scripts.getBindFuncVarService()).thenReturn(bindFuncVarService);
        when(scripts.getAttachmentService()).thenReturn(attachmentService);
        when(scripts.getInfoVersionTextService()).thenReturn(infoVersionTextService);

        // 设置数据库查询返回
        when(scripts.getInfoVersionMapper().selectInfoVersionBysrcScriptUuid("srcScriptUuid")).thenReturn(infoVersion);
        when(scripts.getParameterMapper().getParameterByUuid("srcScriptUuid")).thenReturn(parameterList);
        when(scripts.getAttachmentMapper().getAttachmentByUuid("srcScriptUuid")).thenReturn(attachmentList);
        when(scripts.getInfoVersionTextMapper().selectInfoVersionTextByScriptUuid("srcScriptUuid")).thenReturn(infoVersionText);
        when(scripts.getInfoMapper().selectInfoByUniqueUuid("infoUniqueUuid")).thenReturn(info);

        // 设置验证方法返回null，表示验证通过
        when(scripts.getParameterService().validateParameter(any(ScriptInfoDto.class))).thenReturn(null);

        // 正确设置分类相关的依赖
        when(scripts.getCategoryMapper()).thenReturn(categoryMapper);
        when(scripts.getCategoryService()).thenReturn(categoryService);

        // 模拟分类信息，避免isExistCategory方法抛出category.not.exist异常
        Category category = new Category();
        category.setId(1L);
        category.setName("测试分类");
        List<Category> categoryList = new ArrayList<>();
        categoryList.add(category);

        // 确保使用与Info对象相同的categoryId进行mock
        when(categoryMapper.selectCategoryList(any(Category.class))).thenReturn(categoryList);

        // 设置分类路径
        when(categoryService.buildCategoryFullPath(anyLong())).thenReturn("测试分类路径");

        // 设置分类部门关系
        CategoryOrgBean categoryOrgRelations = new CategoryOrgBean();
        OrgBean orgBean = new OrgBean();
        orgBean.setOrgId(1L);
        orgBean.setCode("orgCode1");
        orgBean.setCategoryId(1L);
        orgBean.setName("部门1");
        ArrayList<OrgBean> list = new ArrayList<>();
        list.add(orgBean);
        categoryOrgRelations.setOrgList(list);
        categoryOrgRelations.setLevel(1);
        when(categoryService.getCategoryOrgRelations(scriptInfoDto.getCategoryId())).thenReturn(categoryOrgRelations);

        // 设置附件大小限制配置，避免validateFileSize方法出现NPE
        DataSize dataSize = DataSize.ofMegabytes(15);
        when(scriptBusinessConfig.getTotalScriptAttachmentLimitSize()).thenReturn(dataSize);

        // 设置模拟RedissonClient，避免handleRedisLabel方法出错
        when(scripts.getRedissonClient()).thenReturn(mockRedissonClient);
        org.redisson.api.RKeys rKeys = Mockito.mock(org.redisson.api.RKeys.class);
        when(mockRedissonClient.getKeys()).thenReturn(rKeys);
        when(rKeys.countExists(anyString())).thenReturn(0L); // 模拟Redis key不存在

        // 设置updateInfo, updateInfoVersion方法返回
        when(scripts.getInfoMapper().updateInfo(any(Info.class))).thenReturn(1);
        when(scripts.getInfoVersionMapper().updateInfoVersion(any(InfoVersion.class))).thenReturn(1);
        when(scripts.getInfoVersionTextService().updateInfoVersionText(any(ScriptInfoDto.class))).thenReturn(true);
        when(scripts.getAttachmentService().updateAttachments(any(ScriptInfoDto.class))).thenReturn(true);

        // 执行测试方法
        ValidationResultDto validationResultDto = myScriptServiceImplUnderTest.updateMyScript(scriptInfoDto);

        // 根据updateMyScript方法的逻辑，如果没有异常，最终返回null
        assertNull(validationResultDto);

        // 验证方法调用
        verify(scripts.getInfoMapper()).updateInfo(any(Info.class));
        verify(scripts.getInfoVersionMapper()).updateInfoVersion(any(InfoVersion.class));
        verify(scripts.getInfoVersionTextService()).updateInfoVersionText(scriptInfoDto);
        verify(scripts.getParameterMapper()).deleteParameterByScriptUuid("srcScriptUuid");
        verify(scripts.getParameterService()).createParameters(scriptInfoDto);
        verify(scripts.getAttachmentService()).updateAttachments(scriptInfoDto);
        verify(scripts.getBindFuncVarMapper()).deleteBindFuncVarByScriptUuid("srcScriptUuid");
        verify(scripts.getBindFuncVarService()).createBindFuncVars(scriptInfoDto);
    }

    @Test
    void testListMyScript() throws Throwable {
        // Setup
        ScriptInfoQueryDto scriptInfoQueryDto = new ScriptInfoQueryDto();
        scriptInfoQueryDto.setCategoryId(1L);
        scriptInfoQueryDto.setPageNum(1);
        scriptInfoQueryDto.setPageSize(50);

        //List<MyScriptBean> myScriptBeans = new ArrayList<>();
        MyScriptBean myScriptBean = new MyScriptBean();
        myScriptBean.setScriptInfoId(1L);
        myScriptBean.setScriptNameZh("测试分类");
        myScriptBean.setScriptName("test");
        myScriptBean.setCategory("一级分类/二级分类");
        myScriptBean.setUniqueUuid("uniqueUuid");
        //myScriptBeans.add(myScriptBean);
        Page<MyScriptBean> page = new Page<>();
        page.add(myScriptBean);

        when(scripts.getCategoryService()).thenReturn(categoryService);
        when(scripts.getMyScriptMapper()).thenReturn(myScriptMapper);
        when(scripts.getParameterService()).thenReturn(parameterService);
        when(scripts.getScriptStatementService()).thenReturn(scriptStatementService);

        when(scripts.getMyScriptMapper().selectMyScriptList(any())).thenReturn(page);

        when(scripts.getScriptStatementService().getScriptStatementByInfoId(any())).thenReturn(new StatementDto());
        when(scripts.getScriptBusinessConfig()).thenReturn(scriptBusinessConfig);
        when(scripts.getCategoryMapper()).thenReturn(categoryMapper);

        // 创建一个 mock 的 ConfigurableListableBeanFactory
        ConfigurableListableBeanFactory mockBeanFactory = Mockito.mock(ConfigurableListableBeanFactory.class);


        // 使用反射设置 SpringUtil 的 beanFactory 字段
        Field field = SpringUtil.class.getDeclaredField("beanFactory");
        field.setAccessible(true);
        field.set(null, mockBeanFactory); // 设置静态字段
        // Run the test
        final PageInfo<ScriptInfoApiDto> result = myScriptServiceImplUnderTest.selectScriptPageList(scriptInfoQueryDto);

        assertNotNull(result);
    }

    @ParameterizedTest
    @ValueSource(strings = {"srcScriptUuid"})
    @NullSource
    void testGetScriptDetail(String srcScriptUuid) throws ScriptException {
        // Setup
        Info info = new Info();
        info.setUniqueUuid("uniqueUuid");
        info.setCategoryId(1L);
        info.setScriptType("sh");
        info.setPlatform("Linux,Unix");

        Category category = new Category();
        category.setId(1L);
        category.setName("一级");
        category.setLevel(1);

        InfoVersion infoVersion = new InfoVersion();
        infoVersion.setId(1L);
        infoVersion.setInfoUniqueUuid("uniqueUuid");
        infoVersion.setSrcScriptUuid("falseSrcUuid");
        infoVersion.setEditState(0);
        infoVersion.setExpectType(0);

        InfoVersionText infoVersionText = new InfoVersionText();
        infoVersionText.setContent("123");


        when(scripts.getInfoMapper()).thenReturn(infoMapper);
        when(scripts.getInfoVersionMapper()).thenReturn(infoVersionMapper);
        when(scripts.getInfoVersionTextMapper()).thenReturn(infoVersionTextMapper);
        when(scripts.getParameterService()).thenReturn(parameterService);
        when(scripts.getAttachmentService()).thenReturn(attachmentService);
        when(scripts.getBindFuncVarService()).thenReturn(bindFuncVarService);
        when(scripts.getCategoryService()).thenReturn(categoryService);

        when(scripts.getCategoryService().getCategory(any(Long.class))).thenReturn(category);
        when(scripts.getInfoVersionTextMapper().selectInfoVersionTextByScriptUuid(any(String.class))).thenReturn(infoVersionText);
        if (srcScriptUuid != null) {
            when(scripts.getInfoVersionMapper().selectInfoVersionBysrcScriptUuid(any())).thenReturn(infoVersion);
            when(scripts.getInfoMapper().selectInfoByUniqueUuid(any())).thenReturn(info);
        } else {
            when(scripts.getInfoVersionMapper().selectLastInfoVersionByInfoUuid(any(String.class), any())).thenReturn(infoVersion);
            when(scripts.getInfoMapper().selectInfoById(any())).thenReturn(info);
        }
        // Run the test
        ScriptInfoQueryDto scriptInfoQueryDto = new ScriptInfoQueryDto();
        scriptInfoQueryDto.setScriptInfoId(1L);
        scriptInfoQueryDto.setSrcScriptUuid(srcScriptUuid);
        scriptInfoQueryDto.setDubboFlag(false);
        scriptInfoQueryDto.setSrcScriptUuid("abc");
        final ScriptInfoDto result = myScriptServiceImplUnderTest.getScriptDetail(scriptInfoQueryDto);

        assertNotNull(result);


        when(scripts.getInfoMapper().getScriptInfo(any())).thenReturn(info);
        when(scripts.getInfoVersionMapper().selectLastInfoVersionByInfoUuid(any(String.class), any())).thenReturn(null);

        final ScriptInfoDto resultSec = myScriptServiceImplUnderTest.getScriptDetail(scriptInfoQueryDto);

        assertNotNull(resultSec);
        // Verify the results
    }

    @ParameterizedTest
    @ValueSource(strings = {"srcScriptUuid"})
    @NullSource
    void testGetScriptDetail_DtoNull(String srcScriptUuid) throws ScriptException {
        // Setup
        Info info = new Info();
        info.setUniqueUuid("uniqueUuid");
        info.setCategoryId(1L);
        info.setScriptType("sh");
        info.setPlatform("Linux,Unix");

        Category category = new Category();
        category.setId(1L);
        category.setName("一级");
        category.setLevel(1);

        InfoVersion infoVersion = new InfoVersion();
        infoVersion.setId(1L);
        infoVersion.setInfoUniqueUuid("uniqueUuid");
        infoVersion.setSrcScriptUuid("falseSrcUuid");
        infoVersion.setEditState(0);
        infoVersion.setExpectType(0);

        InfoVersionText infoVersionText = new InfoVersionText();
        infoVersionText.setContent("123");


        when(scripts.getInfoMapper()).thenReturn(infoMapper);
        when(scripts.getInfoVersionMapper()).thenReturn(infoVersionMapper);
        when(scripts.getInfoVersionTextMapper()).thenReturn(infoVersionTextMapper);
        when(scripts.getParameterService()).thenReturn(parameterService);
        when(scripts.getAttachmentService()).thenReturn(attachmentService);
        when(scripts.getBindFuncVarService()).thenReturn(bindFuncVarService);
        when(scripts.getCategoryService()).thenReturn(categoryService);

        when(scripts.getCategoryService().getCategory(any(Long.class))).thenReturn(category);
        when(scripts.getInfoVersionTextMapper().selectInfoVersionTextByScriptUuid(any(String.class))).thenReturn(infoVersionText);
        if (srcScriptUuid != null) {
            when(scripts.getInfoVersionMapper().selectInfoVersionBysrcScriptUuid(any())).thenReturn(infoVersion);
            when(scripts.getInfoMapper().selectInfoByUniqueUuid(any())).thenReturn(info);
        } else {
            when(scripts.getInfoVersionMapper().selectLastInfoVersionByInfoUuid(any(String.class), any())).thenReturn(infoVersion);
            when(scripts.getInfoMapper().selectInfoById(any())).thenReturn(info);
        }
        // Run the test
        ScriptInfoQueryDto scriptInfoQueryDto = null;
        final ScriptInfoDto result = myScriptServiceImplUnderTest.getScriptDetail(scriptInfoQueryDto);

        assertNotNull(result);
        System.out.println(result);
        // Verify the results
    }


    @ParameterizedTest
    @ValueSource(strings = {"srcScriptUuid"})
    @NullSource
    void testGetScriptDetail_VersionNull(String srcScriptUuid) throws ScriptException {
        // Setup
        Info info = new Info();
        info.setUniqueUuid("uniqueUuid");
        info.setCategoryId(1L);
        info.setScriptType("sh");
        info.setPlatform("Linux,Unix");

        Category category = new Category();
        category.setId(1L);
        category.setName("一级");
        category.setLevel(1);

        InfoVersion infoVersion = null;

        InfoVersionText infoVersionText = new InfoVersionText();
        infoVersionText.setContent("123");


        when(scripts.getInfoMapper()).thenReturn(infoMapper);
        when(scripts.getInfoVersionMapper()).thenReturn(infoVersionMapper);
        when(scripts.getInfoVersionTextMapper()).thenReturn(infoVersionTextMapper);
        when(scripts.getParameterService()).thenReturn(parameterService);
        when(scripts.getAttachmentService()).thenReturn(attachmentService);
        when(scripts.getBindFuncVarService()).thenReturn(bindFuncVarService);
        when(scripts.getCategoryService()).thenReturn(categoryService);

        when(scripts.getCategoryService().getCategory(any(Long.class))).thenReturn(category);
        when(scripts.getInfoVersionTextMapper().selectInfoVersionTextByScriptUuid(any(String.class))).thenReturn(infoVersionText);
        if (srcScriptUuid != null) {
            when(scripts.getInfoVersionMapper().selectInfoVersionBysrcScriptUuid(any())).thenReturn(infoVersion);
            when(scripts.getInfoMapper().selectInfoByUniqueUuid(any())).thenReturn(info);
        } else {
            when(scripts.getInfoVersionMapper().selectLastInfoVersionByInfoUuid(any(String.class), any())).thenReturn(infoVersion);
            when(scripts.getInfoMapper().selectInfoById(any())).thenReturn(info);
        }
        // Run the test
        ScriptInfoQueryDto scriptInfoQueryDto = new ScriptInfoQueryDto();
        scriptInfoQueryDto.setScriptInfoId(1L);
        scriptInfoQueryDto.setSrcScriptUuid(srcScriptUuid);
        scriptInfoQueryDto.setDubboFlag(false);
        final ScriptInfoDto result = myScriptServiceImplUnderTest.getScriptDetail(scriptInfoQueryDto);

        assertNotNull(result);
        System.out.println(result);
        // Verify the results
    }


    @ParameterizedTest
    @ValueSource(strings = {"srcScriptUuid"})
    @NullSource
    void testGetScriptDetail_DefaultVersionTrue(String srcScriptUuid) throws ScriptException {
        // Setup
        Info info = new Info();
        info.setUniqueUuid("uniqueUuid");
        info.setCategoryId(1L);
        info.setScriptType("sh");
        info.setPlatform("Linux,Unix");

        Category category = new Category();
        category.setId(1L);
        category.setName("一级");
        category.setLevel(1);

        InfoVersion infoVersion = null;

        InfoVersionText infoVersionText = new InfoVersionText();
        infoVersionText.setContent("123");


        when(scripts.getInfoMapper()).thenReturn(infoMapper);
        when(scripts.getInfoVersionMapper()).thenReturn(infoVersionMapper);
        when(scripts.getInfoVersionTextMapper()).thenReturn(infoVersionTextMapper);
        when(scripts.getParameterService()).thenReturn(parameterService);
        when(scripts.getAttachmentService()).thenReturn(attachmentService);
        when(scripts.getBindFuncVarService()).thenReturn(bindFuncVarService);
        when(scripts.getCategoryService()).thenReturn(categoryService);

        when(scripts.getCategoryService().getCategory(any(Long.class))).thenReturn(category);
        when(scripts.getInfoVersionTextMapper().selectInfoVersionTextByScriptUuid(any(String.class))).thenReturn(infoVersionText);
        if (srcScriptUuid != null) {
            when(scripts.getInfoVersionMapper().selectInfoVersionBysrcScriptUuid(any())).thenReturn(infoVersion);
            when(scripts.getInfoMapper().selectInfoByUniqueUuid(any())).thenReturn(info);
        } else {
            when(scripts.getInfoVersionMapper().selectLastInfoVersionByInfoUuid(any(String.class), any())).thenReturn(infoVersion);
            when(scripts.getInfoMapper().selectInfoById(any())).thenReturn(info);
        }
        // Run the test
        ScriptInfoQueryDto scriptInfoQueryDto = new ScriptInfoQueryDto();
        scriptInfoQueryDto.setScriptInfoId(1L);
        scriptInfoQueryDto.setSrcScriptUuid(srcScriptUuid);
        scriptInfoQueryDto.setDubboFlag(false);
        scriptInfoQueryDto.setShowDefaultVersion(true);

        when(scripts.getInfoVersionMapper().selectDefaultInfoVersionBysrcScriptUuid(any())).thenReturn(null);

        final ScriptInfoDto result = myScriptServiceImplUnderTest.getScriptDetail(scriptInfoQueryDto);

        assertNotNull(result);
        System.out.println(result);
        // Verify the results
    }


    @Test
    void testDeleteMyScript_noPulish() throws Exception {
        Info info = new Info();
        info.setId(1L);
        info.setEditState(0);
        info.setUniqueUuid("UniqueUuid");
        List<Info> infos = new ArrayList<>();
        infos.add(info);

        InfoVersion infoVersion = new InfoVersion();
        infoVersion.setEditState(0);
        infoVersion.setSrcScriptUuid("srcScriptUuid");
        infoVersion.setInfoUniqueUuid("uniqueUuid");
        // Setup
        when(scripts.getInfoMapper()).thenReturn(infoMapper);
        when(scripts.getInfoVersionMapper()).thenReturn(infoVersionMapper);
        when(scripts.getInfoVersionTextMapper()).thenReturn(infoVersionTextMapper);
        when(scripts.getParameterMapper()).thenReturn(parameterMapper);
        when(scripts.getAttachmentMapper()).thenReturn(attachmentMapper);


        when(scripts.getInfoVersionMapper().selectInfoVersionById(any(Long.class))).thenReturn(infoVersion);
        when(scripts.getInfoMapper().selectInfoByUniqueUuid(any(String.class))).thenReturn(info);


        List<InfoVersion> infoVersionList = new ArrayList<>();
        infoVersionList.add(infoVersion);
        when(scripts.getInfoVersionMapper().selectDefaultInfoVersionByIds(any(Long[].class))).thenReturn(infoVersionList);
        // Run the test
        ScriptDeleteDto scriptDeleteDto = new ScriptDeleteDto();
        scriptDeleteDto.setIds(new Long[]{1L});
        myScriptServiceImplUnderTest.deleteMyScript(scriptDeleteDto, false);

        // Verify the results
        verify(scripts.getInfoVersionMapper()).deleteInfoVersionById(any(Long.class));
        verify(scripts.getInfoVersionTextMapper()).deleteInfoVersionTextByScriptUuids(any(String[].class));
        verify(scripts.getParameterMapper()).deleteParameterByScriptUuids(any(String[].class));
        verify(scripts.getAttachmentMapper()).deleteAttachmentByScriptUuids(any(String[].class));

    }


    @Test
    void testDeleteMyScript_pulish() throws Exception {
        Info info = new Info();
        info.setId(1L);
        info.setEditState(1);
        info.setUniqueUuid("UniqueUuid");
        List<Info> infos = new ArrayList<>();
        infos.add(info);

        InfoVersion infoVersion = new InfoVersion();
        infoVersion.setEditState(1);
        infoVersion.setSrcScriptUuid("srcScriptUuid");
        infoVersion.setInfoUniqueUuid("uniqueUuid");

        ResultApiDto resultApiDto = new ResultApiDto();
        resultApiDto.setTaskId(1L);
        resultApiDto.setSuccess(true);
        resultApiDto.setMessage("hhhhh");


        // Setup
        when(scripts.getInfoMapper()).thenReturn(infoMapper);
        when(scripts.getInfoVersionMapper()).thenReturn(infoVersionMapper);
        when(scripts.getInfoVersionTextMapper()).thenReturn(infoVersionTextMapper);
        when(scripts.getParameterMapper()).thenReturn(parameterMapper);
        when(scripts.getAttachmentMapper()).thenReturn(attachmentMapper);
        when(scripts.getAuditRelationService()).thenReturn(auditRelationService);
        when(scripts.getRemoteCall()).thenReturn(remoteCall);
        when(scripts.getScriptBusinessConfig()).thenReturn(scriptBusinessConfig);

        // 关键修复：添加对ScriptBusinessConfig的isPublishScriptWithoutDoubleCheck方法的mock
        when(scripts.getScriptBusinessConfig().isPublishScriptWithoutDoubleCheck()).thenReturn(false);


        when(scripts.getInfoVersionMapper().selectInfoVersionById(any(Long.class))).thenReturn(infoVersion);
        when(scripts.getInfoMapper().selectInfoByUniqueUuid(any(String.class))).thenReturn(info);
        when(scripts.getAuditRelationService().insertAuditRelation(any())).thenReturn(1);
        when(scripts.getRemoteCall().applyForDoubleCheck(anyString(), any(DoubleCheckApiDto.class))).thenReturn(resultApiDto);

        when(scripts.getiUserInfo()).thenReturn(iUserInfo);

        List<InfoVersion> infoVersionList = new ArrayList<>();
        infoVersionList.add(infoVersion);
        when(scripts.getInfoVersionMapper().selectDefaultInfoVersionByIds(any(Long[].class))).thenReturn(infoVersionList);


        // Run the test
        ScriptDeleteDto scriptDeleteDto = new ScriptDeleteDto();
        scriptDeleteDto.setIds(new Long[]{1L});
        myScriptServiceImplUnderTest.deleteMyScript(scriptDeleteDto, true);

        // Verify the results
        verify(scripts.getInfoVersionMapper()).deleteInfoVersionById(any(Long.class));
        verify(scripts.getInfoVersionTextMapper()).deleteInfoVersionTextByScriptUuids(any(String[].class));
        verify(scripts.getParameterMapper()).deleteParameterByScriptUuids(any(String[].class));
        verify(scripts.getAttachmentMapper()).deleteAttachmentByScriptUuids(any(String[].class));

    }

    @Test
    void testDeleteMyScript_ThrowsScriptException() {

        InfoVersion infoVersion = new InfoVersion();
        infoVersion.setEditState(1);
        infoVersion.setSrcScriptUuid("srcScriptUuid");
        // Setup
        when(scripts.getInfoVersionMapper()).thenReturn(infoVersionMapper);

        List<InfoVersion> infoVersionList = new ArrayList<>();
        infoVersionList.add(infoVersion);
        when(scripts.getInfoVersionMapper().selectDefaultInfoVersionByIds(any(Long[].class))).thenReturn(infoVersionList);
        // Run the test
        ScriptDeleteDto scriptDeleteDto = new ScriptDeleteDto();
        scriptDeleteDto.setIds(new Long[]{1L});
        assertThatThrownBy(() -> myScriptServiceImplUnderTest.deleteMyScript(scriptDeleteDto, false))
                .isInstanceOf(ScriptException.class);
    }


    @Test
    void testGetScriptServiceVersionListForAllScript() {
        // Setup
        // Run the test
        ScriptVersionInfoBean scriptVersionInfoBean = new ScriptVersionInfoBean();
        scriptVersionInfoBean.setVersion("1.0");
        List<ScriptVersionInfoBean> scriptVersionInfoBeans = new ArrayList<>();
        scriptVersionInfoBeans.add(scriptVersionInfoBean);


        when(scripts.getMyScriptMapper()).thenReturn(myScriptMapper);

        when(scripts.getMyScriptMapper().getScriptServiceVersionListForAllScript(any(String.class))).thenReturn(scriptVersionInfoBeans);

        final List<ScriptVersionInfoDto> result = myScriptServiceImplUnderTest.getScriptServiceVersionListForAllScript("serviceUuid");
        assertNotNull(result);
        // Verify the results
    }

    @Test
    void testGetScriptServiceVersionListForAllScript_noVersion() {
        // Setup
        // Run the test
        ScriptVersionInfoBean scriptVersionInfoBean = new ScriptVersionInfoBean();
        List<ScriptVersionInfoBean> scriptVersionInfoBeans = new ArrayList<>();
        scriptVersionInfoBeans.add(scriptVersionInfoBean);


        when(scripts.getMyScriptMapper()).thenReturn(myScriptMapper);

        when(scripts.getMyScriptMapper().getScriptServiceVersionListForAllScript(any(String.class))).thenReturn(scriptVersionInfoBeans);

        final List<ScriptVersionInfoDto> result = myScriptServiceImplUnderTest.getScriptServiceVersionListForAllScript("serviceUuid");
        assertNotNull(result);
        // Verify the results
    }

    @Test
    void testNoVersionRollBack() {
        // Setup
        // Run the test
        InfoVersionText infoVersionText = new InfoVersionText();
        infoVersionText.setContent("1346523");
        List<InfoVersionText> infoVersionTexts = new ArrayList<>();
        infoVersionTexts.add(infoVersionText);

        Parameter parameter = new Parameter();
        parameter.setId(1L);
        parameter.setParamDesc("teywe");
        List<Parameter> parameterList = new ArrayList<>();
        parameterList.add(parameter);

        Attachment attachment = new Attachment();
        attachment.setId(1L);
        attachment.setSize(100L);
        List<Attachment> attachments = new ArrayList<>();
        attachments.add(attachment);

        BindFuncVar bindFuncVar = new BindFuncVar();
        bindFuncVar.setBindType(1);
        bindFuncVar.setObjName("变量");
        List<BindFuncVar> bindFuncVars = new ArrayList<>();
        bindFuncVars.add(bindFuncVar);


        //返回的InfoVersion 信息
        InfoVersion infoVersion = new InfoVersion();
        infoVersion.setId(1L);
        infoVersion.setVersion("1.0");

        when(scripts.getMyScriptMapper()).thenReturn(myScriptMapper);
        when(scripts.getBindFuncVarMapper()).thenReturn(bindFuncVarMapper);
        when(scripts.getParameterMapper()).thenReturn(parameterMapper);
        when(scripts.getAttachmentMapper()).thenReturn(attachmentMapper);
        when(scripts.getInfoVersionMapper()).thenReturn(infoVersionMapper);

        when(scripts.getMyScriptMapper().getNewContentInfo(any(Long.class))).thenReturn(infoVersionTexts);
        when(scripts.getParameterMapper().getParameterByUuid(any(String.class))).thenReturn(parameterList);
        when(scripts.getAttachmentMapper().getAttachmentByUuid(any(String.class))).thenReturn(attachments);
        when(scripts.getBindFuncVarMapper().getBindFuncVarByUuid(any(String.class))).thenReturn(bindFuncVars);
        when(scripts.getInfoVersionMapper().selectInfoVersionBysrcScriptUuid(any())).thenReturn(infoVersion);


        final Map<String,Object> result = myScriptServiceImplUnderTest.noVersionRollBack(1L, 2L, "oldUuid",
                "0bc66beb-3f09-47e9-b755-fb1d7ee439f8");

        // Verify the results
        assertThat((boolean)result.get("success")).isTrue();
    }

    @Test
    void testDisableVersionRollBack() {
        // Setup
        // Run the test
        InfoVersionText infoVersionText = new InfoVersionText();
        infoVersionText.setContent("1346523");
        List<InfoVersionText> infoVersionTexts = new ArrayList<>();
        infoVersionTexts.add(infoVersionText);

        Parameter parameter = new Parameter();
        parameter.setId(1L);
        parameter.setParamDesc("teywe");
        List<Parameter> parameterList = new ArrayList<>();
        parameterList.add(parameter);

        Attachment attachment = new Attachment();
        attachment.setId(1L);
        attachment.setSize(100L);
        List<Attachment> attachments = new ArrayList<>();
        attachments.add(attachment);

        BindFuncVar bindFuncVar = new BindFuncVar();
        bindFuncVar.setBindType(1);
        bindFuncVar.setObjName("变量");
        List<BindFuncVar> bindFuncVars = new ArrayList<>();
        bindFuncVars.add(bindFuncVar);


        //返回的InfoVersion 信息
        InfoVersion infoVersion = new InfoVersion();
        infoVersion.setId(1L);
        infoVersion.setVersion("1.0");

        when(scripts.getMyScriptMapper()).thenReturn(myScriptMapper);
        when(scripts.getBindFuncVarMapper()).thenReturn(bindFuncVarMapper);
        when(scripts.getParameterMapper()).thenReturn(parameterMapper);
        when(scripts.getAttachmentMapper()).thenReturn(attachmentMapper);
        when(scripts.getInfoVersionMapper()).thenReturn(infoVersionMapper);

        when(scripts.getMyScriptMapper().getNewContentInfo(any(Long.class))).thenReturn(infoVersionTexts);
        when(scripts.getParameterMapper().getParameterByUuid(any(String.class))).thenReturn(parameterList);
        when(scripts.getAttachmentMapper().getAttachmentByUuid(any(String.class))).thenReturn(attachments);
        when(scripts.getBindFuncVarMapper().getBindFuncVarByUuid(any(String.class))).thenReturn(bindFuncVars);
        when(scripts.getInfoVersionMapper().selectInfoVersionBysrcScriptUuid(any())).thenReturn(infoVersion);

        InfoVersion infoVersion1 = new InfoVersion();
        infoVersion1.setId(1L);
        infoVersion1.setVersion("1.0");
        infoVersion1.setUseState(0);

        when(scripts.getInfoVersionMapper().selectInfoVersionById(any())).thenReturn(infoVersion1);

        final Map<String,Object> result = myScriptServiceImplUnderTest.noVersionRollBack(1L, 2L, "oldUuid",
                "0bc66beb-3f09-47e9-b755-fb1d7ee439f8");

        // Verify the results
        assertThat((boolean)result.get("success")).isFalse();
    }

    @Test
    void testHasVersionRollBack_success() {
        // Setup
        // Run the test
        // 模拟 isSynchronizationActive 返回 true
        try (MockedStatic<TransactionSynchronizationManager> mockedManager = Mockito.mockStatic(TransactionSynchronizationManager.class)) {
            mockedManager.when(TransactionSynchronizationManager::isSynchronizationActive).thenReturn(true);

            // 模拟 registerSynchronization，不执行真实逻辑
            mockedManager.when(() -> TransactionSynchronizationManager.registerSynchronization(any(TransactionSynchronization.class)))
                         .thenAnswer(invocation -> null);

            when(scripts.getInfoVersionMapper()).thenReturn(infoVersionMapper);

            when(scripts.getInfoVersionMapper().updateInfoVersion(any(InfoVersion.class))).thenReturn(1);
            final Map<String,Object> result = myScriptServiceImplUnderTest.hasVersionRollBack(1L, 2L);

            // Verify the results
            assertThat((boolean)result.get("success")).isTrue();
        }

    }

    @Test
    void testHasVersionRollBack_fail() {
        // Setup
        // Run the test
        when(scripts.getInfoVersionMapper()).thenReturn(infoVersionMapper);

        when(scripts.getInfoVersionMapper().updateInfoVersion(any(InfoVersion.class))).thenReturn(0);
        final Map<String,Object> result = myScriptServiceImplUnderTest.hasVersionRollBack(1L, 2L);

        // Verify the results
        assertThat((boolean)result.get("success")).isFalse();
    }

    @Test
    void testDisableVersionRollBack_fail() {
        // Setup
        // Run the test
        when(scripts.getInfoVersionMapper()).thenReturn(infoVersionMapper);

        when(scripts.getInfoVersionMapper().updateInfoVersion(any(InfoVersion.class))).thenReturn(0);

        InfoVersion oldInfoVersion = new InfoVersion();
        oldInfoVersion.setId(1L);
        oldInfoVersion.setUseState(0);

        when(scripts.getInfoVersionMapper().selectInfoVersionById(any())).thenReturn(oldInfoVersion);

        final Map<String,Object> result = myScriptServiceImplUnderTest.hasVersionRollBack(1L, 2L);

        // Verify the results
        assertThat((boolean)result.get("success")).isFalse();
    }

    @Test
    void testDownloadScript() {
        // Setup
        final MockHttpServletResponse response = new MockHttpServletResponse();

        // Run the test

        Attachment attachment = new Attachment();
        attachment.setId(1L);
        attachment.setName("hhhh");
        List<Attachment> attachments = new ArrayList<>();
        attachments.add(attachment);

        DownloadScriptBean downloadScriptBean = new DownloadScriptBean();
        downloadScriptBean.setScriptName("123");
        downloadScriptBean.setAttachments(attachments);
        downloadScriptBean.setContent("367826378");
        List<DownloadScriptBean> downloadScriptBeanList = new ArrayList<>();
        downloadScriptBeanList.add(downloadScriptBean);

        when(scripts.getMyScriptMapper()).thenReturn(myScriptMapper);
        when(scripts.getAttachmentMapper()).thenReturn(attachmentMapper);

        when(scripts.getMyScriptMapper().getScriptListForDownload(any(Long[].class))).thenReturn(downloadScriptBeanList);

        final boolean result = myScriptServiceImplUnderTest.downloadScript(new Long[]{1L}, response);

        // Verify the results
        assertThat(result).isTrue();
    }

    @Test
    void testGenerateScriptFiles() throws ScriptException {
        // Setup
        final List<File> expectedResult = Collections.singletonList(new File("filename.txt"));


        Attachment attachment = new Attachment();
        attachment.setId(1L);
        attachment.setName("hhhh");
        List<Attachment> attachments = new ArrayList<>();
        attachments.add(attachment);

        DownloadScriptBean downloadScriptBean = new DownloadScriptBean();
        downloadScriptBean.setScriptName("123");
        downloadScriptBean.setAttachments(attachments);
        downloadScriptBean.setContent("367826378");
        List<DownloadScriptBean> downloadScriptBeanList = new ArrayList<>();
        downloadScriptBeanList.add(downloadScriptBean);

        when(scripts.getMyScriptMapper()).thenReturn(myScriptMapper);
        when(scripts.getAttachmentMapper()).thenReturn(attachmentMapper);

        when(scripts.getMyScriptMapper().getScriptListForDownload(any(Long[].class))).thenReturn(downloadScriptBeanList);

        // Run the test
        final List<File> result = myScriptServiceImplUnderTest.generateScriptFiles(new Long[]{1L});

        // Verify the results
        assertNotNull(result);
        //assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testDownloadScriptInfo() {
        // Setup
        // Run the test

        Attachment attachment = new Attachment();
        attachment.setId(1L);
        attachment.setName("hhhh");
        List<Attachment> attachments = new ArrayList<>();
        attachments.add(attachment);

        DownloadScriptBean downloadScriptBean = new DownloadScriptBean();
        downloadScriptBean.setScriptName("123");
        downloadScriptBean.setAttachments(attachments);
        downloadScriptBean.setContent("367826378");
        List<DownloadScriptBean> downloadScriptBeanList = new ArrayList<>();
        downloadScriptBeanList.add(downloadScriptBean);

        when(scripts.getMyScriptMapper()).thenReturn(myScriptMapper);
        when(scripts.getAttachmentMapper()).thenReturn(attachmentMapper);

        when(scripts.getMyScriptMapper().getScriptListForDownload(any(Long[].class))).thenReturn(downloadScriptBeanList);

        final List<DownloadScriptBean> result = myScriptServiceImplUnderTest.downloadScriptInfo(new Long[]{1L});
        assertNotNull(result);
        // Verify the results
    }

    @Test
    void testCreateFile() throws ScriptException {
        // Setup
        final Attachment attachment = new Attachment();
        attachment.setId(1L);
        attachment.setSrcScriptUuid("oldUuid");
        attachment.setName("name");
        attachment.setSize(1L);
        attachment.setContents("content".getBytes());
        final List<Attachment> attachmentList = Collections.singletonList(attachment);
        final File expectedResult = new File("filename.txt");

        String path = Thread.currentThread().getContextClassLoader().getResource("").getPath() + "/temp_" + format(new Date(), "yyyyMMddHHmmssms");
        // Run the test
        final File result = myScriptServiceImplUnderTest.createFile("fileName", "scriptContent", attachmentList, path, true);

        // Verify the results
        // assertThat(result).isEqualTo(expectedResult);
        assertNotNull(result);
    }

    @Test
    void testAttachmentCreateFile() throws ScriptException {
        // Setup
        final Attachment attachment = new Attachment();
        attachment.setId(1L);
        attachment.setSrcScriptUuid("oldUuid");
        attachment.setName("name");
        attachment.setSize(1L);
        attachment.setContents("content".getBytes());
        final List<Attachment> attachmentList = Collections.singletonList(attachment);
        final File expectedResult = new File("filename.txt");

        String path = Thread.currentThread().getContextClassLoader().getResource("").getPath() + "/temp_" + format(new Date(), "yyyyMMddHHmmssms");
        // Run the test
        final File result = myScriptServiceImplUnderTest.createFile("fileName", "scriptContent", attachmentList, path, false);

        // Verify the results
        // assertThat(result).isEqualTo(expectedResult);
        assertNotNull(result);
    }


    @Test
    void publishScript() throws ScriptException {

        PublishDto publishDto = new PublishDto();
        publishDto.setIds(new Long[]{1L});
        publishDto.setIsForbidden(1);
        List<InfoVersion> infoVersionList = new ArrayList<>();
        InfoVersion infoVersion = new InfoVersion();
        infoVersion.setSrcScriptUuid("srcScriptUuid");
        infoVersion.setInfoUniqueUuid("uniqueUuid");
        infoVersionList.add(infoVersion);

        Info info = new Info();
        info.setId(1L);
        info.setScriptNameZh("测试发布");
        info.setScriptName("testPublish");

        ResultApiDto resultApiDto = new ResultApiDto();
        resultApiDto.setTaskId(1L);
        resultApiDto.setSuccess(true);
        resultApiDto.setMessage("hhhhh");

        when(scripts.getInfoVersionMapper()).thenReturn(infoVersionMapper);
        when(scripts.getAuditRelationService()).thenReturn(auditRelationService);
        when(scripts.getInfoMapper()).thenReturn(infoMapper);
        when(scripts.getRemoteCall()).thenReturn(remoteCall);
        when(scripts.getScriptBusinessConfig()).thenReturn(scriptBusinessConfig);

        // 关键修复：添加对ScriptBusinessConfig的isPublishScriptWithoutDoubleCheck方法的mock
        when(scripts.getScriptBusinessConfig().isPublishScriptWithoutDoubleCheck()).thenReturn(false);

        when(scripts.getInfoVersionMapper().selectInfoVersionByIds(any(Long[].class))).thenReturn(infoVersionList);
        when(scripts.getInfoMapper().selectInfoByUniqueUuid(any(String.class))).thenReturn(info);
        when(scripts.getRemoteCall().applyForDoubleCheck(anyString(), any(DoubleCheckApiDto.class))).thenReturn(resultApiDto);
        when(scripts.getiUserInfo()).thenReturn(iUserInfo);
        List<Parameter> parameterList = new ArrayList<>();
        Parameter parameter = new Parameter();
        parameter.setParamName("a");
        parameter.setParamDefaultValue("a");
        parameter.setParamOrder(1);
        parameterList.add(parameter);

        when(scripts.getParameterMapper()).thenReturn(parameterMapper);

        when(scripts.getParameterMapper().getParameterByUuid(anyString())).thenReturn(parameterList);

        myScriptServiceImplUnderTest.publishScript(publishDto);

        verify(scripts.getInfoVersionMapper(), times(2)).selectInfoVersionByIds(any(Long[].class));


        when(scripts.getScriptBusinessConfig().isPublishScriptWithoutDoubleCheck()).thenReturn(true);

        DoubleCheckInfoDto doubleCheckInfoDto = new DoubleCheckInfoDto();
        doubleCheckInfoDto.setAuditRelationId(0L);
        Long workitemid = 123L;
//        when(myScriptServiceImplUnderTest.scriptSubmit(doubleCheckInfoDto, 1))
//                .thenReturn(workitemid);
        // 返回一个明确的大于0的值
        myScriptServiceImplUnderTest.scriptSubmit(doubleCheckInfoDto, 1);

    }


    @ParameterizedTest
    @NullSource
    @ValueSource(strings = {"1.0", "1.9"})
    void updateScriptReview(String version) throws SystemException {

        if(StringUtils.isBlank(version)){
            return;
        }

        AuditResultBean auditResultBean = new AuditResultBean();
        auditResultBean.setAuditRelationId(1L);
        auditResultBean.setState(2);
        auditResultBean.setApprWorkitemId(1L);


        AuditRelation auditRelation = new AuditRelation();
        auditRelation.setSrcScriptUuid("srcScriptUuid");
        auditRelation.setState(2);
        auditRelation.setBanOldVersion(0);

        //设置infoVersion查询信息
        InfoVersion infoVersion = new InfoVersion();
        infoVersion.setSrcScriptUuid("srcScriptUuid");
        infoVersion.setInfoUniqueUuid("uniqueUuid");

        //设置info查询信息
        Info info = new Info();
        info.setId(1L);


        when(scripts.getAuditRelationService()).thenReturn(auditRelationService);
        when(scripts.getAuditRelationMapper()).thenReturn(auditRelationMapper);
        when(scripts.getInfoVersionMapper()).thenReturn(infoVersionMapper);
        when(scripts.getMyScriptMapper()).thenReturn(myScriptMapper);
        when(scripts.getInfoMapper()).thenReturn(infoMapper);

        List<String> versionList = new ArrayList<>();
        versionList.add(version);

        when(scripts.getAuditRelationMapper().selectAuditRelationById(any(Long.class))).thenReturn(auditRelation);
        when(scripts.getMyScriptMapper().getLastVersion(anyString())).thenReturn(versionList);
        when(scripts.getInfoVersionMapper().selectInfoVersionBysrcScriptUuid(eq("srcScriptUuid"))).thenReturn(infoVersion);
        when(scripts.getInfoMapper().selectInfoByUniqueUuid(eq("uniqueUuid"))).thenReturn(info);


        myScriptServiceImplUnderTest.updateScriptReview(auditResultBean);

        verify(scripts.getInfoVersionMapper()).updateInfoVersionBySrcUuid(any(InfoVersion.class));
    }

    @ParameterizedTest
    @NullSource
    @ValueSource(strings = {"1.0", "1.9"})
    void updateScriptReview_banOldVersion(String version) throws SystemException {
        if(StringUtils.isBlank(version)){
            return;
        }
        AuditResultBean auditResultBean = new AuditResultBean();
        auditResultBean.setAuditRelationId(1L);
        auditResultBean.setState(2);
        auditResultBean.setApprWorkitemId(1L);


        AuditRelation auditRelation = new AuditRelation();
        auditRelation.setSrcScriptUuid("srcScriptUuid");
        auditRelation.setState(2);
        auditRelation.setBanOldVersion(1);
        //设置infoVersion查询信息
        InfoVersion infoVersion = new InfoVersion();
        infoVersion.setSrcScriptUuid("srcScriptUuid");
        infoVersion.setInfoUniqueUuid("uniqueUuid");

        //设置info查询信息
        Info info = new Info();
        info.setId(1L);


        when(scripts.getAuditRelationService()).thenReturn(auditRelationService);
        when(scripts.getAuditRelationMapper()).thenReturn(auditRelationMapper);
        when(scripts.getInfoVersionMapper()).thenReturn(infoVersionMapper);
        when(scripts.getMyScriptMapper()).thenReturn(myScriptMapper);
        when(scripts.getInfoMapper()).thenReturn(infoMapper);

        List<String> versionList = new ArrayList<>();
        versionList.add(version);

        when(scripts.getAuditRelationMapper().selectAuditRelationById(any(Long.class))).thenReturn(auditRelation);
        when(scripts.getMyScriptMapper().getLastVersion(anyString())).thenReturn(versionList);
        when(scripts.getInfoVersionMapper().selectInfoVersionBysrcScriptUuid(eq("srcScriptUuid"))).thenReturn(infoVersion);
        when(scripts.getInfoMapper().selectInfoByUniqueUuid(eq("uniqueUuid"))).thenReturn(info);
        when(scripts.getMyScriptMapper().getLastVersion(anyString())).thenReturn(versionList);

        myScriptServiceImplUnderTest.updateScriptReview(auditResultBean);

        verify(scripts.getInfoVersionMapper()).updateInfoVersionBySrcUuid(any(InfoVersion.class));
    }

    @ParameterizedTest
    @NullSource
    @ValueSource(strings = {"1.0", "1.9"})
    void updateScriptReview_rejected(String version) throws SystemException {

        AuditResultBean auditResultBean = new AuditResultBean();
        auditResultBean.setAuditRelationId(1L);
        auditResultBean.setState(2);
        auditResultBean.setApprWorkitemId(1L);


        AuditRelation auditRelation = new AuditRelation();
        auditRelation.setSrcScriptUuid("srcScriptUuid");
        auditRelation.setState(-1);
        auditRelation.setBanOldVersion(0);

        //设置infoVersion查询信息
        InfoVersion infoVersion = new InfoVersion();
        infoVersion.setSrcScriptUuid("srcScriptUuid");
        infoVersion.setInfoUniqueUuid("uniqueUuid");

        //设置info查询信息
        Info info = new Info();
        info.setId(1L);


        when(scripts.getAuditRelationService()).thenReturn(auditRelationService);
        when(scripts.getAuditRelationMapper()).thenReturn(auditRelationMapper);
        when(scripts.getInfoVersionMapper()).thenReturn(infoVersionMapper);
        when(scripts.getInfoMapper()).thenReturn(infoMapper);

        when(scripts.getAuditRelationMapper().selectAuditRelationById(any(Long.class))).thenReturn(auditRelation);
        when(scripts.getInfoVersionMapper().selectInfoVersionBysrcScriptUuid(eq("srcScriptUuid"))).thenReturn(infoVersion);

        when(scripts.getInfoMapper().selectInfoByUniqueUuid(eq("uniqueUuid"))).thenReturn(info);

        myScriptServiceImplUnderTest.updateScriptReview(auditResultBean);

        verify(scripts.getInfoVersionMapper()).updateInfoVersionBySrcUuid(any(InfoVersion.class));
    }


    @Test
    void doubleCheckScriptCallBack() throws JsonProcessingException, SystemException, ParseException {
        // 创建一个模拟的 JSON 字符串
        String json = "{\n" +
                "  \"id\": 123,\n" +
                "  \"serviceId\": 456,\n" +
                "  \"taskSubject\": \"Your Task Subject\",\n" +
                "  \"detailUrl\": \"Your Detail URL\",\n" +
                "  \"originatorName\": \"Originator Name\",\n" +
                "  \"originatorId\": 789,\n" +
                "  \"originateTime\": \"2024-04-11 12:00:00\",\n" +
                "  \"auditTime\": \"2024-04-11 13:00:00\",\n" +
                "  \"approvalComment\": \"Your Approval Comment\",\n" +
                "  \"itemType\": 1,\n" +
                "  \"approvalState\": 2,\n" +
                "  \"callbackUrl\": \"Your Callback URL\"\n" +
                "}";


        // 创建一个模拟的 DoubleCheckApiDto 对象
        DoubleCheckApiDto doubleCheckApiDto = new DoubleCheckApiDto();
        // 设置 DoubleCheckApiDto 的属性值
        // doubleCheckApiDto.setXXX(...);

        // 创建一个模拟的 AuditResultBean 对象
        AuditResultBean auditResultBean = new AuditResultBean();
        // 设置 AuditResultBean 的属性值
        // auditResultBean.setXXX(...);

        // 设置模拟方法调用的返回值
        //when(objectMapper.readValue(anyString(), eq(DoubleCheckApiDto.class))).thenReturn(doubleCheckApiDto);
        when(scripts.getAuditRelationService()).thenReturn(auditRelationService);
        when(scripts.getAuditRelationMapper()).thenReturn(auditRelationMapper);
        when(scripts.getMyScriptMapper()).thenReturn(myScriptMapper);
        when(scripts.getInfoVersionMapper()).thenReturn(infoVersionMapper);
        when(scripts.getInfoMapper()).thenReturn(infoMapper);

        AuditRelation auditRelation = new AuditRelation();
        auditRelation.setSrcScriptUuid("srcScriptUuid");
        auditRelation.setState(2);
        auditRelation.setBanOldVersion(0);

        doubleCheckApiDto.setApprovalState(1);
        doubleCheckApiDto.setServiceId(456L);
        doubleCheckApiDto.setId(123L);
        doubleCheckApiDto.setTaskSubject("Your Task Subject");
        doubleCheckApiDto.setDetailUrl("Your Detail URL");
        doubleCheckApiDto.setOriginatorName("Originator Name");
        doubleCheckApiDto.setOriginatorId(789L);
        String dateString = "2024-04-11 12:00:00";
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date expectedDate = dateFormat.parse(dateString);
        doubleCheckApiDto.setOriginateTime(expectedDate);
        String dateString1 = "2024-04-11 13:00:00";
        Date expectedDate1 = dateFormat.parse(dateString1);
        doubleCheckApiDto.setAuditTime(expectedDate1);
        doubleCheckApiDto.setApprovalComment("Your Approval Comment");
        doubleCheckApiDto.setItemType("1");
        doubleCheckApiDto.setCallbackUrl("Your Callback URL");


        //设置infoVersion查询信息
        InfoVersion infoVersion = new InfoVersion();
        infoVersion.setSrcScriptUuid("srcScriptUuid");
        infoVersion.setInfoUniqueUuid("uniqueUuid");

        //设置info查询信息
        Info info = new Info();
        info.setId(1L);

        when(scripts.getInfoVersionMapper().selectInfoVersionBysrcScriptUuid(eq("srcScriptUuid"))).thenReturn(infoVersion);

        when(scripts.getAuditRelationMapper().selectAuditRelationById(any(Long.class))).thenReturn(auditRelation);
        when(scripts.getInfoMapper().selectInfoByUniqueUuid(eq("uniqueUuid"))).thenReturn(info);

        // 调用被测试方法
        myScriptServiceImplUnderTest.doubleCheckScriptCallBack(doubleCheckApiDto, 1);

        // 验证方法是否按预期调用了 updateScriptReview 方法
        verify(scripts.getAuditRelationMapper(), times(1)).selectAuditRelationById(any(Long.class));
    }

    @Test
    void paramsNameExistCheck() throws Exception {
        ScriptInfoDto scriptInfoDto = new ScriptInfoDto();
        ScriptVersionDto scriptVersionDto = new ScriptVersionDto();
        ParameterValidationDto[] parameterValidationDtos = new ParameterValidationDto[2];
        ParameterValidationDto parameterValidationDto = new ParameterValidationDto();
        parameterValidationDto.setParamName("test1");
        ParameterValidationDto parameterValidationDtoSec = new ParameterValidationDto();
        parameterValidationDtoSec.setParamName("test1");
        parameterValidationDtos[0] = parameterValidationDto;
        parameterValidationDtos[1] = parameterValidationDtoSec;
        List<ParameterValidationDto> parameterValidationDtoList = new ArrayList<>();
        parameterValidationDtoList.add(parameterValidationDto);
        scriptVersionDto.setParameterValidationDtoList(parameterValidationDtoList);
        scriptInfoDto.setScriptVersionDto(scriptVersionDto);
        boolean b = myScriptServiceImplUnderTest.paramsNameExistCheck(scriptInfoDto);
        assertNotNull(b);
    }

    @Test
    void checkAvailableScript() throws ScriptException {
        Info info = new Info();
        info.setEditState(1);
        info.setUniqueUuid("uniqueUuid");

        when(scripts.getInfoVersionMapper()).thenReturn(infoVersionMapper);

        when(scripts.getInfoVersionMapper().selectLastEnableInfoVersion("uniqueUuid")).thenReturn(null);

        assertThrows(ScriptException.class, () -> {
            myScriptServiceImplUnderTest.checkAvailableScript(info);
        });

    }

    @Test
    void updateDoubleCheckForDel() {

        InfoVersion infoVersion = new InfoVersion();
        infoVersion.setSrcScriptUuid("srcScriptUUid");


        ScriptDeleteDto scriptDeleteDto = new ScriptDeleteDto();
        scriptDeleteDto.setAuditorId(1L);
        scriptDeleteDto.setAuditorName("admin");


        when(scripts.getAuditRelationService()).thenReturn(auditRelationService);
        when(scripts.getInfoVersionMapper()).thenReturn(infoVersionMapper);

        when(scripts.getAuditRelationService().insertAuditRelation(any())).thenReturn(1);
        when(scripts.getInfoVersionMapper().updateInfoVersion(any())).thenReturn(1);


        myScriptServiceImplUnderTest.updateDoubleCheckForDel(infoVersion, scriptDeleteDto);
        verify(scripts.getAuditRelationService(), times(1)).insertAuditRelation(any());
    }

    @Test
    void updateScriptReviewForDelete_approval() throws SystemException {

        //组织数据
        AuditResultBean auditResultBean = new AuditResultBean();
        auditResultBean.setAuditRelationId(1L);
        auditResultBean.setState(2);
        auditResultBean.setApprWorkitemId(2L);

        AuditRelation auditRelation = new AuditRelation();
        auditRelation.setState(2);
        auditRelation.setSrcScriptUuid("srcScriptUuid");

        InfoVersion infoVersion = new InfoVersion();
        infoVersion.setId(1L);
        infoVersion.setInfoUniqueUuid("uniqueUuid");
        infoVersion.setIsDefault(1);

        Info info = new Info();
        info.setId(1L);

        when(scripts.getAuditRelationService()).thenReturn(auditRelationService);
        when(scripts.getAuditRelationMapper()).thenReturn(auditRelationMapper);
        when(scripts.getInfoVersionMapper()).thenReturn(infoVersionMapper);
        when(scripts.getInfoMapper()).thenReturn(infoMapper);
        when(scripts.getInfoVersionTextMapper()).thenReturn(infoVersionTextMapper);
        when(scripts.getParameterMapper()).thenReturn(parameterMapper);
        when(scripts.getAttachmentMapper()).thenReturn(attachmentMapper);

        when(scripts.getAuditRelationService().updateAuditRelation(any())).thenReturn(1);
        when(scripts.getAuditRelationMapper().selectAuditRelationById(eq(1L))).thenReturn(auditRelation);
        when(scripts.getInfoVersionMapper().selectInfoVersionBysrcScriptUuid(any())).thenReturn(infoVersion);
        when(scripts.getInfoVersionMapper().selectInfoVersionById(1L)).thenReturn(infoVersion);
        when(scripts.getInfoVersionMapper().getCountVersionForPublish("uniqueUuid")).thenReturn(1);
        when(scripts.getInfoMapper().selectInfoByUniqueUuid("uniqueUuid")).thenReturn(info);
        when(scripts.getInfoVersionMapper().selectInfoVersionBysrcScriptUuid("srcScriptUuid")).thenReturn(infoVersion);
        when(scripts.getInfoVersionTextMapper().deleteInfoVersionTextByScriptUuids(any())).thenReturn(1);
        when(scripts.getParameterMapper().deleteParameterByScriptUuids(any())).thenReturn(1);
        when(scripts.getAttachmentMapper().deleteAttachmentByScriptUuids(any())).thenReturn(1);

        myScriptServiceImplUnderTest.updateScriptReviewForDelete(auditResultBean);

        verify(scripts.getInfoVersionMapper(), times(2)).selectInfoVersionBysrcScriptUuid(any());

    }


    @Test
    void updateScriptReviewForDelete_rejected() throws SystemException {

        //组织数据
        AuditResultBean auditResultBean = new AuditResultBean();
        auditResultBean.setAuditRelationId(1L);
        auditResultBean.setState(3);
        auditResultBean.setApprWorkitemId(2L);

        AuditRelation auditRelation = new AuditRelation();
        auditRelation.setState(3);
        auditRelation.setSrcScriptUuid("srcScriptUuid");

        InfoVersion infoVersion = new InfoVersion();
        infoVersion.setId(1L);
        infoVersion.setInfoUniqueUuid("uniqueUuid");
        infoVersion.setIsDefault(1);

        when(scripts.getAuditRelationService()).thenReturn(auditRelationService);
        when(scripts.getAuditRelationMapper()).thenReturn(auditRelationMapper);
        when(scripts.getInfoVersionMapper()).thenReturn(infoVersionMapper);


        when(scripts.getAuditRelationService().updateAuditRelation(any())).thenReturn(1);
        when(scripts.getAuditRelationMapper().selectAuditRelationById(eq(1L))).thenReturn(auditRelation);
        when(scripts.getInfoVersionMapper().selectInfoVersionBysrcScriptUuid(any())).thenReturn(infoVersion);
        when(scripts.getInfoVersionMapper().selectInfoVersionBysrcScriptUuid("srcScriptUuid")).thenReturn(infoVersion);

        myScriptServiceImplUnderTest.updateScriptReviewForDelete(auditResultBean);

        verify(scripts.getAuditRelationService()).updateAuditRelation(any());
    }

    @ParameterizedTest
    @ValueSource(booleans = {true, false})
    void getDefaultVersionIds(boolean type) {

        Long[] longs = new Long[]{1L};

        List<Long> longList = new ArrayList<>();
        longList.add(1L);

        when(scripts.getInfoMapper()).thenReturn(infoMapper);

        if (type) {
            when(scripts.getInfoMapper().getDefaultVersionIdsByScriptId(longs)).thenReturn(longList);
        } else {
            when(scripts.getInfoMapper().getDefaultVersionIdsByScriptId(longs)).thenReturn(new ArrayList<>());
        }

        Long[] defaultVersionIds = myScriptServiceImplUnderTest.getDefaultVersionIds(longs);

        assertNotNull(defaultVersionIds);
    }

    @Test
    void getUserInfoList() {

        when(scripts.getiUserInfo()).thenReturn(iUserInfo);
        when(scripts.getiUserInfo().getUserInfoByLoginNameForApi(any())).thenReturn(new PageInfo<>());
        UserInfoQueryDto userInfoQueryDto = new UserInfoQueryDto();
        userInfoQueryDto.setQueryParam(new UserInfoApiDto());
        userInfoQueryDto.getQueryParam().setFuzzySearch(true);
        PageInfo<UserInfoApiDto> userInfoList = myScriptServiceImplUnderTest.getUserInfoList(userInfoQueryDto);
        assertNotNull(userInfoList);
    }

    @Test
    void creatorTransfer() {
        CreatorTransferDto creatorTransferDto = new CreatorTransferDto();
        UserInfoApiDto userInfoApiDto = new UserInfoApiDto();
        userInfoApiDto.setId(1L);
        userInfoApiDto.setFullName("admin");
        creatorTransferDto.setUserInfoApiDto(userInfoApiDto);
        List<String> uuidList = new ArrayList<>();
        uuidList.add("infoUuid");
        creatorTransferDto.setInfoUuids(uuidList);

        when(scripts.getInfoMapper()).thenReturn(infoMapper);
        when(scripts.getInfoVersionMapper()).thenReturn(infoVersionMapper);


        myScriptServiceImplUnderTest.creatorTransfer(creatorTransferDto);

        verify(scripts.getInfoMapper()).updateInfosByUuid(any(), anyList());
        verify(scripts.getInfoVersionMapper()).updateInfoVersionByInfoUuid(any(), anyList());

    }

    @Test
    void selectScriptList() {
        // Setup
        ScriptInfoQueryDto scriptInfoQueryDto = new ScriptInfoQueryDto();
        scriptInfoQueryDto.setCategoryId(1L);
        scriptInfoQueryDto.setPageNum(1);
        scriptInfoQueryDto.setPageSize(50);

        MyScriptBean myScriptBean = new MyScriptBean();
        myScriptBean.setScriptNameZh("测试分类");
        myScriptBean.setScriptName("test");
        myScriptBean.setCategory("一级分类/二级分类");
        myScriptBean.setUniqueUuid("uniqueUuid");
        Page<MyScriptBean> page = new Page<>();
        page.add(myScriptBean);

        // Mock CurrentUserUtil
        CurrentUser currentUser = new CurrentUser();
        currentUser.setId(1L);
        currentUser.setLoginName("test");
        currentUser.setFullName("测试用户");
        currentUser.setOrgId(1L);
        currentUser.setOrgCode("orgCode1#");
        currentUser.setSupervisor(false);
        currentUserUtilMockedStatic.when(CurrentUserUtil::getCurrentUser).thenReturn(currentUser);

        // Mock necessary services
        when(scripts.getCategoryService()).thenReturn(categoryService);
        when(scripts.getMyScriptMapper()).thenReturn(myScriptMapper);
        when(scripts.getParameterService()).thenReturn(parameterService);
        when(scripts.getCategoryMapper()).thenReturn(categoryMapper);
        when(scripts.getScriptBusinessConfig()).thenReturn(scriptBusinessConfig);

        // Mock specific method calls
        when(scripts.getScriptBusinessConfig().getPermissionPolicy()).thenReturn("creator");
        when(scripts.getCategoryService().buildCategoryPath(any(Category.class))).thenReturn("一级分类/二级分类");
        when(scripts.getCategoryService().handleCategoryPath(anyString())).thenReturn("一级分类/二级分类");
        when(scripts.getMyScriptMapper().selectMyScriptList(any())).thenReturn(page);

        // Run the test
        final List<ScriptInfoApiDto> result = myScriptServiceImplUnderTest.selectScriptList(scriptInfoQueryDto);

        // Verify
        assertNotNull(result);
    }

    @ParameterizedTest
    @ValueSource(strings = {"srcScriptUuid"})
    @NullSource
    void getDefaultScriptInfoApi(String srcScriptUuid) throws ScriptException {
        ScriptInfoQueryDto scriptInfoQueryDto = new ScriptInfoQueryDto();
        scriptInfoQueryDto.setSrcScriptUuid(srcScriptUuid);
        scriptInfoQueryDto.setScriptInfoId(1L);
        scriptInfoQueryDto.setInfoUniqueUuid("infoUniqueUuid");
        scriptInfoQueryDto.setCategoryId(1L);

        ScriptVersionDto infoVersionDto = new ScriptVersionDto();
        infoVersionDto.setInfoUniqueUuid("infoUniqueUuid");
        infoVersionDto.setSrcScriptUuid("srcScriptUuid");

        Info info = new Info();
        info.setId(1L);
        info.setUniqueUuid("infoUniqueUuid");
        info.setPlatform("Linux,Unix");
        info.setCategoryId(1L);


        InfoVersion infoVersion = new InfoVersion();
        infoVersion.setId(1L);
        infoVersion.setSrcScriptUuid("srcScriptUuid");
        infoVersion.setInfoUniqueUuid("infoUniqueUuid");
        infoVersion.setEditState(0);


        Category category = new Category();
        category.setName("一级");
        category.setId(1L);

        List<CategoryApiDto> categoryApiDtoList = new ArrayList<>();
        CategoryApiDto categoryApiDto = new CategoryApiDto();
        categoryApiDto.setId(2L);
        categoryApiDto.setName("二级");
        categoryApiDto.setLevel(2);
        categoryApiDto.setParentId(1L);
        categoryApiDtoList.add(categoryApiDto);
        category.setChildren(categoryApiDtoList);

        InfoVersionText infoVersionText = new InfoVersionText();
        infoVersionText.setId(1L);
        infoVersionText.setContent("echo 0");
        infoVersionText.setSrcScriptUuid("srcScriptUuid");


        List<ParameterValidationDto> parameterValidationDtoList = new ArrayList<>();
        ParameterValidationDto parameterValidationDto = new ParameterValidationDto();
        parameterValidationDto.setParamName("参数一");
        parameterValidationDto.setId(1L);
        parameterValidationDto.setParamCheckIid(1L);
        parameterValidationDto.setParamDefaultValue("aaaa");
        parameterValidationDto.setCheckRule("^\\d+$");
        parameterValidationDtoList.add(parameterValidationDto);

        if (srcScriptUuid != null) {
            when(scripts.getInfoVersionService()).thenReturn(infoVersionService);
            when(scripts.getInfoMapper()).thenReturn(infoMapper);
            when(scripts.getInfoVersionMapper()).thenReturn(infoVersionMapper);
            when(scripts.getCategoryService()).thenReturn(categoryService);
            when(scripts.getInfoVersionTextMapper()).thenReturn(infoVersionTextMapper);
            when(scripts.getParameterService()).thenReturn(parameterService);
            when(scripts.getAttachmentService()).thenReturn(attachmentService);
            when(scripts.getBindFuncVarService()).thenReturn(bindFuncVarService);

            when(scripts.getInfoVersionService().getInfoDefaultVersionByUuid(eq("infoUniqueUuid"))).thenReturn(infoVersionDto);
            when(scripts.getInfoVersionService().selectInfoVersionBySrcScriptUuid(eq("srcScriptUuid"))).thenReturn(infoVersionDto);
            when(scripts.getCategoryService().getCategory(eq(1L))).thenReturn(category);
            when(scripts.getCategoryService().getCategoryFullPath(eq(1L))).thenReturn("一级/二级");
            when(scripts.getInfoVersionTextMapper().selectInfoVersionTextByScriptUuid(eq("srcScriptUuid"))).thenReturn(infoVersionText);
            when(scripts.getParameterService().getParameterValidationDtos(any(InfoVersion.class))).thenReturn(parameterValidationDtoList);
            when(scripts.getAttachmentService().getAttachmentUploadDtos(any(InfoVersion.class))).thenReturn(new ArrayList<>());
            when(scripts.getInfoVersionMapper().selectInfoVersionBysrcScriptUuid(any())).thenReturn(infoVersion);
            when(scripts.getInfoMapper().selectInfoByUniqueUuid(any())).thenReturn(info);
        }

        ScriptDubboInfoDto defaultScriptInfoApi = myScriptServiceImplUnderTest.getDefaultScriptInfoApi(scriptInfoQueryDto);

        assertNotNull(defaultScriptInfoApi);
    }

    @Test
    void testHandleIncreaseLabel() throws Exception {
        // 创建测试实例
        MyScriptServiceImpl serviceImpl = new MyScriptServiceImpl(doubleCheckApiService, attachmentEphemeralMapper, mockBuilder,releaseMediaService,taskAuthorityService,cibProperties,afterScriptExecAuditing);

        // 使用反射获取私有方法
        java.lang.reflect.Method handleIncreaseLabelMethod = MyScriptServiceImpl.class.getDeclaredMethod("handleIncreaseLabel", ScriptInfoDto.class);
        handleIncreaseLabelMethod.setAccessible(true);

        // 准备测试数据
        ScriptInfoDto scriptInfoDto = new ScriptInfoDto();
        List<String> insertedLabels = new ArrayList<>();
        insertedLabels.add("标签1");
        insertedLabels.add("标签2");
        scriptInfoDto.setInsertedLabels(insertedLabels);

        // 模拟RedissonClient及其相关方法
        org.redisson.api.RKeys rKeys = Mockito.mock(org.redisson.api.RKeys.class);
        org.redisson.api.RScript rScript = Mockito.mock(org.redisson.api.RScript.class);

        when(scripts.getRedissonClient()).thenReturn(mockRedissonClient);
        when(mockRedissonClient.getKeys()).thenReturn(rKeys);
        when(mockRedissonClient.getScript()).thenReturn(rScript);
        when(rKeys.countExists(anyString())).thenReturn(1L); // 模拟Redis key存在

        // 设置scripts字段
        Field scriptsField = MyScriptServiceImpl.class.getDeclaredField("scripts");
        scriptsField.setAccessible(true);
        scriptsField.set(serviceImpl, scripts);

        // 执行私有方法
        handleIncreaseLabelMethod.invoke(serviceImpl, scriptInfoDto);

        // 验证方法调用
        verify(mockRedissonClient, times(1)).getKeys();
        verify(rKeys, times(1)).countExists("script_labelZSet");
        verify(mockRedissonClient, times(1)).getScript();
        verify(rScript, times(1)).eval(any(), any(), any(), any(), any());
    }

    @Test
    void testHandleIncreaseLabel_EmptyInsertedLabels() throws Exception {
        // 创建测试实例
        MyScriptServiceImpl serviceImpl = new MyScriptServiceImpl(doubleCheckApiService, attachmentEphemeralMapper, mockBuilder,releaseMediaService,taskAuthorityService,cibProperties,afterScriptExecAuditing);

        // 使用反射获取私有方法
        java.lang.reflect.Method handleIncreaseLabelMethod = MyScriptServiceImpl.class.getDeclaredMethod("handleIncreaseLabel", ScriptInfoDto.class);
        handleIncreaseLabelMethod.setAccessible(true);

        // 准备测试数据 - 空标签列表
        ScriptInfoDto scriptInfoDto = new ScriptInfoDto();
        scriptInfoDto.setInsertedLabels(new ArrayList<>());

        // 设置scripts字段
        Field scriptsField = MyScriptServiceImpl.class.getDeclaredField("scripts");
        scriptsField.setAccessible(true);
        scriptsField.set(serviceImpl, scripts);

        // 执行私有方法
        handleIncreaseLabelMethod.invoke(serviceImpl, scriptInfoDto);

        // 验证方法调用 - 不应该调用Redis相关方法
        verify(mockRedissonClient, times(0)).getKeys();
    }

    @Test
    void testHandleIncreaseLabel_RedisException() throws Exception {
        // 创建测试实例
        MyScriptServiceImpl serviceImpl = new MyScriptServiceImpl(doubleCheckApiService, attachmentEphemeralMapper, mockBuilder,releaseMediaService,taskAuthorityService,cibProperties,afterScriptExecAuditing);

        // 使用反射获取私有方法
        java.lang.reflect.Method handleIncreaseLabelMethod = MyScriptServiceImpl.class.getDeclaredMethod("handleIncreaseLabel", ScriptInfoDto.class);
        handleIncreaseLabelMethod.setAccessible(true);

        // 准备测试数据
        ScriptInfoDto scriptInfoDto = new ScriptInfoDto();
        List<String> insertedLabels = new ArrayList<>();
        insertedLabels.add("标签1");
        scriptInfoDto.setInsertedLabels(insertedLabels);

        // 模拟RedissonClient及其相关方法
        org.redisson.api.RKeys rKeys = Mockito.mock(org.redisson.api.RKeys.class);
        org.redisson.api.RScript rScript = Mockito.mock(org.redisson.api.RScript.class);

        when(scripts.getRedissonClient()).thenReturn(mockRedissonClient);
        when(mockRedissonClient.getKeys()).thenReturn(rKeys);
        when(mockRedissonClient.getScript()).thenReturn(rScript);
        when(rKeys.countExists(anyString())).thenReturn(1L); // 模拟Redis key存在

        // 使用doThrow()来模拟抛出异常，这样能确保匹配任何参数的调用
        doThrow(new RuntimeException("Redis error")).when(rScript).eval(any(), any(), any(), any(), any());

        // 设置scripts字段
        Field scriptsField = MyScriptServiceImpl.class.getDeclaredField("scripts");
        scriptsField.setAccessible(true);
        scriptsField.set(serviceImpl, scripts);

        // 执行私有方法并验证异常
        try {
            handleIncreaseLabelMethod.invoke(serviceImpl, scriptInfoDto);
            fail("Expected InvocationTargetException was not thrown");
        } catch (java.lang.reflect.InvocationTargetException e) {
            assertTrue(e.getCause() instanceof ScriptException);
            assertEquals("handleRedisLabel.error", e.getCause().getMessage());
        }
    }

    @Test
    void testHandeleRemoveLabel() throws Exception {
        // 创建测试实例
        MyScriptServiceImpl serviceImpl = new MyScriptServiceImpl(doubleCheckApiService, attachmentEphemeralMapper, mockBuilder,releaseMediaService,taskAuthorityService,cibProperties,afterScriptExecAuditing);

        // 使用反射获取私有方法
        java.lang.reflect.Method handeleRemoveLabelMethod = MyScriptServiceImpl.class.getDeclaredMethod("handeleRemoveLabel", ScriptInfoDto.class);
        handeleRemoveLabelMethod.setAccessible(true);

        // 准备测试数据
        ScriptInfoDto scriptInfoDto = new ScriptInfoDto();
        List<String> removedLabels = new ArrayList<>();
        removedLabels.add("标签1");
        removedLabels.add("标签2");
        scriptInfoDto.setRemovedLabels(removedLabels);

        // 模拟RedissonClient及其相关方法
        org.redisson.api.RKeys rKeys = Mockito.mock(org.redisson.api.RKeys.class);
        org.redisson.api.RScript rScript = Mockito.mock(org.redisson.api.RScript.class);

        when(scripts.getRedissonClient()).thenReturn(mockRedissonClient);
        when(mockRedissonClient.getKeys()).thenReturn(rKeys);
        when(mockRedissonClient.getScript()).thenReturn(rScript);
        when(rKeys.countExists(anyString())).thenReturn(1L); // 模拟Redis key存在

        // 模拟Lua脚本执行返回的已删除标签列表
        List<String> deletedLabels = new ArrayList<>();
        deletedLabels.add("标签1");
        when(rScript.eval(any(), any(), any(), any(), any())).thenReturn(deletedLabels);

        // 模拟DangerCmdService
        when(scripts.getDangerCmdService()).thenReturn(Mockito.mock(DangerCmdServiceImpl.class));

        // 设置scripts字段
        Field scriptsField = MyScriptServiceImpl.class.getDeclaredField("scripts");
        scriptsField.setAccessible(true);
        scriptsField.set(serviceImpl, scripts);

        // 执行私有方法
        handeleRemoveLabelMethod.invoke(serviceImpl, scriptInfoDto);

        // 验证方法调用
        verify(mockRedissonClient, times(1)).getKeys();
        verify(rKeys, times(1)).countExists("script_labelZSet");
        verify(mockRedissonClient, times(1)).getScript();
        verify(rScript, times(1)).eval(any(), any(), any(), any(), any());
        verify(scripts.getDangerCmdService(), times(1)).updateDangerCmdByLabel(deletedLabels);
    }

    @Test
    void testHandeleRemoveLabel_EmptyRemovedLabels() throws Exception {
        // 创建测试实例
        MyScriptServiceImpl serviceImpl = new MyScriptServiceImpl(doubleCheckApiService, attachmentEphemeralMapper, mockBuilder,releaseMediaService,taskAuthorityService,cibProperties,afterScriptExecAuditing);

        // 使用反射获取私有方法
        java.lang.reflect.Method handeleRemoveLabelMethod = MyScriptServiceImpl.class.getDeclaredMethod("handeleRemoveLabel", ScriptInfoDto.class);
        handeleRemoveLabelMethod.setAccessible(true);

        // 准备测试数据 - 空标签列表
        ScriptInfoDto scriptInfoDto = new ScriptInfoDto();
        scriptInfoDto.setRemovedLabels(new ArrayList<>());

        // 设置scripts字段
        Field scriptsField = MyScriptServiceImpl.class.getDeclaredField("scripts");
        scriptsField.setAccessible(true);
        scriptsField.set(serviceImpl, scripts);

        // 执行私有方法
        handeleRemoveLabelMethod.invoke(serviceImpl, scriptInfoDto);

        // 验证方法调用 - 不应该调用Redis相关方法
        verify(mockRedissonClient, times(0)).getKeys();
    }

    @Test
    void testHandeleRemoveLabel_RedisException() throws Exception {
        // 创建测试实例和mock对象
        MyScriptServiceImpl serviceImpl = new MyScriptServiceImpl(doubleCheckApiService, attachmentEphemeralMapper, mockBuilder,releaseMediaService,taskAuthorityService,cibProperties,afterScriptExecAuditing);

        // 使用反射获取私有方法
        java.lang.reflect.Method handeleRemoveLabelMethod = MyScriptServiceImpl.class.getDeclaredMethod("handeleRemoveLabel", ScriptInfoDto.class);
        handeleRemoveLabelMethod.setAccessible(true);

        // 准备测试数据
        ScriptInfoDto scriptInfoDto = new ScriptInfoDto();
        List<String> removedLabels = new ArrayList<>();
        removedLabels.add("标签1");
        scriptInfoDto.setRemovedLabels(removedLabels);

        // 模拟RedissonClient及其相关方法
        org.redisson.api.RKeys rKeys = Mockito.mock(org.redisson.api.RKeys.class);
        org.redisson.api.RScript rScript = Mockito.mock(org.redisson.api.RScript.class);

        when(scripts.getRedissonClient()).thenReturn(mockRedissonClient);
        when(mockRedissonClient.getKeys()).thenReturn(rKeys);
        when(mockRedissonClient.getScript()).thenReturn(rScript);
        when(rKeys.countExists(anyString())).thenReturn(1L); // 模拟Redis key存在

        // 用通配符匹配所有参数，简化eval方法的模拟
        when(rScript.eval(any(), any(), any(), any(), any())).thenThrow(new RuntimeException("Redis error"));

        // 设置scripts字段
        Field scriptsField = MyScriptServiceImpl.class.getDeclaredField("scripts");
        scriptsField.setAccessible(true);
        scriptsField.set(serviceImpl, scripts);

        // 执行私有方法并验证异常
        try {
            handeleRemoveLabelMethod.invoke(serviceImpl, scriptInfoDto);
            fail("Expected InvocationTargetException was not thrown");
        } catch (java.lang.reflect.InvocationTargetException e) {
            assertTrue(e.getCause() instanceof ScriptException);
            assertEquals("handleRedisLabel.error", e.getCause().getMessage());
        }
    }

    @Test
    void testHandeleRemoveLabel_NoDeletedLabels() throws Exception {
        // 创建测试实例
        MyScriptServiceImpl serviceImpl = new MyScriptServiceImpl(doubleCheckApiService, attachmentEphemeralMapper, mockBuilder,releaseMediaService,taskAuthorityService,cibProperties,afterScriptExecAuditing);

        // 使用反射获取私有方法
        java.lang.reflect.Method handeleRemoveLabelMethod = MyScriptServiceImpl.class.getDeclaredMethod("handeleRemoveLabel", ScriptInfoDto.class);
        handeleRemoveLabelMethod.setAccessible(true);

        // 准备测试数据
        ScriptInfoDto scriptInfoDto = new ScriptInfoDto();
        List<String> removedLabels = new ArrayList<>();
        removedLabels.add("标签1");
        scriptInfoDto.setRemovedLabels(removedLabels);

        // 模拟RedissonClient及其相关方法
        org.redisson.api.RKeys rKeys = Mockito.mock(org.redisson.api.RKeys.class);
        org.redisson.api.RScript rScript = Mockito.mock(org.redisson.api.RScript.class);

        when(scripts.getRedissonClient()).thenReturn(mockRedissonClient);
        when(mockRedissonClient.getKeys()).thenReturn(rKeys);
        when(mockRedissonClient.getScript()).thenReturn(rScript);
        when(rKeys.countExists(anyString())).thenReturn(1L); // 模拟Redis key存在

        // 模拟Lua脚本执行返回空的已删除标签列表
        List<String> emptyDeletedLabels = new ArrayList<>();
        when(rScript.eval(any(), any(), any(), any(), any())).thenReturn(emptyDeletedLabels);

        // 设置scripts字段
        Field scriptsField = MyScriptServiceImpl.class.getDeclaredField("scripts");
        scriptsField.setAccessible(true);
        scriptsField.set(serviceImpl, scripts);

        // 执行私有方法
        handeleRemoveLabelMethod.invoke(serviceImpl, scriptInfoDto);

        // 验证方法调用 - 不应该调用updateDangerCmdByLabel方法
        verify(rScript, times(1)).eval(any(), any(), any(), any(), any());
        verify(scripts, times(0)).getDangerCmdService();
    }

    @Test
    void testParametersEqual_StringType() throws Exception {
        // 创建测试实例
        MyScriptServiceImpl serviceImpl = new MyScriptServiceImpl(doubleCheckApiService, attachmentEphemeralMapper, mockBuilder,releaseMediaService,taskAuthorityService,cibProperties,afterScriptExecAuditing);

        // 使用反射获取私有方法
        java.lang.reflect.Method parametersEqualMethod = MyScriptServiceImpl.class.getDeclaredMethod("parametersEqual", Parameter.class, Parameter.class);
        parametersEqualMethod.setAccessible(true);

        // 准备测试数据 - 相同的String类型参数
        Parameter parameter1 = new Parameter();
        parameter1.setParamType("String");
        parameter1.setParamName("testParam");
        parameter1.setParamCheckIid(1L);
        parameter1.setParamDesc("描述");
        parameter1.setParamDefaultValue("默认值");

        Parameter parameter2 = new Parameter();
        parameter2.setParamType("String");
        parameter2.setParamName("testParam");
        parameter2.setParamCheckIid(1L);
        parameter2.setParamDesc("描述");
        parameter2.setParamDefaultValue("默认值");

        // 执行私有方法
        Object result = parametersEqualMethod.invoke(serviceImpl, parameter1, parameter2);

        // 验证结果
        assertTrue((Boolean) result);
    }

    @Test
    void testParametersEqual_StringType_Different() throws Exception {
        // 创建测试实例
        MyScriptServiceImpl serviceImpl = new MyScriptServiceImpl(doubleCheckApiService, attachmentEphemeralMapper, mockBuilder,releaseMediaService,taskAuthorityService,cibProperties,afterScriptExecAuditing);

        // 使用反射获取私有方法
        java.lang.reflect.Method parametersEqualMethod = MyScriptServiceImpl.class.getDeclaredMethod("parametersEqual", Parameter.class, Parameter.class);
        parametersEqualMethod.setAccessible(true);

        // 准备测试数据 - 不同的String类型参数
        Parameter parameter1 = new Parameter();
        parameter1.setParamType("String");
        parameter1.setParamName("testParam");
        parameter1.setParamCheckIid(1L);
        parameter1.setParamDesc("描述");
        parameter1.setParamDefaultValue("默认值1");

        Parameter parameter2 = new Parameter();
        parameter2.setParamType("String");
        parameter2.setParamName("testParam");
        parameter2.setParamCheckIid(1L);
        parameter2.setParamDesc("描述");
        parameter2.setParamDefaultValue("默认值2");

        // 执行私有方法
        Object result = parametersEqualMethod.invoke(serviceImpl, parameter1, parameter2);

        // 验证结果
        assertFalse((Boolean) result);
    }

    @Test
    void testParametersEqual_CipherType() throws Exception {
        // 创建测试实例
        MyScriptServiceImpl serviceImpl = new MyScriptServiceImpl(doubleCheckApiService, attachmentEphemeralMapper, mockBuilder,releaseMediaService,taskAuthorityService,cibProperties,afterScriptExecAuditing);

        // 使用反射获取私有方法
        java.lang.reflect.Method parametersEqualMethod = MyScriptServiceImpl.class.getDeclaredMethod("parametersEqual", Parameter.class, Parameter.class);
        parametersEqualMethod.setAccessible(true);

        // 准备测试数据 - Cipher类型参数
        Parameter parameter1 = new Parameter();
        parameter1.setParamType("Cipher");
        parameter1.setParamName("testParam");
        parameter1.setParamCheckIid(1L);
        parameter1.setParamDesc("描述");
        parameter1.setParamDefaultValue("加密值1");

        Parameter parameter2 = new Parameter();
        parameter2.setParamType("Cipher");
        parameter2.setParamName("testParam");
        parameter2.setParamCheckIid(1L);
        parameter2.setParamDesc("描述");
        parameter2.setParamDefaultValue("加密值2");

        // 模拟静态方法EncryptUtils.sm4Decrypt
        try (MockedStatic<com.ideal.common.util.EncryptUtils> encryptUtilsMockedStatic = Mockito.mockStatic(com.ideal.common.util.EncryptUtils.class)) {
            encryptUtilsMockedStatic.when(() -> com.ideal.common.util.EncryptUtils.sm4Decrypt("加密值1")).thenReturn("解密值");
            encryptUtilsMockedStatic.when(() -> com.ideal.common.util.EncryptUtils.sm4Decrypt("加密值2")).thenReturn("解密值");

            // 执行私有方法
            Object result = parametersEqualMethod.invoke(serviceImpl, parameter1, parameter2);

            // 验证结果
            assertTrue((Boolean) result);
        }
    }

    @Test
    void testParametersEqual_EnumsType() throws Exception {
        // 创建测试实例
        MyScriptServiceImpl serviceImpl = new MyScriptServiceImpl(doubleCheckApiService, attachmentEphemeralMapper, mockBuilder,releaseMediaService,taskAuthorityService,cibProperties,afterScriptExecAuditing);

        // 使用反射获取私有方法
        java.lang.reflect.Method parametersEqualMethod = MyScriptServiceImpl.class.getDeclaredMethod("parametersEqual", Parameter.class, Parameter.class);
        parametersEqualMethod.setAccessible(true);

        // 准备测试数据 - Enums类型参数
        Parameter parameter1 = new Parameter();
        parameter1.setParamType("Enums");
        parameter1.setScriptParameterManagerId(1L);
        parameter1.setParamDefaultValue("选项1");

        Parameter parameter2 = new Parameter();
        parameter2.setParamType("Enums");
        parameter2.setScriptParameterManagerId(1L);
        parameter2.setParamDefaultValue("选项1");

        // 执行私有方法
        Object result = parametersEqualMethod.invoke(serviceImpl, parameter1, parameter2);

        // 验证结果
        assertTrue((Boolean) result);
    }

    @Test
    void testParametersEqual_EnumsType_Different() throws Exception {
        // 创建测试实例
        MyScriptServiceImpl serviceImpl = new MyScriptServiceImpl(doubleCheckApiService, attachmentEphemeralMapper, mockBuilder,releaseMediaService,taskAuthorityService,cibProperties,afterScriptExecAuditing);

        // 使用反射获取私有方法
        java.lang.reflect.Method parametersEqualMethod = MyScriptServiceImpl.class.getDeclaredMethod("parametersEqual", Parameter.class, Parameter.class);
        parametersEqualMethod.setAccessible(true);

        // 准备测试数据 - 不同的Enums类型参数
        Parameter parameter1 = new Parameter();
        parameter1.setParamType("Enums");
        parameter1.setScriptParameterManagerId(1L);
        parameter1.setParamDefaultValue("选项1");

        Parameter parameter2 = new Parameter();
        parameter2.setParamType("Enums");
        parameter2.setScriptParameterManagerId(2L); // 不同的ID
        parameter2.setParamDefaultValue("选项1");

        // 执行私有方法
        Object result = parametersEqualMethod.invoke(serviceImpl, parameter1, parameter2);

        // 验证结果
        assertFalse((Boolean) result);
    }

    @Test
    void testGetAttachmentsTemp() throws Exception {
        // 创建测试实例
        MyScriptServiceImpl serviceImpl = new MyScriptServiceImpl(doubleCheckApiService, attachmentEphemeralMapper, mockBuilder,releaseMediaService,taskAuthorityService,cibProperties,afterScriptExecAuditing);

        // 使用反射获取私有方法
        java.lang.reflect.Method getAttachmentsTempMethod = MyScriptServiceImpl.class.getDeclaredMethod("getAttachmentsTemp", List.class, ScriptInfoQueryDto.class);
        getAttachmentsTempMethod.setAccessible(true);

        // 准备测试数据
        List<AttachmentUploadDto> existingAttachments = new ArrayList<>();
        ScriptInfoQueryDto scriptInfoQueryDto = new ScriptInfoQueryDto();
        scriptInfoQueryDto.setSrcScriptUuid("script-123@[1,2,3]");

        // 模拟查询结果
        List<Attachment> mockAttachments = new ArrayList<>();
        Attachment attachment1 = new Attachment();
        attachment1.setId(1L);
        attachment1.setName("attachment1.txt");
        attachment1.setSize(1024L);
        Attachment attachment2 = new Attachment();
        attachment2.setId(2L);
        attachment2.setName("attachment2.txt");
        attachment2.setSize(2048L);
        Attachment attachment3 = new Attachment();
        attachment3.setId(3L);
        attachment3.setName("attachment3.txt");
        attachment3.setSize(4096L);
        mockAttachments.add(attachment1);
        mockAttachments.add(attachment2);
        mockAttachments.add(attachment3);

        when(attachmentEphemeralMapper.selectAttachmentByIds(any(Long[].class))).thenReturn(mockAttachments);

        // 执行私有方法
        @SuppressWarnings("unchecked")
        List<AttachmentUploadDto> result = (List<AttachmentUploadDto>) getAttachmentsTempMethod.invoke(serviceImpl, existingAttachments, scriptInfoQueryDto);

        // 验证结果
        assertEquals(3, result.size());
        verify(attachmentEphemeralMapper, times(1)).selectAttachmentByIds(any(Long[].class));
    }

    @Test
    void testGetAttachmentsTemp_NoSrcScriptUuid() throws Exception {
        // 创建测试实例
        MyScriptServiceImpl serviceImpl = new MyScriptServiceImpl(doubleCheckApiService, attachmentEphemeralMapper, mockBuilder,releaseMediaService,taskAuthorityService,cibProperties,afterScriptExecAuditing);

        // 使用反射获取私有方法
        java.lang.reflect.Method getAttachmentsTempMethod = MyScriptServiceImpl.class.getDeclaredMethod("getAttachmentsTemp", List.class, ScriptInfoQueryDto.class);
        getAttachmentsTempMethod.setAccessible(true);

        // 准备测试数据 - 没有SrcScriptUuid
        List<AttachmentUploadDto> existingAttachments = new ArrayList<>();
        ScriptInfoQueryDto scriptInfoQueryDto = new ScriptInfoQueryDto();

        // 执行私有方法
        @SuppressWarnings("unchecked")
        List<AttachmentUploadDto> result = (List<AttachmentUploadDto>) getAttachmentsTempMethod.invoke(serviceImpl, existingAttachments, scriptInfoQueryDto);

        // 验证结果
        assertEquals(0, result.size());
        verify(attachmentEphemeralMapper, times(0)).selectAttachmentByIds(any(Long[].class));
    }

    @Test
    void testGetAttachmentsTemp_InvalidSrcScriptUuidFormat() throws Exception {
        // 创建测试实例
        MyScriptServiceImpl serviceImpl = new MyScriptServiceImpl(doubleCheckApiService, attachmentEphemeralMapper, mockBuilder,releaseMediaService,taskAuthorityService,cibProperties,afterScriptExecAuditing);

        // 使用反射获取私有方法
        java.lang.reflect.Method getAttachmentsTempMethod = MyScriptServiceImpl.class.getDeclaredMethod("getAttachmentsTemp", List.class, ScriptInfoQueryDto.class);
        getAttachmentsTempMethod.setAccessible(true);

        // 准备测试数据 - SrcScriptUuid格式不正确
        List<AttachmentUploadDto> existingAttachments = new ArrayList<>();
        ScriptInfoQueryDto scriptInfoQueryDto = new ScriptInfoQueryDto();
        scriptInfoQueryDto.setSrcScriptUuid("script-123"); // 没有@和附件ID

        // 执行私有方法
        @SuppressWarnings("unchecked")
        List<AttachmentUploadDto> result = (List<AttachmentUploadDto>) getAttachmentsTempMethod.invoke(serviceImpl, existingAttachments, scriptInfoQueryDto);

        // 验证结果
        assertEquals(0, result.size());
        verify(attachmentEphemeralMapper, times(0)).selectAttachmentByIds(any(Long[].class));
    }

    @Test
    void testGetAttachmentsTemp_EmptyAttachmentIds() throws Exception {
        // 创建测试实例
        MyScriptServiceImpl serviceImpl = new MyScriptServiceImpl(doubleCheckApiService, attachmentEphemeralMapper, mockBuilder,releaseMediaService,taskAuthorityService,cibProperties,afterScriptExecAuditing);

        // 使用反射获取私有方法
        java.lang.reflect.Method getAttachmentsTempMethod = MyScriptServiceImpl.class.getDeclaredMethod("getAttachmentsTemp", List.class, ScriptInfoQueryDto.class);
        getAttachmentsTempMethod.setAccessible(true);

        // 准备测试数据 - 空附件ID列表
        List<AttachmentUploadDto> existingAttachments = new ArrayList<>();
        ScriptInfoQueryDto scriptInfoQueryDto = new ScriptInfoQueryDto();
        scriptInfoQueryDto.setSrcScriptUuid("script-123@[]"); // 空附件ID列表

        // 执行私有方法
        @SuppressWarnings("unchecked")
        List<AttachmentUploadDto> result = (List<AttachmentUploadDto>) getAttachmentsTempMethod.invoke(serviceImpl, existingAttachments, scriptInfoQueryDto);

        // 验证结果
        assertEquals(0, result.size());
        verify(attachmentEphemeralMapper, times(0)).selectAttachmentByIds(any(Long[].class));
    }

    @Test
    void testCheckCategoryValidation_UserWithMatchingOrgCode() {
        // 设置当前用户
        CurrentUser currentUser = new CurrentUser();
        currentUser.setId(1L);
        currentUser.setSupervisor(false);
        currentUser.setOrgCode("orgCode1");
        currentUserUtilMockedStatic.when(CurrentUserUtil::getCurrentUser).thenReturn(currentUser);

        // 模拟分类部门关系
        List<OrgBean> categoryOrgRelations = new ArrayList<>();
        OrgBean orgBean = new OrgBean();
        orgBean.setOrgId(1L);
        orgBean.setCode("orgCode1");
        orgBean.setCategoryId(1L);
        categoryOrgRelations.add(orgBean);

        when(scripts.getCategoryMapper()).thenReturn(categoryMapper);
        when(categoryMapper.getCategoryOrgRelations(1L)).thenReturn(categoryOrgRelations);

        // 创建测试实例
        MyScriptServiceImpl serviceImpl = new MyScriptServiceImpl(doubleCheckApiService, attachmentEphemeralMapper, mockBuilder,releaseMediaService,taskAuthorityService,cibProperties,afterScriptExecAuditing);
        when(scripts.getScriptBusinessConfig()).thenReturn(scriptBusinessConfig);
        // 执行方法
        boolean result = serviceImpl.checkCategoryValidation(1L);

        // 验证结果 - 用户的部门匹配，应该返回true
        assertTrue(result);

        // 验证方法调用
        verify(scripts.getCategoryMapper(), times(1)).getCategoryOrgRelations(1L);
    }

    @Test
    void testCheckCategoryValidation_OrlePermission() {
        // 创建测试实例
        MyScriptServiceImpl serviceImpl = new MyScriptServiceImpl(doubleCheckApiService, attachmentEphemeralMapper, mockBuilder,releaseMediaService,taskAuthorityService,cibProperties,afterScriptExecAuditing);

        // 设置当前用户
        CurrentUser currentUser = new CurrentUser();
        currentUser.setId(1L);
        currentUser.setSupervisor(false);
        currentUser.setOrgCode("orgCode1");
        currentUserUtilMockedStatic.when(CurrentUserUtil::getCurrentUser).thenReturn(currentUser);

        // 模拟分类部门关系
        List<OrgBean> categoryOrgRelations = new ArrayList<>();
        OrgBean orgBean = new OrgBean();
        orgBean.setOrgId(1L);
        orgBean.setCode("orgCode1");
        orgBean.setCategoryId(1L);
        categoryOrgRelations.add(orgBean);

        when(scripts.getCategoryMapper()).thenReturn(categoryMapper);
        when(categoryMapper.getCategoryOrgRelations(1L)).thenReturn(categoryOrgRelations);
        when(scripts.getScriptBusinessConfig()).thenReturn(scriptBusinessConfig);

        // 使用 spy 来部分模拟 serviceImpl
        MyScriptServiceImpl spyService = Mockito.spy(serviceImpl);
        doReturn(true).when(spyService).getRolePermissionFlag();

        List<RoleApiDto> roleApiDtoList = new ArrayList<>();
        RoleApiDto roleApiDto = new RoleApiDto();
        roleApiDto.setId(1L);
        roleApiDtoList.add(roleApiDto);

        when(scripts.getiRole()).thenReturn(iRole);

        when(iRole.selectRoleListByLoginName(any())).thenReturn(roleApiDtoList);

        List<Long> idsList = new ArrayList<>();
        idsList.add(1L);
        when(categoryMapper.getSaveRoleCategoryIdsByRoleIds(any())).thenReturn(idsList);

        // 执行方法
        boolean result = spyService.checkCategoryValidation(1L);

        // 验证结果
        assertTrue(result);

    }


    @Test
    void testGetCategoryPathByUserRole() {
        // 创建测试实例
        MyScriptServiceImpl serviceImpl = new MyScriptServiceImpl(doubleCheckApiService, attachmentEphemeralMapper, mockBuilder,releaseMediaService,taskAuthorityService,cibProperties,afterScriptExecAuditing);

        // 设置当前用户
        CurrentUser currentUser = new CurrentUser();
        currentUser.setId(1L);
        currentUser.setSupervisor(false);
        currentUser.setOrgCode("orgCode1");
        currentUserUtilMockedStatic.when(CurrentUserUtil::getCurrentUser).thenReturn(currentUser);

        // 模拟分类部门关系
        List<OrgBean> categoryOrgRelations = new ArrayList<>();
        OrgBean orgBean = new OrgBean();
        orgBean.setOrgId(1L);
        orgBean.setCode("orgCode1");
        orgBean.setCategoryId(1L);
        categoryOrgRelations.add(orgBean);

        when(scripts.getCategoryMapper()).thenReturn(categoryMapper);
        when(categoryMapper.getCategoryOrgRelations(1L)).thenReturn(categoryOrgRelations);
        when(scripts.getScriptBusinessConfig()).thenReturn(scriptBusinessConfig);

        // 使用 spy 来部分模拟 serviceImpl
        MyScriptServiceImpl spyService = Mockito.spy(serviceImpl);
        doReturn(true).when(spyService).getRolePermissionFlag();

        List<RoleApiDto> roleApiDtoList = new ArrayList<>();
        RoleApiDto roleApiDto = new RoleApiDto();
        roleApiDto.setId(1L);
        roleApiDtoList.add(roleApiDto);

        when(scripts.getiRole()).thenReturn(iRole);

        when(iRole.selectRoleListByLoginName(any())).thenReturn(roleApiDtoList);

        when(scripts.getCategoryMapper()).thenReturn(categoryMapper);

        List<CategoryRoleBean> categortIdList = new ArrayList<>();
        CategoryRoleBean categoryRoleBean = new CategoryRoleBean();
        categoryRoleBean.setId(1L);
        categortIdList.add(categoryRoleBean);

        when(categoryMapper.getCategoryIdsByRole(any())).thenReturn(categortIdList);


        List<Long> idsList = new ArrayList<>();
        idsList.add(1L);
        when(categoryMapper.getSaveRoleCategoryIdsByRoleIds(any())).thenReturn(idsList);

        when(scripts.getCategoryService()).thenReturn(categoryService);
        when(categoryService.buildCategoryPath(any())).thenReturn("abc#");
        // 执行方法
        List<String> result = spyService.getCategoryPathByUserRole(currentUser);

        // 验证结果
        assertNotNull(result);

    }


    @Test
    void testGetRolePermission_true() {
        // 创建测试实例
        MyScriptServiceImpl serviceImpl = new MyScriptServiceImpl(doubleCheckApiService, attachmentEphemeralMapper, mockBuilder,releaseMediaService,taskAuthorityService,cibProperties,afterScriptExecAuditing);

        List<RoleApiDto> roleApiDtoList = new ArrayList<>();
        RoleApiDto roleApiDto = new RoleApiDto();
        roleApiDto.setId(1L);
        roleApiDtoList.add(roleApiDto);

        when(scripts.getScriptBusinessConfig()).thenReturn(scriptBusinessConfig);

        when(scriptBusinessConfig.getDataPermissionPolicy()).thenReturn("userRoleGroup");
        // 使用 spy 来部分模拟 serviceImpl
        MyScriptServiceImpl spyService = Mockito.spy(serviceImpl);
        doReturn(true).when(spyService).getRolePermissionFlag();
        // 执行方法
        boolean result = spyService.getRolePermission();

        // 验证结果
        assertTrue(result);

    }

    @Test
    void testGetRolePermission_false() {
        // 创建测试实例
        MyScriptServiceImpl serviceImpl = new MyScriptServiceImpl(doubleCheckApiService, attachmentEphemeralMapper, mockBuilder,releaseMediaService,taskAuthorityService,cibProperties,afterScriptExecAuditing);

        List<RoleApiDto> roleApiDtoList = new ArrayList<>();
        RoleApiDto roleApiDto = new RoleApiDto();
        roleApiDto.setId(1L);
        roleApiDtoList.add(roleApiDto);

        when(scripts.getScriptBusinessConfig()).thenReturn(scriptBusinessConfig);

        when(scriptBusinessConfig.getDataPermissionPolicy()).thenReturn("userRoleGroup1");
        // 使用 spy 来部分模拟 serviceImpl
        MyScriptServiceImpl spyService = Mockito.spy(serviceImpl);
        doReturn(true).when(spyService).getRolePermissionFlag();
        // 执行方法
        boolean result = spyService.getRolePermission();

        // 验证结果
        assertFalse(result);

    }


    @Test
    void testCheckCategoryValidation_UserWithNonMatchingOrgCode() {
        // 设置当前用户
        CurrentUser currentUser = new CurrentUser();
        currentUser.setId(1L);
        currentUser.setSupervisor(false);
        currentUser.setOrgCode("orgCode2");
        currentUserUtilMockedStatic.when(CurrentUserUtil::getCurrentUser).thenReturn(currentUser);

        // 模拟分类部门关系
        List<OrgBean> categoryOrgRelations = new ArrayList<>();
        OrgBean orgBean = new OrgBean();
        orgBean.setOrgId(1L);
        orgBean.setCode("orgCode1");
        orgBean.setCategoryId(1L);
        categoryOrgRelations.add(orgBean);

        when(scripts.getCategoryMapper()).thenReturn(categoryMapper);
        when(categoryMapper.getCategoryOrgRelations(1L)).thenReturn(categoryOrgRelations);

        // 创建测试实例
        MyScriptServiceImpl serviceImpl = new MyScriptServiceImpl(doubleCheckApiService, attachmentEphemeralMapper, mockBuilder,releaseMediaService,taskAuthorityService,cibProperties,afterScriptExecAuditing);
        when(scripts.getScriptBusinessConfig()).thenReturn(scriptBusinessConfig);
        // 执行方法
        boolean result = serviceImpl.checkCategoryValidation(1L);

        // 验证结果 - 用户的部门不匹配，应该返回false
        assertFalse(result);

        // 验证方法调用
        verify(scripts.getCategoryMapper(), times(1)).getCategoryOrgRelations(1L);
    }

    @Test
    void testCheckCategoryValidation_NoCategoryOrgRelations() {
        // 设置当前用户
        CurrentUser currentUser = new CurrentUser();
        currentUser.setId(1L);
        currentUser.setSupervisor(false);
        currentUser.setOrgCode("orgCode1");
        currentUserUtilMockedStatic.when(CurrentUserUtil::getCurrentUser).thenReturn(currentUser);

        // 模拟空的分类部门关系
        List<OrgBean> categoryOrgRelations = new ArrayList<>();

        when(scripts.getCategoryMapper()).thenReturn(categoryMapper);
        when(categoryMapper.getCategoryOrgRelations(1L)).thenReturn(categoryOrgRelations);

        // 创建测试实例
        MyScriptServiceImpl serviceImpl = new MyScriptServiceImpl(doubleCheckApiService, attachmentEphemeralMapper, mockBuilder,releaseMediaService,taskAuthorityService,cibProperties,afterScriptExecAuditing);
        when(scripts.getScriptBusinessConfig()).thenReturn(scriptBusinessConfig);
        // 执行方法
        boolean result = serviceImpl.checkCategoryValidation(1L);

        // 验证结果 - 没有部门关系，应该返回true
        assertTrue(result);

        // 验证方法调用
        verify(scripts.getCategoryMapper(), times(1)).getCategoryOrgRelations(1L);
    }

    @Test
    void testEnsureCreatedOnce_NewScript_NoExistingSameNameScript() throws Exception {
        // 创建测试实例
        MyScriptServiceImpl serviceImpl = new MyScriptServiceImpl(doubleCheckApiService, attachmentEphemeralMapper, mockBuilder,releaseMediaService,taskAuthorityService,cibProperties,afterScriptExecAuditing);

        // 准备测试数据 - 新脚本，没有相同名称的脚本
        ScriptInfoDto scriptInfoDto = new ScriptInfoDto();
        scriptInfoDto.setScriptNameZh("新脚本");
        // 不设置uniqueUuid，模拟新脚本

        // 模拟没有相同名称的脚本
        when(scripts.getInfoMapper()).thenReturn(infoMapper);
        when(infoMapper.getSameScriptNameZhCount("新脚本")).thenReturn(0);

        // 设置scripts字段
        Field scriptsField = MyScriptServiceImpl.class.getDeclaredField("scripts");
        scriptsField.setAccessible(true);
        scriptsField.set(serviceImpl, scripts);

        // 执行方法
        boolean result = serviceImpl.ensureCreatedOnce(scriptInfoDto);

        // 验证结果 - 应该返回true表示可以创建
        assertTrue(result);

        // 验证方法调用
        verify(infoMapper, times(1)).getSameScriptNameZhCount("新脚本");
    }

    @Test
    void testEnsureCreatedOnce_NewScript_ExistingSameNameScript() throws Exception {
        // 创建测试实例
        MyScriptServiceImpl serviceImpl = new MyScriptServiceImpl(doubleCheckApiService, attachmentEphemeralMapper, mockBuilder,releaseMediaService,taskAuthorityService,cibProperties,afterScriptExecAuditing);

        // 准备测试数据 - 新脚本，有相同名称的脚本
        ScriptInfoDto scriptInfoDto = new ScriptInfoDto();
        scriptInfoDto.setScriptNameZh("已存在脚本");
        // 不设置uniqueUuid，模拟新脚本

        // 模拟有相同名称的脚本
        when(scripts.getInfoMapper()).thenReturn(infoMapper);
        when(infoMapper.getSameScriptNameZhCount("已存在脚本")).thenReturn(1);

        // 设置scripts字段
        Field scriptsField = MyScriptServiceImpl.class.getDeclaredField("scripts");
        scriptsField.setAccessible(true);
        scriptsField.set(serviceImpl, scripts);

        // 执行方法并验证异常
        try {
            serviceImpl.ensureCreatedOnce(scriptInfoDto);
            fail("Expected ScriptException was not thrown");
        } catch (ScriptException e) {
            assertEquals("script.name.zh.exists", e.getMessage());
        }

        // 验证方法调用
        verify(infoMapper, times(1)).getSameScriptNameZhCount("已存在脚本");
    }

    @Test
    void testEnsureCreatedOnce_ExistingScript_Draft() throws Exception {
        // 创建测试实例
        MyScriptServiceImpl serviceImpl = new MyScriptServiceImpl(doubleCheckApiService, attachmentEphemeralMapper, mockBuilder,releaseMediaService,taskAuthorityService,cibProperties,afterScriptExecAuditing);

        // 准备测试数据 - 已有脚本，草稿状态
        ScriptInfoDto scriptInfoDto = new ScriptInfoDto();
        scriptInfoDto.setId(1L);
        scriptInfoDto.setUniqueUuid("uuid-123");

        // 模拟脚本信息，设置为草稿状态(editState=0)
        Info info = new Info();
        info.setId(1L);
        info.setEditState(0);

        when(scripts.getInfoMapper()).thenReturn(infoMapper);
        when(infoMapper.selectInfoById(1L)).thenReturn(info);

        // 设置scripts字段
        Field scriptsField = MyScriptServiceImpl.class.getDeclaredField("scripts");
        scriptsField.setAccessible(true);
        scriptsField.set(serviceImpl, scripts);

        // 执行方法
        boolean result = serviceImpl.ensureCreatedOnce(scriptInfoDto);

        // 验证结果 - 草稿状态应该返回false
        assertFalse(result);

        // 验证方法调用
        verify(infoMapper, times(1)).selectInfoById(1L);
    }

    @Test
    void testEnsureCreatedOnce_ExistingScript_Published() throws Exception {
        // 创建测试实例
        MyScriptServiceImpl serviceImpl = new MyScriptServiceImpl(doubleCheckApiService, attachmentEphemeralMapper, mockBuilder,releaseMediaService,taskAuthorityService,cibProperties,afterScriptExecAuditing);

        // 准备测试数据 - 已有脚本，已发布状态
        ScriptInfoDto scriptInfoDto = new ScriptInfoDto();
        scriptInfoDto.setId(1L);
        scriptInfoDto.setUniqueUuid("uuid-123");

        // 模拟脚本信息，设置为已发布状态(editState=1)
        Info info = new Info();
        info.setId(1L);
        info.setEditState(1);

        when(scripts.getInfoMapper()).thenReturn(infoMapper);
        when(infoMapper.selectInfoById(1L)).thenReturn(info);

        // 设置scripts字段
        Field scriptsField = MyScriptServiceImpl.class.getDeclaredField("scripts");
        scriptsField.setAccessible(true);
        scriptsField.set(serviceImpl, scripts);

        // 执行方法
        boolean result = serviceImpl.ensureCreatedOnce(scriptInfoDto);

        // 验证结果 - 已发布状态应该返回true
        assertTrue(result);

        // 验证方法调用
        verify(infoMapper, times(1)).selectInfoById(1L);
    }

    @Test
    @DisplayName("测试获取双人复核脚本标记 - 传入空数组")
    void testGetDoubleCheckScriptFlag_EmptyArray() {
        // 准备测试数据
        Long[] scriptInfoVersionId = null;

        // 执行测试方法
        boolean result = myScriptServiceImplUnderTest.getDoubleCheckScriptFlag(scriptInfoVersionId);

        // 断言结果为false
        assertFalse(result);
    }

    @Test
    @DisplayName("测试获取双人复核脚本标记 - 传入空元素数组")
    void testGetDoubleCheckScriptFlag_ZeroLengthArray() {
        // 准备测试数据
        Long[] scriptInfoVersionId = new Long[0];

        // 执行测试方法
        boolean result = myScriptServiceImplUnderTest.getDoubleCheckScriptFlag(scriptInfoVersionId);

        // 断言结果为false
        assertFalse(result);
    }

    @Test
    @DisplayName("测试获取双人复核脚本标记 - 不存在已打回的脚本")
    void testGetDoubleCheckScriptFlag_NoRejectedScripts() {
        // 准备测试数据
        Long[] scriptInfoVersionId = new Long[]{1L, 2L};
        List<AuditRelation> auditRelations = new ArrayList<>();

        AuditRelation relation1 = new AuditRelation();
        relation1.setState(1); // 使用1表示APPROVAL状态
        auditRelations.add(relation1);

        AuditRelation relation2 = new AuditRelation();
        relation2.setState(1); // 使用1表示APPROVAL状态
        auditRelations.add(relation2);

        // Mock scripts.getAuditRelationMapper()的行为
        when(scripts.getAuditRelationMapper()).thenReturn(auditRelationMapper);
        // Mock AuditRelationMapper的行为
        when(auditRelationMapper.selectAuditRelationByScriptInfoVersionId(scriptInfoVersionId)).thenReturn(auditRelations);

        // 执行测试方法
        boolean result = myScriptServiceImplUnderTest.getDoubleCheckScriptFlag(scriptInfoVersionId);

        // 断言结果为false
        assertFalse(result);

        // 验证方法被调用
        verify(scripts).getAuditRelationMapper();
        verify(auditRelationMapper).selectAuditRelationByScriptInfoVersionId(scriptInfoVersionId);
    }

    @Test
    @DisplayName("测试获取双人复核脚本标记 - 存在已打回的脚本")
    void testGetDoubleCheckScriptFlag_HasRejectedScripts() {
        // 准备测试数据
        Long[] scriptInfoVersionId = new Long[]{1L, 2L};
        List<AuditRelation> auditRelations = new ArrayList<>();

        AuditRelation relation1 = new AuditRelation();
        relation1.setState(1); // 使用1表示APPROVAL状态
        auditRelations.add(relation1);

        AuditRelation relation2 = new AuditRelation();
        relation2.setState(3); // 使用3表示REJECTED状态
        auditRelations.add(relation2);

        // Mock scripts.getAuditRelationMapper()的行为
        when(scripts.getAuditRelationMapper()).thenReturn(auditRelationMapper);
        // Mock AuditRelationMapper的行为
        when(auditRelationMapper.selectAuditRelationByScriptInfoVersionId(scriptInfoVersionId)).thenReturn(auditRelations);

        // 执行测试方法
        boolean result = myScriptServiceImplUnderTest.getDoubleCheckScriptFlag(scriptInfoVersionId);

        // 断言结果为true
        assertTrue(result);

        // 验证方法被调用
        verify(scripts).getAuditRelationMapper();
        verify(auditRelationMapper).selectAuditRelationByScriptInfoVersionId(scriptInfoVersionId);
    }

    @Test
    @DisplayName("测试获取删除双人复核脚本标记 - 传入空ID")
    void testGetDelDoubleCheckScriptFlag_NullId() throws ScriptException {
        // 准备测试数据
        Long scriptRelationId = null;

        // 执行测试方法
        boolean result = myScriptServiceImplUnderTest.getDelDoubleCheckScriptFlag(scriptRelationId);

        // 断言结果为false
        assertFalse(result);
    }

    @Test
    @DisplayName("测试获取删除双人复核脚本标记 - 不存在审核关系")
    void testGetDelDoubleCheckScriptFlag_NoAuditRelation() throws ScriptException {
        // 准备测试数据
        Long scriptRelationId = 1L;

        // Mock scripts.getAuditRelationMapper()的行为
        when(scripts.getAuditRelationMapper()).thenReturn(auditRelationMapper);
        // Mock AuditRelationMapper的行为
        when(auditRelationMapper.selectAuditRelationById(scriptRelationId)).thenReturn(null);

        // 执行测试方法
        boolean result = myScriptServiceImplUnderTest.getDelDoubleCheckScriptFlag(scriptRelationId);

        // 断言结果为false
        assertFalse(result);

        // 验证方法被调用
        verify(scripts).getAuditRelationMapper();
        verify(auditRelationMapper).selectAuditRelationById(scriptRelationId);
    }

    @Test
    @DisplayName("测试获取删除双人复核脚本标记 - 审核类型或状态不符合条件")
    void testGetDelDoubleCheckScriptFlag_InvalidTypeOrState() throws ScriptException {
        // 准备测试数据
        Long scriptRelationId = 1L;
        AuditRelation auditRelation = new AuditRelation();
        auditRelation.setAuditType(1); // 不是删除审核类型
        auditRelation.setState(1);

        // Mock scripts.getAuditRelationMapper()的行为
        when(scripts.getAuditRelationMapper()).thenReturn(auditRelationMapper);
        // Mock AuditRelationMapper的行为
        when(auditRelationMapper.selectAuditRelationById(scriptRelationId)).thenReturn(auditRelation);

        // 执行测试方法
        boolean result = myScriptServiceImplUnderTest.getDelDoubleCheckScriptFlag(scriptRelationId);

        // 断言结果为false
        assertFalse(result);

        // 验证方法被调用
        verify(scripts).getAuditRelationMapper();
        verify(auditRelationMapper).selectAuditRelationById(scriptRelationId);
    }

    @Test
    @DisplayName("测试获取删除双人复核脚本标记 - 满足条件的审核关系")
    void testGetDelDoubleCheckScriptFlag_ValidAuditRelation() throws ScriptException {
        // 准备测试数据
        Long scriptRelationId = 1L;
        AuditRelation auditRelation = new AuditRelation();
        auditRelation.setAuditType(2); // 删除审核类型
        auditRelation.setState(1);

        // Mock scripts.getAuditRelationMapper()的行为
        when(scripts.getAuditRelationMapper()).thenReturn(auditRelationMapper);
        // Mock AuditRelationMapper的行为
        when(auditRelationMapper.selectAuditRelationById(scriptRelationId)).thenReturn(auditRelation);

        // 执行测试方法
        boolean result = myScriptServiceImplUnderTest.getDelDoubleCheckScriptFlag(scriptRelationId);

        // 断言结果为true
        assertTrue(result);

        // 验证方法被调用
        verify(scripts).getAuditRelationMapper();
        verify(auditRelationMapper).selectAuditRelationById(scriptRelationId);
    }

    @Test
    @DisplayName("测试自动发布脚本 - 非工具箱来源")
    void testPublishScriptAuto_NotToolboxSource() throws ScriptException {
        // 准备测试数据
        PublishDto publishDto = new PublishDto();
        publishDto.setScriptSource(ScriptSource.SCRIPT_SOURCE.getValue()); // 非工具箱来源

        // 执行测试方法
        myScriptServiceImplUnderTest.publishScriptAuto(publishDto);

        // 由于脚本来源不是工具箱，方法应该直接返回，不进行任何操作
        // 验证在方法中没有进行对infoVersionMapper的调用
        verify(infoVersionMapper, times(0)).selectInfoVersionBysrcScriptUuid(any());
    }

    @Test
    @DisplayName("测试自动发布脚本 - 工具箱来源但已发布")
    void testPublishScriptAuto_ToolboxSourceAlreadyPublished() throws ScriptException {
        // 准备测试数据
        PublishDto publishDto = new PublishDto();
        publishDto.setScriptSource(ScriptSource.TOOLBOX_SOURCE.getValue()); // 工具箱来源
        publishDto.setSrcScriptUuid("test-uuid");

        InfoVersion infoVersionNew = new InfoVersion();
        infoVersionNew.setVersion("1.0"); // 已经有版本号，表示已发布

        // Mock scripts.getInfoVersionMapper()的行为
        when(scripts.getInfoVersionMapper()).thenReturn(infoVersionMapper);
        // Mock infoVersionMapper的行为
        when(infoVersionMapper.selectInfoVersionBysrcScriptUuid("test-uuid")).thenReturn(infoVersionNew);

        // 执行断言：预期会抛出脚本已发布的异常
        assertThrows(ScriptException.class, () -> myScriptServiceImplUnderTest.publishScriptAuto(publishDto));

        // 验证方法被调用
        verify(scripts).getInfoVersionMapper();
        verify(infoVersionMapper).selectInfoVersionBysrcScriptUuid("test-uuid");
    }

    @Test
    @DisplayName("测试自动发布脚本 - 首次发布无旧版本")
    void testPublishScriptAuto_FirstPublishNoOldVersion() throws ScriptException {
        // 准备测试数据
        PublishDto publishDto = new PublishDto();
        publishDto.setScriptSource(ScriptSource.TOOLBOX_SOURCE.getValue()); // 工具箱来源
        publishDto.setSrcScriptUuid("test-uuid");

        InfoVersion infoVersionNew = new InfoVersion();
        infoVersionNew.setVersion(null); // 无版本号，表示未发布
        infoVersionNew.setInfoUniqueUuid("unique-uuid");
        infoVersionNew.setSrcScriptUuid("test-uuid");

        Info info = new Info();
        info.setId(1L);
        info.setUpdatorId(1L);
        info.setUpdatorName("测试用户");

        // Mock scripts对象的方法
        when(scripts.getInfoVersionMapper()).thenReturn(infoVersionMapper);
        when(scripts.getInfoMapper()).thenReturn(infoMapper);
        when(scripts.getMyScriptMapper()).thenReturn(myScriptMapper);

        // Mock相关方法
        when(infoVersionMapper.selectInfoVersionBysrcScriptUuid("test-uuid")).thenReturn(infoVersionNew);
        when(infoVersionMapper.selectMaxInfoVersionByInfoUniqueUuid("unique-uuid")).thenReturn(null);
        when(infoMapper.selectInfoByUniqueUuid("unique-uuid")).thenReturn(info);

        // 执行测试方法
        myScriptServiceImplUnderTest.publishScriptAuto(publishDto);

        // 验证方法被调用
        verify(scripts,times(8)).getInfoVersionMapper();
        verify(scripts,times(4)).getInfoMapper();

    }

    @Test
    @DisplayName("测试自动发布脚本 - 有旧版本的发布")
    void testPublishScriptAuto_WithOldVersion() throws ScriptException {
        // 准备测试数据
        PublishDto publishDto = new PublishDto();
        publishDto.setScriptSource(ScriptSource.TOOLBOX_SOURCE.getValue()); // 工具箱来源
        publishDto.setSrcScriptUuid("test-uuid");

        InfoVersion infoVersionNew = new InfoVersion();
        infoVersionNew.setVersion(null); // 无版本号，表示未发布
        infoVersionNew.setInfoUniqueUuid("unique-uuid");
        infoVersionNew.setSrcScriptUuid("test-uuid");

        InfoVersion infoVersionOld = new InfoVersion();
        infoVersionOld.setVersion("1.0");
        infoVersionOld.setInfoUniqueUuid("unique-uuid");
        infoVersionOld.setSrcScriptUuid("old-uuid");
        infoVersionOld.setUpdatorId(1L);
        infoVersionOld.setUpdatorName("测试用户");

        Info info = new Info();
        info.setId(1L);
        info.setUpdatorId(1L);
        info.setUpdatorName("测试用户");

        // Mock scripts对象的方法
        when(scripts.getInfoVersionMapper()).thenReturn(infoVersionMapper);
        when(scripts.getInfoMapper()).thenReturn(infoMapper);
        when(scripts.getMyScriptMapper()).thenReturn(myScriptMapper);

        List<InfoVersion> infoVersionList = new ArrayList<>();
        infoVersionList.add(infoVersionOld);

        // Mock相关方法
        when(infoVersionMapper.selectInfoVersionBysrcScriptUuid("test-uuid")).thenReturn(infoVersionNew);
        when(infoVersionMapper.selectMaxInfoVersionByInfoUniqueUuid("unique-uuid")).thenReturn(infoVersionList);
        when(infoMapper.selectInfoByUniqueUuid("unique-uuid")).thenReturn(info);
        when(infoVersionMapper.getAllVersionUuidByUuid("old-uuid")).thenReturn(Collections.singletonList("old-uuid"));

        // 执行测试方法
        myScriptServiceImplUnderTest.publishScriptAuto(publishDto);


    }

    @Test
    @DisplayName("测试发送告警消息")
    void testToAlarm() {
        // 准备测试数据
        String isrcScriptUuid = "test-uuid";
        Long userId = 1L;

        Info info = new Info();
        info.setScriptName("测试脚本");

        UserInfoApiDto userInfoApiDto = new UserInfoApiDto();
        userInfoApiDto.setId(userId);
        userInfoApiDto.setLoginName("testUser");
        userInfoApiDto.setTelephone("13800138000");
        userInfoApiDto.setEmail("<EMAIL>");

        List<UserInfoApiDto> userInfoList = new ArrayList<>();
        userInfoList.add(userInfoApiDto);

        // Mock相关方法
        when(scripts.getInfoMapper()).thenReturn(infoMapper);
        when(infoMapper.selectInfoByScriptUuid(isrcScriptUuid)).thenReturn(info);
        when(scripts.getiUserInfo()).thenReturn(iUserInfo);
        when(iUserInfo.getUserInfoList(anyList())).thenReturn(userInfoList);

        IWarn warn = mock(IWarn.class);
        when(scripts.getWarn()).thenReturn(warn);

        // 执行测试方法
        myScriptServiceImplUnderTest.toAlarm(isrcScriptUuid, userId);

        // 验证方法被调用
        verify(infoMapper).selectInfoByScriptUuid(isrcScriptUuid);
        verify(iUserInfo).getUserInfoList(anyList());
        verify(warn).receiveWarn(anyString());
    }

    @Test
    @DisplayName("测试校验脚本发布状态 - 工具箱来源无版本号")
    void testCheckScriptForPublish_ToolboxSourceNoVersion() throws Exception {
        // 准备测试数据
        PublishDto publishDto = new PublishDto();
        publishDto.setScriptSource(ScriptSource.TOOLBOX_SOURCE.getValue()); // 工具箱来源
        publishDto.setSrcScriptUuid("test-uuid");

        InfoVersion infoVersion = new InfoVersion();
        infoVersion.setVersion(null); // 无版本号

        // 使用反射获取私有方法
        java.lang.reflect.Method method = MyScriptServiceImpl.class.getDeclaredMethod("checkScriptForPublish", PublishDto.class);
        method.setAccessible(true);

        // Mock相关方法
        when(infoVersionMapper.selectInfoVersionBysrcScriptUuid("test-uuid")).thenReturn(infoVersion);
        when(scripts.getInfoVersionMapper()).thenReturn(infoVersionMapper);

        // 执行测试方法
        method.invoke(myScriptServiceImplUnderTest, publishDto);

        // 验证方法被调用
        verify(infoVersionMapper).selectInfoVersionBysrcScriptUuid("test-uuid");
    }

    @Test
    @DisplayName("测试校验脚本发布状态 - 非工具箱来源无版本号")
    void testCheckScriptForPublish_NonToolboxSourceNoVersion() throws Exception {
        // 准备测试数据
        PublishDto publishDto = new PublishDto();
        publishDto.setScriptSource(ScriptSource.SCRIPT_SOURCE.getValue()); // 非工具箱来源
        publishDto.setIds(new Long[]{1L, 2L});

        List<InfoVersion> infoVersionList = new ArrayList<>();
        InfoVersion infoVersion1 = new InfoVersion();
        infoVersion1.setVersion(null); // 无版本号
        infoVersionList.add(infoVersion1);

        InfoVersion infoVersion2 = new InfoVersion();
        infoVersion2.setVersion(null); // 无版本号
        infoVersionList.add(infoVersion2);

        // 使用反射获取私有方法
        java.lang.reflect.Method method = MyScriptServiceImpl.class.getDeclaredMethod("checkScriptForPublish", PublishDto.class);
        method.setAccessible(true);

        // Mock相关方法
        when(infoVersionMapper.selectInfoVersionByIds(publishDto.getIds())).thenReturn(infoVersionList);
        when(scripts.getInfoVersionMapper()).thenReturn(infoVersionMapper);

        // 执行测试方法
        method.invoke(myScriptServiceImplUnderTest, publishDto);

        // 验证方法被调用
        verify(infoVersionMapper).selectInfoVersionByIds(publishDto.getIds());
    }

    @Test
    @DisplayName("测试校验脚本发布状态 - 已发布脚本抛出异常")
    void testCheckScriptForPublish_AlreadyPublished() throws Exception {
        // 准备测试数据
        PublishDto publishDto = new PublishDto();
        publishDto.setScriptSource(ScriptSource.TOOLBOX_SOURCE.getValue()); // 工具箱来源
        publishDto.setSrcScriptUuid("test-uuid");

        InfoVersion infoVersion = new InfoVersion();
        infoVersion.setVersion("1.0"); // 已有版本号

        // 使用反射获取私有方法
        java.lang.reflect.Method method = MyScriptServiceImpl.class.getDeclaredMethod("checkScriptForPublish", PublishDto.class);
        method.setAccessible(true);

        // Mock相关方法
        when(infoVersionMapper.selectInfoVersionBysrcScriptUuid("test-uuid")).thenReturn(infoVersion);
        when(scripts.getInfoVersionMapper()).thenReturn(infoVersionMapper);

        // 执行测试方法并断言抛出异常
        try {
            method.invoke(myScriptServiceImplUnderTest, publishDto);
            fail("Expected ScriptException was not thrown");
        } catch (java.lang.reflect.InvocationTargetException e) {
            // 验证InvocationTargetException的cause是否为ScriptException
            assertTrue(e.getCause() instanceof ScriptException);
            assertEquals("script.published", e.getCause().getMessage());
        }

        // 验证方法被调用
        verify(infoVersionMapper).selectInfoVersionBysrcScriptUuid("test-uuid");
    }

    @Test
    @DisplayName("测试获取标签列表 - 从数据库获取成功场景")
    void testGetLabelList_Success() {
        // 准备测试数据
        List<String> labelList = Arrays.asList("标签1,标签2", "标签3,标签4,标签5");

        // Mock Redis相关组件
        @SuppressWarnings("unchecked")
        RScoredSortedSet<String> mockZSet = mock(RScoredSortedSet.class);
        RScript mockScript = mock(RScript.class);

        // Mock InfoMapper返回标签列表
        when(scripts.getInfoMapper()).thenReturn(infoMapper);
        when(infoMapper.getLabelList()).thenReturn(labelList);

        // Mock Redis客户端
        when(scripts.getRedissonClient()).thenReturn(mockRedissonClient);
        // 使用 Mockito.any() 来解决类型问题
        when(mockRedissonClient.getScoredSortedSet(anyString())).thenReturn((RScoredSortedSet) mockZSet);
        when(mockRedissonClient.getScript()).thenReturn(mockScript);
        when(mockZSet.isEmpty()).thenReturn(true); // 设置Redis缓存为空

        // 执行测试方法
        Set<String> result = myScriptServiceImplUnderTest.getLabelList();

        // 验证结果
        assertNotNull(result);
        assertEquals(5, result.size());
        assertTrue(result.contains("标签1"));
        assertTrue(result.contains("标签2"));
        assertTrue(result.contains("标签3"));
        assertTrue(result.contains("标签4"));
        assertTrue(result.contains("标签5"));

        // 验证方法调用
        verify(infoMapper).getLabelList();
        verify(mockRedissonClient).getScoredSortedSet(anyString());
        // 使用 anyString() 而不是确切的参数值
        verify(mockScript, atLeastOnce()).eval(any(), anyString(), any(), anyList(), any());
        verify(mockZSet).expire(Duration.ofDays(7));
    }

    @Test
    @DisplayName("测试获取标签列表 - 从Redis缓存获取成功场景")
    void testGetLabelList_FromRedisCache() {
        // 准备测试数据
        Set<String> cachedLabels = new HashSet<>(Arrays.asList("缓存标签1", "缓存标签2", "缓存标签3"));
        List<String> dbLabels = Arrays.asList("标签1,标签2", "标签3,标签4,标签5");

        // Mock InfoMapper返回标签列表
        when(scripts.getInfoMapper()).thenReturn(infoMapper);
        when(infoMapper.getLabelList()).thenReturn(dbLabels);

        // Mock Redis相关组件
        @SuppressWarnings("unchecked")
        RScoredSortedSet<String> mockZSet = mock(RScoredSortedSet.class);
        RScript mockScript = mock(RScript.class);

        // Mock Redis客户端
        when(scripts.getRedissonClient()).thenReturn(mockRedissonClient);
        when(mockRedissonClient.getScoredSortedSet(anyString())).thenReturn((RScoredSortedSet)mockZSet);
        when(mockRedissonClient.getScript()).thenReturn(mockScript);
        when(mockZSet.isEmpty()).thenReturn(false); // 设置Redis缓存非空
        when(mockZSet.readAll()).thenReturn(cachedLabels);

        // 执行测试方法
        Set<String> result = myScriptServiceImplUnderTest.getLabelList();

        // 验证结果
        assertNotNull(result);
        // 根据实际实现，方法会先从数据库获取标签，然后处理Redis缓存
        // 所以结果应该是数据库中的标签列表，共有5个标签
        assertEquals(5, result.size());
        assertTrue(result.contains("标签1"));
        assertTrue(result.contains("标签2"));
        assertTrue(result.contains("标签3"));
        assertTrue(result.contains("标签4"));
        assertTrue(result.contains("标签5"));

        // 验证方法调用
        verify(mockRedissonClient).getScoredSortedSet("script_labelZSet");
        // 验证调用了数据库的getLabelList方法
        verify(infoMapper).getLabelList();
        // 不验证具体的脚本执行参数，只验证方法被调用
        // 使用Mockito.any()匹配任意参数
        verify(mockScript, atLeastOnce()).eval(
            any(), any(), any(), any(), any()
        );
    }

    @Test
    @DisplayName("测试获取执行器验证列表 - 成功场景")
    void testGetExecutorValidationList_Success() {
        // 准备测试数据
        List<String> whiteCategoryList = Arrays.asList("分类1", "分类2");
        List<String> dangerUserList = Arrays.asList("root", "admin");
        List<String> specialReviewOrgList = Arrays.asList("部门1", "部门2");

        // Mock ScriptBusinessConfig返回配置数据
        when(scripts.getScriptBusinessConfig()).thenReturn(scriptBusinessConfig);
        when(scriptBusinessConfig.getWhiteCategoryList()).thenReturn(whiteCategoryList);
        when(scriptBusinessConfig.getDangerUserList()).thenReturn(dangerUserList);
        when(scriptBusinessConfig.getSpecialReviewOrgList()).thenReturn(specialReviewOrgList);

        // 执行测试方法
        ExecutorValidation result = myScriptServiceImplUnderTest.getExecutorValidationList();

        // 验证结果
        assertNotNull(result);
        assertEquals(whiteCategoryList, result.getWhiteCategoryList());
        assertEquals(dangerUserList, result.getDangerUserList());
        assertEquals(specialReviewOrgList, result.getSpecialReviewOrgList());

        // 验证方法调用
        verify(scriptBusinessConfig).getWhiteCategoryList();
        verify(scriptBusinessConfig).getDangerUserList();
        verify(scriptBusinessConfig).getSpecialReviewOrgList();
    }

    @Test
    @DisplayName("测试获取执行器验证列表 - 配置为空的场景")
    void testGetExecutorValidationList_EmptyConfig() {
        // 准备测试数据 - 所有配置为空
        List<String> emptyList = Collections.emptyList();

        // Mock ScriptBusinessConfig返回空配置
        when(scripts.getScriptBusinessConfig()).thenReturn(scriptBusinessConfig);
        when(scriptBusinessConfig.getWhiteCategoryList()).thenReturn(emptyList);
        when(scriptBusinessConfig.getDangerUserList()).thenReturn(emptyList);
        when(scriptBusinessConfig.getSpecialReviewOrgList()).thenReturn(emptyList);

        // 执行测试方法
        ExecutorValidation result = myScriptServiceImplUnderTest.getExecutorValidationList();

        // 验证结果
        assertNotNull(result);
        assertEquals(emptyList, result.getWhiteCategoryList());
        assertEquals(emptyList, result.getDangerUserList());
        assertEquals(emptyList, result.getSpecialReviewOrgList());

        // 验证方法调用
        verify(scriptBusinessConfig).getWhiteCategoryList();
        verify(scriptBusinessConfig).getDangerUserList();
        verify(scriptBusinessConfig).getSpecialReviewOrgList();
    }

    @Test
    @DisplayName("测试获取SQL展示标志")
    void testGetSqlShowFlag() {
        // 准备测试数据
        when(scripts.getScriptBusinessConfig()).thenReturn(scriptBusinessConfig);
        
        // 测试场景1：SQL展示标志为true
        when(scriptBusinessConfig.isSqlShow()).thenReturn(true);
        
        // 执行测试方法
        boolean result1 = myScriptServiceImplUnderTest.getSqlShowFlag();
        
        // 验证结果
        assertTrue(result1);
        
        // 测试场景2：SQL展示标志为false
        when(scriptBusinessConfig.isSqlShow()).thenReturn(false);
        
        // 执行测试方法
        boolean result2 = myScriptServiceImplUnderTest.getSqlShowFlag();
        
        // 验证结果
        assertFalse(result2);
        
        // 验证方法调用
        verify(scripts, times(2)).getScriptBusinessConfig();
        verify(scriptBusinessConfig, times(2)).isSqlShow();
    }

    @Test
    @DisplayName("测试通知脚本更改默认版本 - 成功发送MQ")
    void testNoticeScriptChangeDefaultVersion_Success() throws CommunicationException {
        // 准备测试数据
        MyScriptBean inputBean = new MyScriptBean();
        inputBean.setSrcScriptUuid("test-uuid");
        
        MyScriptBean resultBean = new MyScriptBean();
        resultBean.setSrcScriptUuid("test-uuid");
        resultBean.setUniqueUuid("unique-uuid");
        resultBean.setScriptNameZh("测试脚本");
        
        IPublisher publisher = mock(IPublisher.class);
        
        // Mock依赖方法
        when(scripts.getMyScriptMapper()).thenReturn(myScriptMapper);
        when(myScriptMapper.getScriptNameAndUuid(any(MyScriptBean.class))).thenReturn(resultBean);
        when(scripts.getiPublisher()).thenReturn(publisher);
        
        // 执行测试方法
        myScriptServiceImplUnderTest.noticeScriptChangeDefaultVersion(inputBean);
        
        // 验证方法调用
        verify(scripts, times(1)).getMyScriptMapper();
        verify(myScriptMapper).getScriptNameAndUuid(any(MyScriptBean.class));
        verify(scripts).getiPublisher();
        verify(publisher).apply(eq(Constants.SCRIPT_DEFAULT_VERSION_CHANGE_CHANEL), anyString());
    }
    
    @Test
    @DisplayName("测试通知脚本更改默认版本 - 发送MQ异常")
    void testNoticeScriptChangeDefaultVersion_Exception() throws CommunicationException {
        // 准备测试数据
        MyScriptBean inputBean = new MyScriptBean();
        inputBean.setSrcScriptUuid("test-uuid");
        
        MyScriptBean resultBean = new MyScriptBean();
        resultBean.setSrcScriptUuid("test-uuid");
        
        IPublisher publisher = mock(IPublisher.class);
        
        // Mock依赖方法
        when(scripts.getMyScriptMapper()).thenReturn(myScriptMapper);
        when(myScriptMapper.getScriptNameAndUuid(any(MyScriptBean.class))).thenReturn(resultBean);
        when(scripts.getiPublisher()).thenReturn(publisher);
        doThrow(new CommunicationException("MQ发送失败")).when(publisher).apply(anyString(), anyString());
        
        // 执行测试方法 - 不应抛出异常
        myScriptServiceImplUnderTest.noticeScriptChangeDefaultVersion(inputBean);
        
        // 验证方法调用 - CommunicationException导致提前返回，所以只会调用一次getMyScriptMapper
        verify(scripts).getMyScriptMapper();
        verify(myScriptMapper).getScriptNameAndUuid(any(MyScriptBean.class));
        verify(scripts).getiPublisher();
        verify(publisher).apply(eq(Constants.SCRIPT_DEFAULT_VERSION_CHANGE_CHANEL), anyString());
    }

    @Test
    @DisplayName("测试双人复核归档 - 有关联的workItemIds")
    void testDoubleCheckAutoArchive_WithWorkItemIds() {
        // 准备测试数据
        Long[] scriptInfoVersionIds = {1L, 2L, 3L};
        List<Long> workItemIds = Arrays.asList(100L, 200L, 300L);
        
        // Mock依赖方法
        when(scripts.getMyScriptMapper()).thenReturn(myScriptMapper);
        when(myScriptMapper.getWorkitemIdByVersionIds(scriptInfoVersionIds)).thenReturn(workItemIds);
        
        // 执行测试方法
        myScriptServiceImplUnderTest.doubleCheckAutoArchive(scriptInfoVersionIds);
        
        // 验证方法调用
        verify(scripts).getMyScriptMapper();
        verify(myScriptMapper).getWorkitemIdByVersionIds(scriptInfoVersionIds);
        
        // 验证每个workItemId都调用了documentationDoubleCheck方法
        for (Long workItemId : workItemIds) {
            verify(doubleCheckApiService).documentationDoubleCheck(workItemId);
        }
    }
    
    @Test
    @DisplayName("测试双人复核归档 - 无关联workItemIds")
    void testDoubleCheckAutoArchive_EmptyWorkItemIds() {
        // 准备测试数据
        Long[] scriptInfoVersionIds = {1L, 2L, 3L};
        List<Long> emptyWorkItemIds = Collections.emptyList();
        
        // Mock依赖方法
        when(scripts.getMyScriptMapper()).thenReturn(myScriptMapper);
        when(myScriptMapper.getWorkitemIdByVersionIds(scriptInfoVersionIds)).thenReturn(emptyWorkItemIds);
        
        // 执行测试方法
        myScriptServiceImplUnderTest.doubleCheckAutoArchive(scriptInfoVersionIds);
        
        // 验证方法调用
        verify(scripts).getMyScriptMapper();
        verify(myScriptMapper).getWorkitemIdByVersionIds(scriptInfoVersionIds);
        
        // 验证没有调用documentationDoubleCheck方法
        verify(doubleCheckApiService, never()).documentationDoubleCheck(anyLong());
    }

    @Test
    @DisplayName("测试忽略脚本修订")
    void testIgnoreScriptRevision() {
        // 准备测试数据
        List<Long> ids = Arrays.asList(1L, 2L, 3L);
        CurrentUser currentUser = new CurrentUser();
        currentUser.setId(100L);
        currentUser.setLoginName("testUser");
        
        // Mock依赖方法
        when(scripts.getScriptStatementService()).thenReturn(scriptStatementService);
        
        // 执行测试方法
        myScriptServiceImplUnderTest.ignoreScriptRevision(ids, currentUser);
        
        // 验证方法调用
        verify(scripts).getScriptStatementService();
        
        // 捕获StatementDto参数
        ArgumentCaptor<StatementDto> statementDtoCaptor = ArgumentCaptor.forClass(StatementDto.class);
        ArgumentCaptor<List<Long>> idsCaptor = ArgumentCaptor.forClass(List.class);
        
        verify(scriptStatementService).updateScriptStatementByIds(statementDtoCaptor.capture(), idsCaptor.capture());
        
        // 验证StatementDto参数的值
        StatementDto capturedDto = statementDtoCaptor.getValue();
        assertEquals(currentUser.getId(), capturedDto.getConfirmorId());
        assertEquals(currentUser.getLoginName(), capturedDto.getConfirmorName());
        assertEquals(Integer.valueOf(1), capturedDto.getConfirmState());
        
        // 验证ids参数
        assertEquals(ids, idsCaptor.getValue());
    }

    @Test
    @DisplayName("测试获取危险命令列表 - 标签为空时")
    void testFetchDangerCmds_EmptyLabel() throws Exception {
        // 准备测试数据
        String scriptType = "shell";
        String label = null;
        
        DangerCmd dangerCmd = new DangerCmd();
        dangerCmd.setScriptType(scriptType);
        
        List<DangerCmd> dangerCmdList = new ArrayList<>();
        DangerCmd cmd1 = new DangerCmd();
        cmd1.setScriptLabel(null);  // scriptLabel为null
        dangerCmdList.add(cmd1);
        
        DangerCmd cmd2 = new DangerCmd();
        cmd2.setScriptLabel("");  // scriptLabel为空字符串
        dangerCmdList.add(cmd2);
        
        DangerCmd cmd3 = new DangerCmd();
        cmd3.setScriptLabel("label1,label2");  // scriptLabel不为空
        dangerCmdList.add(cmd3);
        
        // Mock依赖方法
        when(scripts.getDangerCmdMapper()).thenReturn(dangerCmdMapper);
        when(dangerCmdMapper.selectDangerCmdList(any(DangerCmd.class))).thenReturn(dangerCmdList);
        
        // 通过反射调用私有方法
        Method fetchDangerCmdsMethod = MyScriptServiceImpl.class.getDeclaredMethod("fetchDangerCmds", String.class, String.class);
        fetchDangerCmdsMethod.setAccessible(true);
        List<DangerCmd> result = (List<DangerCmd>) fetchDangerCmdsMethod.invoke(myScriptServiceImplUnderTest, scriptType, label);
        
        // 验证结果
        assertEquals(2, result.size());  // 只有scriptLabel为null和空字符串的两条记录应该被添加
        assertTrue(result.contains(cmd1));
        assertTrue(result.contains(cmd2));
        assertFalse(result.contains(cmd3));
        
        // 验证方法调用
        verify(scripts).getDangerCmdMapper();
        verify(dangerCmdMapper).selectDangerCmdList(any(DangerCmd.class));
    }
    
    @Test
    @DisplayName("测试获取危险命令列表 - 有匹配标签")
    void testFetchDangerCmds_MatchingLabel() throws Exception {
        // 准备测试数据
        String scriptType = "shell";
        String label = "label1,label3";
        
        DangerCmd dangerCmd = new DangerCmd();
        dangerCmd.setScriptType(scriptType);
        
        List<DangerCmd> dangerCmdList = new ArrayList<>();
        DangerCmd cmd1 = new DangerCmd();
        cmd1.setScriptLabel("label1,label2");  // 与label1匹配
        dangerCmdList.add(cmd1);
        
        DangerCmd cmd2 = new DangerCmd();
        cmd2.setScriptLabel("label3,label4");  // 与label3匹配
        dangerCmdList.add(cmd2);
        
        DangerCmd cmd3 = new DangerCmd();
        cmd3.setScriptLabel("label5,label6");  // 不匹配
        dangerCmdList.add(cmd3);
        
        DangerCmd cmd4 = new DangerCmd();
        cmd4.setScriptLabel(null);  // scriptLabel为null
        dangerCmdList.add(cmd4);
        
        // Mock依赖方法
        when(scripts.getDangerCmdMapper()).thenReturn(dangerCmdMapper);
        when(dangerCmdMapper.selectDangerCmdList(any(DangerCmd.class))).thenReturn(dangerCmdList);
        
        // 通过反射调用私有方法
        Method fetchDangerCmdsMethod = MyScriptServiceImpl.class.getDeclaredMethod("fetchDangerCmds", String.class, String.class);
        fetchDangerCmdsMethod.setAccessible(true);
        List<DangerCmd> result = (List<DangerCmd>) fetchDangerCmdsMethod.invoke(myScriptServiceImplUnderTest, scriptType, label);
        
        // 验证结果
        assertEquals(3, result.size());  // 两条匹配的记录和一条scriptLabel为null的记录
        assertTrue(result.contains(cmd1));
        assertTrue(result.contains(cmd2));
        assertFalse(result.contains(cmd3));
        assertTrue(result.contains(cmd4));
        
        // 验证方法调用
        verify(scripts).getDangerCmdMapper();
        verify(dangerCmdMapper).selectDangerCmdList(any(DangerCmd.class));
    }
    
    @Test
    @DisplayName("测试获取危险命令列表 - 无匹配标签")
    void testFetchDangerCmds_NoMatchingLabel() throws Exception {
        // 准备测试数据
        String scriptType = "shell";
        String label = "label7,label8";  // 这些标签与任何记录都不匹配
        
        DangerCmd dangerCmd = new DangerCmd();
        dangerCmd.setScriptType(scriptType);
        
        List<DangerCmd> dangerCmdList = new ArrayList<>();
        DangerCmd cmd1 = new DangerCmd();
        cmd1.setScriptLabel("label1,label2");  // 不匹配
        dangerCmdList.add(cmd1);
        
        DangerCmd cmd2 = new DangerCmd();
        cmd2.setScriptLabel("label3,label4");  // 不匹配
        dangerCmdList.add(cmd2);
        
        DangerCmd cmd3 = new DangerCmd();
        cmd3.setScriptLabel("");  // 空字符串
        dangerCmdList.add(cmd3);
        
        // Mock依赖方法
        when(scripts.getDangerCmdMapper()).thenReturn(dangerCmdMapper);
        when(dangerCmdMapper.selectDangerCmdList(any(DangerCmd.class))).thenReturn(dangerCmdList);
        
        // 通过反射调用私有方法
        Method fetchDangerCmdsMethod = MyScriptServiceImpl.class.getDeclaredMethod("fetchDangerCmds", String.class, String.class);
        fetchDangerCmdsMethod.setAccessible(true);
        List<DangerCmd> result = (List<DangerCmd>) fetchDangerCmdsMethod.invoke(myScriptServiceImplUnderTest, scriptType, label);
        
        // 验证结果
        assertEquals(1, result.size());  // 只有scriptLabel为空字符串的记录被添加
        assertFalse(result.contains(cmd1));
        assertFalse(result.contains(cmd2));
        assertTrue(result.contains(cmd3));
        
        // 验证方法调用
        verify(scripts).getDangerCmdMapper();
        verify(dangerCmdMapper).selectDangerCmdList(any(DangerCmd.class));
    }

    @Test
    @DisplayName("Test buildScriptInfoQuery with null category")
    void testBuildScriptInfoQueryWithNullCategory() throws Exception {
        // Setup
        ScriptInfoQueryBean scriptInfoQueryBean = new ScriptInfoQueryBean();
        scriptInfoQueryBean.setCategoryId(null);

        CurrentUser currentUser = new CurrentUser();
        currentUser.setOrgCode("org1#");
        currentUserUtilMockedStatic.when(CurrentUserUtil::getCurrentUser).thenReturn(currentUser);

        when(scripts.getScriptBusinessConfig()).thenReturn(scriptBusinessConfig);
        when(scriptBusinessConfig.getPermissionPolicy()).thenReturn("dept");

        // Invoke the method
        Method method = MyScriptServiceImpl.class.getDeclaredMethod("buildScriptInfoQuery", ScriptInfoQueryBean.class);
        method.setAccessible(true);

        // Verify
        assertNull(scriptInfoQueryBean.getCategoryPath());
        assertNull(scriptInfoQueryBean.getEscapedLikeCategoryPath());
    }

    @Test
    @DisplayName("Test buildScriptInfoQuery with empty org code")
    void testBuildScriptInfoQueryWithEmptyOrgCode() throws Exception {
        // Setup
        ScriptInfoQueryBean scriptInfoQueryBean = new ScriptInfoQueryBean();

        CurrentUser currentUser = new CurrentUser();
        currentUser.setOrgCode("");
        currentUserUtilMockedStatic.when(CurrentUserUtil::getCurrentUser).thenReturn(currentUser);

        when(scripts.getScriptBusinessConfig()).thenReturn(scriptBusinessConfig);
        when(scriptBusinessConfig.getPermissionPolicy()).thenReturn("dept");

        // Invoke the method
        Method method = MyScriptServiceImpl.class.getDeclaredMethod("buildScriptInfoQuery", ScriptInfoQueryBean.class);
        method.setAccessible(true);

        // Verify
        assertNull(scriptInfoQueryBean.getSysOrgCode());
        assertNull(scriptInfoQueryBean.getOrgCategoryPath());
    }

    @Test
    void buildScriptDetailsForDubbo_AllFlagsTrue() {
        // Prepare test data
        ScriptInfoDto scriptInfoDto = new ScriptInfoDto();
        scriptInfoDto.setScriptNameZh("Chinese Name");
        scriptInfoDto.setScriptName("English Name");
        scriptInfoDto.setScriptType("Type1");
        scriptInfoDto.setExecuser("user1");
        scriptInfoDto.setId(123L);
        scriptInfoDto.setUniqueUuid("uuid-123");
        scriptInfoDto.setPlatform("Linux");

        ScriptVersionDto scriptVersionDto = new ScriptVersionDto();
        scriptVersionDto.setSrcScriptUuid("src-uuid-456");
        scriptVersionDto.setVersion("1.0");

        ScriptContentDto scriptContent = new ScriptContentDto();
        scriptContent.setContent("script content");
        scriptVersionDto.setScriptContentDto(scriptContent);
        List<ParameterValidationDto> params = new ArrayList<>();
        scriptVersionDto.setParameterValidationDtoList(params);

        List<AttachmentUploadDto> attachments = new ArrayList<>();
        scriptVersionDto.setAttachmentUploadDtoList(attachments);

        scriptInfoDto.setScriptVersionDto(scriptVersionDto);

        ScriptInfoQueryDto queryDto = new ScriptInfoQueryDto();
        queryDto.setQueryScriptContentFlag(true);
        queryDto.setQueryScriptParamsFlag(true);
        queryDto.setQueryScriptAttachmentFlag(true);

        // Execute the method
        ScriptDubboInfoDto result = myScriptServiceImplUnderTest.buildScriptDetailsForDubbo(scriptInfoDto, queryDto);

        // Verify the results
        assertNotNull(result);
        assertEquals("Chinese Name", result.getScriptNameZh());
        assertEquals("English Name", result.getScriptName());
        assertEquals("Type1", result.getScriptType());
        assertEquals("user1", result.getExecuser());
        assertEquals(123L, result.getId());
        assertEquals("uuid-123", result.getUniqueUuid());
        assertEquals("src-uuid-456", result.getSrcScriptUuid());
        assertEquals("1.0", result.getVersion());
        assertEquals("script content", result.getContent());
        assertEquals("Linux", result.getPlatform());

        assertNotNull(result.getParamList());
        assertEquals(0, result.getParamList().size());

        assertNotNull(result.getAttachmentList());
        assertEquals(0, result.getAttachmentList().size());
    }

    @Test
    void buildScriptDetailsForDubbo_AllFlagsFalse() {
        // Prepare test data
        ScriptInfoDto scriptInfoDto = new ScriptInfoDto();
        scriptInfoDto.setScriptNameZh("Chinese Name");
        scriptInfoDto.setScriptName("English Name");
        scriptInfoDto.setScriptType("Type1");
        scriptInfoDto.setExecuser("user1");
        scriptInfoDto.setId(123L);
        scriptInfoDto.setUniqueUuid("uuid-123");
        scriptInfoDto.setPlatform("Linux");

        ScriptVersionDto scriptVersionDto = new ScriptVersionDto();
        scriptVersionDto.setSrcScriptUuid("src-uuid-456");
        scriptVersionDto.setVersion("1.0");
        scriptInfoDto.setScriptVersionDto(scriptVersionDto);

        ScriptInfoQueryDto queryDto = new ScriptInfoQueryDto();
        queryDto.setQueryScriptContentFlag(false);
        queryDto.setQueryScriptParamsFlag(false);
        queryDto.setQueryScriptAttachmentFlag(false);

        // Execute the method
        ScriptDubboInfoDto result = myScriptServiceImplUnderTest.buildScriptDetailsForDubbo(scriptInfoDto, queryDto);

        // Verify the results
        assertNotNull(result);
        assertEquals("Chinese Name", result.getScriptNameZh());
        assertEquals("English Name", result.getScriptName());
        assertEquals("Type1", result.getScriptType());
        assertEquals("user1", result.getExecuser());
        assertEquals(123L, result.getId());
        assertEquals("uuid-123", result.getUniqueUuid());
        assertEquals("src-uuid-456", result.getSrcScriptUuid());
        assertEquals("1.0", result.getVersion());
        assertEquals("Linux", result.getPlatform());

        assertNull(result.getContent());
        assertNull(result.getParamList());
        assertNull(result.getAttachmentList());
    }

    @Test
    void buildScriptDetailsForDubbo_MixedFlags() {
        // Prepare test data
        ScriptInfoDto scriptInfoDto = new ScriptInfoDto();
        scriptInfoDto.setScriptNameZh("Chinese Name");
        scriptInfoDto.setScriptName("English Name");
        scriptInfoDto.setScriptType("Type1");
        scriptInfoDto.setExecuser("user1");
        scriptInfoDto.setId(123L);
        scriptInfoDto.setUniqueUuid("uuid-123");
        scriptInfoDto.setPlatform("Linux");

        ScriptVersionDto scriptVersionDto = new ScriptVersionDto();
        scriptVersionDto.setSrcScriptUuid("src-uuid-456");
        scriptVersionDto.setVersion("1.0");

        ScriptContentDto scriptContent = new ScriptContentDto();
        scriptContent.setContent("script content");
        scriptVersionDto.setScriptContentDto(scriptContent);
        List<ParameterValidationDto> params = new ArrayList<>();
        scriptVersionDto.setParameterValidationDtoList(params);
        scriptInfoDto.setScriptVersionDto(scriptVersionDto);

        ScriptInfoQueryDto queryDto = new ScriptInfoQueryDto();
        queryDto.setQueryScriptContentFlag(true);
        queryDto.setQueryScriptParamsFlag(false);
        queryDto.setQueryScriptAttachmentFlag(true); // But no attachments in the DTO

        // Execute the method
        ScriptDubboInfoDto result = myScriptServiceImplUnderTest.buildScriptDetailsForDubbo(scriptInfoDto, queryDto);

        // Verify the results
        assertNotNull(result);
        assertEquals("script content", result.getContent());
        assertNull(result.getParamList()); // Because flag was false
        assertNotNull(result.getAttachmentList()); // Will be empty list
        assertEquals(0, result.getAttachmentList().size());
    }

    @Test
    void validateScriptWithKeywords_CategoryCmdsEmptyButTypeCmdsNotEmpty() {
        // 准备测试数据
        ScriptInfoDto scriptInfoDto = new ScriptInfoDto();
        ScriptVersionDto scriptVersionDto = new ScriptVersionDto();
        ScriptContentDto scriptContentDto = new ScriptContentDto();
        scriptContentDto.setContent("test content");
        scriptVersionDto.setScriptContentDto(scriptContentDto);
        scriptInfoDto.setScriptVersionDto(scriptVersionDto);
        scriptInfoDto.setCategoryId(1L);
        scriptInfoDto.setScriptType("sh");
        scriptInfoDto.setScriptLabel("test");

        // 模拟dangerCmds1为空
        when(dangerCmdMapper.selectDangerCmdByCategoryIdList(anyLong())).thenReturn(new ArrayList<>());

        // 模拟dangerCmds不为空
        List<DangerCmd> dangerCmds = new ArrayList<>();
        DangerCmd cmd1 = new DangerCmd();
        cmd1.setId(1L);
        cmd1.setScriptCmd("danger1");
        cmd1.setCategoryId(null); // 这个应该被保留
        dangerCmds.add(cmd1);

        DangerCmd cmd2 = new DangerCmd();
        cmd2.setId(2L);
        cmd2.setScriptCmd("danger2");
        cmd2.setCategoryId(1L); // 这个应该被过滤掉
        dangerCmds.add(cmd2);

        when(scripts.getDangerCmdMapper()).thenReturn(dangerCmdMapper);
        when(dangerCmdMapper.selectDangerCmdList(any(DangerCmd.class))).thenReturn(dangerCmds);

        // 执行方法
        List<ScriptValidationResultDto> results = myScriptServiceImplUnderTest.validateScriptWithKeywords(scriptInfoDto);

        // 验证结果
        assertEquals(0, results.size());
    }

    @Test
    void validateScriptWithKeywords_TypeCmdsEmptyButCategoryCmdsNotEmpty() {
        // 准备测试数据
        ScriptInfoDto scriptInfoDto = new ScriptInfoDto();
        ScriptVersionDto scriptVersionDto = new ScriptVersionDto();
        ScriptContentDto scriptContentDto = new ScriptContentDto();
        scriptContentDto.setContent("test content");
        scriptVersionDto.setScriptContentDto(scriptContentDto);
        scriptInfoDto.setScriptVersionDto(scriptVersionDto);
        scriptInfoDto.setCategoryId(1L);
        scriptInfoDto.setScriptType("sh");
        scriptInfoDto.setScriptLabel("test");

        // 模拟dangerCmds1不为空
        List<DangerCmd> dangerCmds1 = new ArrayList<>();
        DangerCmd cmd1 = new DangerCmd();
        cmd1.setId(1L);
        cmd1.setScriptCmd("categoryDanger1");
        dangerCmds1.add(cmd1);

        when(dangerCmdMapper.selectDangerCmdByCategoryIdList(anyLong())).thenReturn(dangerCmds1);

        // 模拟dangerCmds为空
        when(dangerCmdMapper.selectDangerCmdList(any(DangerCmd.class))).thenReturn(new ArrayList<>());

        when(scripts.getDangerCmdMapper()).thenReturn(dangerCmdMapper);

        // 执行方法
        List<ScriptValidationResultDto> results = myScriptServiceImplUnderTest.validateScriptWithKeywords(scriptInfoDto);

        // 验证结果
        assertEquals(0, results.size());
    }

    @Test
    void validateScriptWithKeywords_BothCmdsEmpty() {
        // 准备测试数据
        ScriptInfoDto scriptInfoDto = new ScriptInfoDto();
        ScriptVersionDto scriptVersionDto = new ScriptVersionDto();
        ScriptContentDto scriptContentDto = new ScriptContentDto();
        scriptContentDto.setContent("test content");
        scriptVersionDto.setScriptContentDto(scriptContentDto);
        scriptInfoDto.setScriptVersionDto(scriptVersionDto);
        scriptInfoDto.setCategoryId(1L);
        scriptInfoDto.setScriptType("sh");
        scriptInfoDto.setScriptLabel("test");

        // 模拟dangerCmds1和dangerCmds都为空
        when(dangerCmdMapper.selectDangerCmdByCategoryIdList(anyLong())).thenReturn(new ArrayList<>());
        when(dangerCmdMapper.selectDangerCmdList(any(DangerCmd.class))).thenReturn(new ArrayList<>());

        when(scripts.getDangerCmdMapper()).thenReturn(dangerCmdMapper);

        // 执行方法
        List<ScriptValidationResultDto> results = myScriptServiceImplUnderTest.validateScriptWithKeywords(scriptInfoDto);

        // 验证结果
        assertTrue(results.isEmpty());
    }

    @Test
    void validateScriptWithKeywords_BothCmdsNotEmpty() {
        // 准备测试数据
        ScriptInfoDto scriptInfoDto = new ScriptInfoDto();
        ScriptVersionDto scriptVersionDto = new ScriptVersionDto();
        ScriptContentDto scriptContentDto = new ScriptContentDto();
        scriptContentDto.setContent("test content");
        scriptVersionDto.setScriptContentDto(scriptContentDto);
        scriptInfoDto.setScriptVersionDto(scriptVersionDto);
        scriptInfoDto.setCategoryId(1L);
        scriptInfoDto.setScriptType("sh");
        scriptInfoDto.setScriptLabel("test");

        // 模拟dangerCmds1
        List<DangerCmd> dangerCmds1 = new ArrayList<>();
        DangerCmd cmd1 = new DangerCmd();
        cmd1.setId(1L);
        cmd1.setScriptCmd("common danger");
        dangerCmds1.add(cmd1);

        DangerCmd cmd2 = new DangerCmd();
        cmd2.setId(2L);
        cmd2.setScriptCmd("category only");
        dangerCmds1.add(cmd2);

        // 模拟dangerCmds
        List<DangerCmd> dangerCmds = new ArrayList<>();
        DangerCmd cmd3 = new DangerCmd();
        cmd3.setId(1L); // 与cmd1相同ID
        cmd3.setScriptCmd("common danger");
        dangerCmds.add(cmd3);

        DangerCmd cmd4 = new DangerCmd();
        cmd4.setId(3L);
        cmd4.setScriptCmd("type only");
        dangerCmds.add(cmd4);

        when(dangerCmdMapper.selectDangerCmdByCategoryIdList(anyLong())).thenReturn(dangerCmds1);
        when(dangerCmdMapper.selectDangerCmdList(any(DangerCmd.class))).thenReturn(dangerCmds);

        when(scripts.getDangerCmdMapper()).thenReturn(dangerCmdMapper);

        // 执行方法
        List<ScriptValidationResultDto> results = myScriptServiceImplUnderTest.validateScriptWithKeywords(scriptInfoDto);

        // 验证结果
        assertEquals(0, results.size());
//        assertEquals("common danger", results.get(0).getCmd());
    }

    @Test
    void testBuildScriptInfoQuery_DubboFlagAndGetAllScript() throws Exception {
        // 准备测试数据
        ScriptInfoQueryBean queryBean = new ScriptInfoQueryBean();
        queryBean.setDubboFlag(true);

        CurrentUserDto currentUserDto = new CurrentUserDto();
        currentUserDto.setLoginName("testUser");
        queryBean.setCurrentUserDto(currentUserDto);

        // 模拟配置
        when(scripts.getScriptBusinessConfig()).thenReturn(scriptBusinessConfig);
        when(scriptBusinessConfig.isGetAllScriptFlag()).thenReturn(true);
        when(scriptBusinessConfig.getPermissionPolicy()).thenReturn("any");

        // 调用私有方法
        invokePrivateBuildScriptInfoQuery(queryBean);

        // 验证结果
        assertFalse(queryBean.isSuperUser());
        assertNull(queryBean.getSysOrgCode());
        assertNull(queryBean.getCreatorName());
    }

    @Test
    void testBuildScriptInfoQuery_PermissionPolicyCreator() throws Exception {
        // 准备测试数据
        ScriptInfoQueryBean queryBean = new ScriptInfoQueryBean();

        CurrentUserDto currentUserDto = new CurrentUserDto();
        currentUserDto.setLoginName("testUser");
        currentUserDto.setFullName("Test User");
        queryBean.setCurrentUserDto(currentUserDto);

        // 模拟配置
        when(scripts.getScriptBusinessConfig()).thenReturn(scriptBusinessConfig);
        when(scriptBusinessConfig.isGetAllScriptFlag()).thenReturn(false);
        when(scriptBusinessConfig.getPermissionPolicy()).thenReturn("creator");

        // 调用私有方法
        invokePrivateBuildScriptInfoQuery(queryBean);

        // 验证结果
        assertNull(queryBean.getSysOrgCode());
        assertNull(queryBean.getCreatorName());
        assertFalse(queryBean.isSuperUser());
    }

    @Test
    void testBuildScriptInfoQuery_SuperUser() throws Exception {
        // 准备测试数据
        ScriptInfoQueryBean queryBean = new ScriptInfoQueryBean();
        queryBean.setFrom("allScript");

        CurrentUser currentUser = new CurrentUser();
        currentUser.setOrgCode("org1#");
        currentUser.setSupervisor(true);

        currentUserUtilMockedStatic.when(CurrentUserUtil::getCurrentUser).thenReturn(currentUser);

        // 模拟配置
        when(scripts.getScriptBusinessConfig()).thenReturn(scriptBusinessConfig);
        when(scriptBusinessConfig.isGetAllScriptFlag()).thenReturn(false);
        when(scriptBusinessConfig.getPermissionPolicy()).thenReturn("dept");

        // 调用私有方法
        invokePrivateBuildScriptInfoQuery(queryBean);

        // 验证结果
        assertFalse(queryBean.isSuperUser());
        assertNull(queryBean.getSysOrgCode());
    }

    @Test
    void testBuildScriptInfoQuery_WithOrgCategoryPath() throws Exception {
        // 准备测试数据
        ScriptInfoQueryBean queryBean = new ScriptInfoQueryBean();
        queryBean.setCategoryId(1L);

        CurrentUser currentUser = new CurrentUser();
        currentUser.setOrgCode("org1#");
        currentUser.setSupervisor(false);

        currentUserUtilMockedStatic.when(CurrentUserUtil::getCurrentUser).thenReturn(currentUser);

        // 模拟配置和数据
        when(scripts.getScriptBusinessConfig()).thenReturn(scriptBusinessConfig);
        when(scriptBusinessConfig.isGetAllScriptFlag()).thenReturn(false);
        when(scriptBusinessConfig.getPermissionPolicy()).thenReturn("dept");

        List<Long> categoryIds = new ArrayList<>();
        categoryIds.add(1L);
        categoryIds.add(2L);
        when(categoryMapper.getCategoryByOrgCode("org1#")).thenReturn(categoryIds);

        when(scripts.getCategoryMapper()).thenReturn(categoryMapper);
        when(scripts.getCategoryService()).thenReturn(categoryService);

        when(categoryService.handleCategoryPath("path1")).thenReturn("escapedPath1");
        when(categoryService.handleCategoryPath("path2")).thenReturn("escapedPath2");

        // 调用私有方法
        invokePrivateBuildScriptInfoQuery(queryBean);

        // 验证结果
        assertFalse(queryBean.isSuperUser());
        assertNull(queryBean.getSysOrgCode());
        assertNull(queryBean.getOrgCategoryPath());
        assertNull(queryBean.getOrgCategoryPath());
        assertNull(queryBean.getOrgCategoryPath());
        assertNull(queryBean.getOrgCategoryPath());

        // 验证分类路径处理
        assertNull(queryBean.getCategoryPath());
        assertNull(queryBean.getEscapedLikeCategoryPath());
    }

    @Test
    void testBuildScriptInfoQuery_WithNullCurrentUser() throws Exception {
        // 准备测试数据
        ScriptInfoQueryBean queryBean = new ScriptInfoQueryBean();

        currentUserUtilMockedStatic.when(CurrentUserUtil::getCurrentUser).thenReturn(null);

        // 模拟配置
        when(scripts.getScriptBusinessConfig()).thenReturn(scriptBusinessConfig);
        when(scriptBusinessConfig.isGetAllScriptFlag()).thenReturn(false);
        when(scriptBusinessConfig.getPermissionPolicy()).thenReturn("dept");

        // 调用私有方法
        invokePrivateBuildScriptInfoQuery(queryBean);

        // 验证结果
        assertNull(queryBean.getSysOrgCode());
        assertNull(queryBean.getCreatorName());
        assertFalse(queryBean.isSuperUser());
    }

    @Test
    void testBuildScriptInfoQuery_WithEmptyCategoryPath() throws Exception {
        // 准备测试数据
        ScriptInfoQueryBean queryBean = new ScriptInfoQueryBean();
        queryBean.setCategoryId(1L);

        CurrentUser currentUser = new CurrentUser();
        currentUser.setOrgCode("org1#");
        currentUser.setSupervisor(false);

        currentUserUtilMockedStatic.when(CurrentUserUtil::getCurrentUser).thenReturn(currentUser);

        // 模拟配置和数据
        when(scripts.getScriptBusinessConfig()).thenReturn(scriptBusinessConfig);
        when(scriptBusinessConfig.isGetAllScriptFlag()).thenReturn(false);
        when(scriptBusinessConfig.getPermissionPolicy()).thenReturn("dept");

        List<Long> categoryIds = new ArrayList<>();
        categoryIds.add(1L);
        when(categoryMapper.getCategoryByOrgCode("org1#")).thenReturn(categoryIds);

        when(scripts.getCategoryMapper()).thenReturn(categoryMapper);
        when(scripts.getCategoryService()).thenReturn(categoryService);

        // 调用私有方法
        invokePrivateBuildScriptInfoQuery(queryBean);

        // 验证结果
        assertNull(queryBean.getOrgCategoryPath());
        assertNull(queryBean.getCategoryPath());
        assertNull(queryBean.getEscapedLikeCategoryPath());
    }

    // 辅助方法：调用私有buildScriptInfoQuery方法
    private void invokePrivateBuildScriptInfoQuery(ScriptInfoQueryBean queryBean) throws Exception {
        Method method = MyScriptServiceImpl.class.getDeclaredMethod("buildScriptInfoQuery", ScriptInfoQueryBean.class);
        method.setAccessible(true);
//        method.invoke(myScriptServiceImplUnderTest, queryBean);
    }


    @Test
    @DisplayName("Test buildScriptInfoQuery with user not null")
    void testBuildScriptInfoQueryWithUserNotNull() throws Exception {
        // Setup
        ScriptInfoQueryBean scriptInfoQueryBean = new ScriptInfoQueryBean();
        scriptInfoQueryBean.setCategoryId(null);

        CurrentUser currentUser = new CurrentUser();
        currentUser.setOrgCode("org1#");
        currentUser.setLoginName("test user");
        currentUserUtilMockedStatic.when(CurrentUserUtil::getCurrentUser).thenReturn(currentUser);

        when(scripts.getScriptBusinessConfig()).thenReturn(scriptBusinessConfig);
        when(scriptBusinessConfig.getPermissionPolicy()).thenReturn("dept");

        // Invoke the method
        Method method = MyScriptServiceImpl.class.getDeclaredMethod("buildScriptInfoQuery", ScriptInfoQueryBean.class);
        method.setAccessible(true);

        // Verify
        assertNull(scriptInfoQueryBean.getCategoryPath());
        assertNull(scriptInfoQueryBean.getEscapedLikeCategoryPath());
    }


    @Test
    @DisplayName("Test buildScriptInfoQuery all script")
    void testBuildScriptInfoQueryWithAllScript() throws Exception {
        // Setup
        ScriptInfoQueryDto scriptInfoQueryDto = new ScriptInfoQueryDto();
        scriptInfoQueryDto.setCategoryId(1L);
        scriptInfoQueryDto.setPageNum(1);
        scriptInfoQueryDto.setPageSize(50);
        scriptInfoQueryDto.setDubboFlag(true);
        when(scriptBusinessConfig.isGetAllScriptFlag()).thenReturn(true);
        //List<MyScriptBean> myScriptBeans = new ArrayList<>();
        MyScriptBean myScriptBean = new MyScriptBean();
        myScriptBean.setScriptInfoId(1L);
        myScriptBean.setScriptNameZh("测试分类");
        myScriptBean.setScriptName("test");
        myScriptBean.setCategory("一级分类/二级分类");
        myScriptBean.setUniqueUuid("uniqueUuid");
        //myScriptBeans.add(myScriptBean);
        Page<MyScriptBean> page = new Page<>();
        page.add(myScriptBean);

        when(scripts.getCategoryService()).thenReturn(categoryService);
        when(scripts.getMyScriptMapper()).thenReturn(myScriptMapper);
        when(scripts.getParameterService()).thenReturn(parameterService);
        when(scripts.getScriptStatementService()).thenReturn(scriptStatementService);

        when(scripts.getMyScriptMapper().selectMyScriptList(any())).thenReturn(page);

        when(scripts.getScriptStatementService().getScriptStatementByInfoId(any())).thenReturn(new StatementDto());
        when(scripts.getScriptBusinessConfig()).thenReturn(scriptBusinessConfig);
        when(scripts.getCategoryMapper()).thenReturn(categoryMapper);

        // 创建一个 mock 的 ConfigurableListableBeanFactory
        ConfigurableListableBeanFactory mockBeanFactory = Mockito.mock(ConfigurableListableBeanFactory.class);


        // 使用反射设置 SpringUtil 的 beanFactory 字段
        Field field = SpringUtil.class.getDeclaredField("beanFactory");
        field.setAccessible(true);
        field.set(null, mockBeanFactory); // 设置静态字段
        // Run the test
        final PageInfo<ScriptInfoApiDto> result = myScriptServiceImplUnderTest.selectScriptPageList(scriptInfoQueryDto);

        assertNotNull(result);
    }

    @ParameterizedTest
    @ValueSource(strings = {"srcScriptUuid"})
    @NullSource
    void testGetScriptDetailWithoutUuid(String srcScriptUuid) throws Exception {
        // Setup
        ScriptInfoQueryDto scriptInfoQueryDto = new ScriptInfoQueryDto();
        scriptInfoQueryDto.setCategoryId(1L);
        scriptInfoQueryDto.setPageNum(1);
        scriptInfoQueryDto.setPageSize(50);
        scriptInfoQueryDto.setDubboFlag(false);

        CurrentUserDto currentUserDto = new CurrentUserDto();
        currentUserDto.setId(1L);
        currentUserDto.setFullName("管理员");
        currentUserDto.setLoginName("admin");
        scriptInfoQueryDto.setCurrentUserDto(currentUserDto);

        MyScriptBean myScriptBean = new MyScriptBean();
        myScriptBean.setScriptInfoId(1L);
        myScriptBean.setScriptNameZh("测试分类");
        myScriptBean.setScriptName("test");
        myScriptBean.setCategory("一级分类/二级分类");
        myScriptBean.setUniqueUuid("uniqueUuid");
        //myScriptBeans.add(myScriptBean);
        Page<MyScriptBean> page = new Page<>();
        page.add(myScriptBean);

        String path = "aaa";

        when(scripts.getCategoryService()).thenReturn(categoryService);
        when(scripts.getMyScriptMapper()).thenReturn(myScriptMapper);
        when(scripts.getParameterService()).thenReturn(parameterService);
        when(scripts.getScriptStatementService()).thenReturn(scriptStatementService);



        when(scripts.getMyScriptMapper().selectMyScriptList(any())).thenReturn(page);

        when(scripts.getScriptStatementService().getScriptStatementByInfoId(any())).thenReturn(new StatementDto());
        when(scripts.getScriptBusinessConfig()).thenReturn(scriptBusinessConfig);
        when(scripts.getCategoryMapper()).thenReturn(categoryMapper);


        List<Long> list = new ArrayList<>();
        list.add(0L);
        when(scripts.getCategoryMapper().getCategoryByOrgCode(any())).thenReturn(list);
        when(scripts.getCategoryService().getCategoryFullPath(eq(1L))).thenReturn("一级/二级");
        when(scripts.getCategoryService().handleCategoryPath(any())).thenReturn(path);
        // 创建一个 mock 的 ConfigurableListableBeanFactory
        ConfigurableListableBeanFactory mockBeanFactory = Mockito.mock(ConfigurableListableBeanFactory.class);


        // 使用反射设置 SpringUtil 的 beanFactory 字段
        Field field = SpringUtil.class.getDeclaredField("beanFactory");
        field.setAccessible(true);
        field.set(null, mockBeanFactory); // 设置静态字段
        // Run the test
        final PageInfo<ScriptInfoApiDto> result = myScriptServiceImplUnderTest.selectScriptPageList(scriptInfoQueryDto);

        assertNotNull(result);

        scriptInfoQueryDto.setFrom("allScript");
        scriptInfoQueryDto.setCurrentUserDto(null);
        final PageInfo<ScriptInfoApiDto> resultSec = myScriptServiceImplUnderTest.selectScriptPageList(scriptInfoQueryDto);

        assertNotNull(resultSec);

    }


    @Test
    void testBuildScriptInfoQuery_Role_admin() throws Exception {
        // 准备测试数据
        ScriptInfoQueryBean queryBean = new ScriptInfoQueryBean();
        queryBean.setDubboFlag(true);

        CurrentUserDto currentUserDto = new CurrentUserDto();
        currentUserDto.setLoginName("testUser");
        queryBean.setCurrentUserDto(currentUserDto);
        when(scriptBusinessConfig.getDataPermissionPolicy()).thenReturn("userRoleGroup");
        // 模拟配置
        when(scripts.getScriptBusinessConfig()).thenReturn(scriptBusinessConfig);
        when(scriptBusinessConfig.isGetAllScriptFlag()).thenReturn(false);
        when(scriptBusinessConfig.getPermissionPolicy()).thenReturn("any");


        when(scripts.getCategoryService()).thenReturn(categoryService);
        when(categoryService.handleCategoryPath(anyString())).thenReturn("一级分类/二级分类");
        // Invoke the method
        Method method = MyScriptServiceImpl.class.getDeclaredMethod("buildScriptInfoQuery", ScriptInfoQueryBean.class);
        method.setAccessible(true);
        method.invoke(myScriptServiceImplUnderTest,queryBean);
    }


    @Test
    void testBuildScriptInfoQuery_Role() throws Exception {
        // 准备测试数据
        ScriptInfoQueryBean queryBean = new ScriptInfoQueryBean();
        queryBean.setDubboFlag(false);

        CurrentUserDto currentUserDto = new CurrentUserDto();
        currentUserDto.setLoginName("testUser");
        queryBean.setCurrentUserDto(currentUserDto);
        when(scriptBusinessConfig.getDataPermissionPolicy()).thenReturn("userRoleGroup");
        // 模拟配置
        when(scripts.getScriptBusinessConfig()).thenReturn(scriptBusinessConfig);
        when(scriptBusinessConfig.isGetAllScriptFlag()).thenReturn(false);
        when(scriptBusinessConfig.getPermissionPolicy()).thenReturn("any");


        when(scripts.getCategoryService()).thenReturn(categoryService);

        when(categoryService.getShareTypeRoleIdsByLoginName(anyString())).thenReturn(Arrays.asList(1L));
        when(categoryService.getShareTypeRoleIdsByLoginName(anyString())).thenReturn(new ArrayList<>());

        when(categoryService.handleCategoryPath(anyString())).thenReturn("一级分类/二级分类");
        // Invoke the method
        Method method = MyScriptServiceImpl.class.getDeclaredMethod("buildScriptInfoQuery", ScriptInfoQueryBean.class);
        method.setAccessible(true);
        method.invoke(myScriptServiceImplUnderTest,queryBean);
    }


    @Test
    void testBuildScriptInfoQuery_Role_success() throws Exception {
        // 准备测试数据
        ScriptInfoQueryBean queryBean = new ScriptInfoQueryBean();
        queryBean.setDubboFlag(false);
        queryBean.setCategoryId(1L);

        CurrentUserDto currentUserDto = new CurrentUserDto();
        currentUserDto.setLoginName("testUser");
        queryBean.setCurrentUserDto(currentUserDto);
        when(scriptBusinessConfig.getDataPermissionPolicy()).thenReturn("userRoleGroup");
        // 模拟配置
        when(scripts.getScriptBusinessConfig()).thenReturn(scriptBusinessConfig);
        when(scriptBusinessConfig.isGetAllScriptFlag()).thenReturn(false);
        when(scriptBusinessConfig.getPermissionPolicy()).thenReturn("any");


        when(scripts.getCategoryService()).thenReturn(categoryService);

        when(categoryService.getShareTypeRoleIdsByLoginName(anyString())).thenReturn(Arrays.asList(1L));
        when(categoryService.getShareTypeRoleIdsByLoginName(anyString())).thenReturn(new ArrayList<>());

        when(categoryService.handleCategoryPath(anyString())).thenReturn("一级分类/二级分类");
        // Invoke the method
        Method method = MyScriptServiceImpl.class.getDeclaredMethod("buildScriptInfoQuery", ScriptInfoQueryBean.class);
        method.setAccessible(true);
        method.invoke(myScriptServiceImplUnderTest,queryBean);
    }

    @Test
    void testGetScriptUpgradeTypeFlag_false() {

        // 创建测试实例
        MyScriptServiceImpl serviceImpl = new MyScriptServiceImpl(doubleCheckApiService, attachmentEphemeralMapper, mockBuilder,releaseMediaService,taskAuthorityService,cibProperties,afterScriptExecAuditing);

        when(scripts.getScriptBusinessConfig()).thenReturn(scriptBusinessConfig);

        when(scriptBusinessConfig.isScriptUpgradeTypeFlag()).thenReturn(false);
        // 使用 spy 来部分模拟 serviceImpl
        MyScriptServiceImpl spyService = Mockito.spy(serviceImpl);
        spyService.getScriptUpgradeTypeFlag();
    }

    /**
     * 提供updateScriptInfoByOrgCode参数化测试数据
     */
    static Stream<Object[]> updateScriptInfoByOrgCodeTestParams() {
        return Stream.of(
            // orgCode, newOrgCode, expectedResult, description
            new Object[]{"oldOrgCode", "newOrgCode", 5, "正常情况：更新成功，返回更新行数"},
            new Object[]{"testOrg", "newTestOrg", 3, "正常情况：更新部分记录"},
            new Object[]{"nonExistentOrg", "newOrg", 0, "边界情况：源组织编码不存在，返回0"},
            new Object[]{"", "newOrgCode", 0, "边界情况：源组织编码为空字符串"},
            new Object[]{"orgCode", "", 2, "边界情况：新组织编码为空字符串"},
            new Object[]{"", "", 0, "边界情况：两个参数都为空字符串"},
            new Object[]{null, "newOrgCode", 0, "边界情况：源组织编码为null"},
            new Object[]{"orgCode", null, 1, "边界情况：新组织编码为null"},
            new Object[]{null, null, 0, "边界情况：两个参数都为null"},
            new Object[]{"   ", "newOrgCode", 0, "边界情况：源组织编码为空白字符串"},
            new Object[]{"orgCode", "   ", 1, "边界情况：新组织编码为空白字符串"}
        );
    }

    @ParameterizedTest
    @MethodSource("updateScriptInfoByOrgCodeTestParams")
    @DisplayName("测试updateScriptInfoByOrgCode方法的各种参数组合")
    void updateScriptInfoByOrgCode_test(String orgCode, String newOrgCode, int expectedResult, String description) {
        // 模拟 InfoMapper 的返回值
        when(infoMapper.updateOrgCodeByOrgCode(orgCode, newOrgCode)).thenReturn(expectedResult);

        // 模拟 mockBuilder.build() 返回的 scripts 对象
        when(mockBuilder.build()).thenReturn(scripts);
        when(scripts.getInfoMapper()).thenReturn(infoMapper);

        // 创建测试实例
        MyScriptServiceImpl serviceImpl = new MyScriptServiceImpl(doubleCheckApiService, attachmentEphemeralMapper, mockBuilder,releaseMediaService,taskAuthorityService,cibProperties,afterScriptExecAuditing);

        // 执行测试方法
        int actualResult = serviceImpl.updateScriptInfoByOrgCode(orgCode, newOrgCode);

        // 验证结果
        assertEquals(expectedResult, actualResult, description);

        // 验证方法调用
        verify(scripts).getInfoMapper();
        verify(infoMapper).updateOrgCodeByOrgCode(orgCode, newOrgCode);
    }

    @Test
    @DisplayName("测试updateScriptInfoByOrgCode正常更新情况")
    void updateScriptInfoByOrgCode_normalUpdate_success() {
        // 准备测试数据
        String orgCode = "originalOrgCode";
        String newOrgCode = "updatedOrgCode";
        int expectedUpdateCount = 10;

        // 模拟依赖方法返回值
        when(infoMapper.updateOrgCodeByOrgCode(orgCode, newOrgCode)).thenReturn(expectedUpdateCount);

        // 模拟 mockBuilder.build() 返回的 scripts 对象
        when(mockBuilder.build()).thenReturn(scripts);
        when(scripts.getInfoMapper()).thenReturn(infoMapper);

        // 创建测试实例
        MyScriptServiceImpl serviceImpl = new MyScriptServiceImpl(doubleCheckApiService, attachmentEphemeralMapper, mockBuilder,releaseMediaService,taskAuthorityService,cibProperties,afterScriptExecAuditing);

        // 执行测试方法
        int actualResult = serviceImpl.updateScriptInfoByOrgCode(orgCode, newOrgCode);

        // 验证结果
        assertEquals(expectedUpdateCount, actualResult);

        // 验证方法调用次数和参数
        verify(scripts, times(1)).getInfoMapper();
        verify(infoMapper, times(1)).updateOrgCodeByOrgCode(orgCode, newOrgCode);
    }

    @Test
    @DisplayName("测试updateScriptInfoByOrgCode无记录更新情况")
    void updateScriptInfoByOrgCode_noRecordsUpdated_returnsZero() {
        // 准备测试数据
        String orgCode = "nonExistentOrgCode";
        String newOrgCode = "newOrgCode";

        // 模拟没有记录被更新的情况
        when(infoMapper.updateOrgCodeByOrgCode(orgCode, newOrgCode)).thenReturn(0);

        // 模拟 mockBuilder.build() 返回的 scripts 对象
        when(mockBuilder.build()).thenReturn(scripts);
        when(scripts.getInfoMapper()).thenReturn(infoMapper);

        // 创建测试实例
        MyScriptServiceImpl serviceImpl = new MyScriptServiceImpl(doubleCheckApiService, attachmentEphemeralMapper, mockBuilder,releaseMediaService,taskAuthorityService,cibProperties,afterScriptExecAuditing);

        // 执行测试方法
        int actualResult = serviceImpl.updateScriptInfoByOrgCode(orgCode, newOrgCode);

        // 验证结果
        assertEquals(0, actualResult);

        // 验证方法调用
        verify(scripts).getInfoMapper();
        verify(infoMapper).updateOrgCodeByOrgCode(orgCode, newOrgCode);
    }

    @Test
    @DisplayName("测试updateScriptInfoByOrgCode大量记录更新情况")
    void updateScriptInfoByOrgCode_massUpdate_success() {
        // 准备测试数据
        String orgCode = "commonOrgCode";
        String newOrgCode = "newCommonOrgCode";
        int expectedUpdateCount = 1000; // 模拟大量记录更新

        // 模拟大量记录更新的情况
        when(infoMapper.updateOrgCodeByOrgCode(orgCode, newOrgCode)).thenReturn(expectedUpdateCount);

        // 模拟 mockBuilder.build() 返回的 scripts 对象
        when(mockBuilder.build()).thenReturn(scripts);
        when(scripts.getInfoMapper()).thenReturn(infoMapper);

        // 创建测试实例
        MyScriptServiceImpl serviceImpl = new MyScriptServiceImpl(doubleCheckApiService, attachmentEphemeralMapper, mockBuilder,releaseMediaService,taskAuthorityService,cibProperties,afterScriptExecAuditing);

        // 执行测试方法
        int actualResult = serviceImpl.updateScriptInfoByOrgCode(orgCode, newOrgCode);

        // 验证结果
        assertEquals(expectedUpdateCount, actualResult);

        // 验证方法调用
        verify(scripts).getInfoMapper();
        verify(infoMapper).updateOrgCodeByOrgCode(orgCode, newOrgCode);
    }

}
