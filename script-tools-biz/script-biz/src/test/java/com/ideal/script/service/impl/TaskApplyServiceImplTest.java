package com.ideal.script.service.impl;

import cn.idev.excel.EasyExcel;
import cn.idev.excel.FastExcelFactory;
import cn.idev.excel.read.builder.ExcelReaderBuilder;
import cn.idev.excel.read.builder.ExcelReaderSheetBuilder;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import com.ideal.approval.dto.DoubleCheckApiDto;
import com.ideal.approval.dto.ResultApiDto;
import com.ideal.sc.util.CurrentUserUtil;
import com.ideal.script.common.constant.Constants;
import com.ideal.script.common.constant.enums.Enums;
import com.ideal.script.config.ScriptBusinessConfig;
import com.ideal.script.dto.ParameterValidationDto;
import com.ideal.script.dto.RetryScriptInstanceApiDto;
import com.ideal.script.dto.ScriptInfoDto;
import com.ideal.script.dto.ScriptTaskApplyAgentApiDto;
import com.ideal.script.dto.ScriptTaskApplyApiDto;
import com.ideal.script.dto.ScriptVersionDto;
import com.ideal.script.exception.ScriptException;
import com.ideal.script.exception.SystemException;
import com.ideal.script.mapper.CategoryMapper;
import com.ideal.script.mapper.InfoMapper;
import com.ideal.script.mapper.InfoVersionMapper;
import com.ideal.script.mapper.ScriptVersionShareMapper;
import com.ideal.script.model.bean.AuditResultBean;
import com.ideal.script.model.bean.CategoryOrgBean;
import com.ideal.script.model.bean.CategoryRoleBean;
import com.ideal.script.model.bean.OrgBean;
import com.ideal.script.model.bean.TaskApplyBean;
import com.ideal.script.model.bean.UserBean;
import com.ideal.script.model.dto.AgentInfoDto;
import com.ideal.script.model.dto.AuditRelationDto;
import com.ideal.script.model.dto.InfoVersionTextDto;
import com.ideal.script.model.dto.ParameterDto;
import com.ideal.script.model.dto.ScriptAuditDetailDto;
import com.ideal.script.model.dto.ScriptExecAuditDto;
import com.ideal.script.model.dto.TaskApplyDto;
import com.ideal.script.model.dto.TaskApplyQueryDto;
import com.ideal.script.model.dto.TaskAttachmentDto;
import com.ideal.script.model.dto.TaskDto;
import com.ideal.script.model.dto.TaskGroupsDto;
import com.ideal.script.model.dto.TaskInstanceDto;
import com.ideal.script.model.dto.TaskParamsDto;
import com.ideal.script.model.dto.interaction.agent.UserInfoDto;
import com.ideal.script.model.entity.Category;
import com.ideal.script.model.entity.Info;
import com.ideal.script.model.entity.TaskRuntime;
import com.ideal.script.remotecall.RemoteCall;
import com.ideal.script.service.*;
import com.ideal.script.service.impl.builders.MyScriptServiceScripts;
import com.ideal.script.service.impl.builders.MyScriptServiceScriptsBuilder;
import com.ideal.system.api.IAgentInfo;
import com.ideal.system.api.IRole;
import com.ideal.system.api.IUserInfo;
import com.ideal.system.common.component.model.CurrentUser;
import com.ideal.system.dto.AgentGroupRoleQueryBean;
import com.ideal.system.dto.PermissionUserInfoApiDto;
import com.ideal.system.dto.RoleApiDto;
import com.ideal.system.dto.ServicePermissionApiQueryDto;
import com.ideal.system.dto.SystemAgentInfoApiDto;
import com.ideal.system.dto.UserInfoApiDto;
import org.apache.ibatis.session.ExecutorType;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.SetOperations;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.mock.web.MockMultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.net.URLEncoder;
import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.logging.Logger;

import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.anyList;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class TaskApplyServiceImplTest {

    @Mock
    private InfoVersionMapper mockInfoVersionMapper;
    @Mock
    private ITaskService mockTaskService;
    @Mock
    private IInfoVersionService mockInfoVersionService;
    @Mock
    private IAuditRelationService mockAuditRelationService;
    @Mock
    private SqlSessionFactory mockFactory;
    @Mock
    private IAgentInfoService mockAgentInfoService;
    @Mock
    private RemoteCall mockRemoteCall;
    @Mock
    private ICategoryService mockCategoryService;
    @Mock
    private ITaskAttachmentService mockTaskAttachmentService;
    @Mock
    private ITaskParamsService mockTaskParamsService;
    @Mock
    private ITaskGroupsService mockTaskGroupsService;
    @Mock
    private ITaskExecuteService mockTaskExecuteService;
    @Mock
    private ITaskIpsService mockTaskIpsService;
    @Mock
    private  JobOperateService jobOperateService;
    @Mock
    private  ITaskScheduleService taskScheduleService;
    private TaskApplyServiceImpl taskApplyServiceImplUnderTest;
    @Mock
    private IUserInfo userInfoApi;
    @Mock
    private  IInfoVersionTextService infoVersionTextService;
    @Mock
    private IAttachmentService attachmentService;
    @Mock
    private ITaskInstanceService taskInstanceService;
    @Mock
    private ITaskApplyService taskApplyService;
    @Mock
    private IMyScriptService iMyScriptService;
    @Mock
    private AuditSource auditSource;
    @Mock
    private RedisTemplate<String, String> redisTemplate;

    @Mock
    private  ScriptBusinessConfig scriptBusinessConfig;

    @Mock
    private IUserInfo iUserInfoApi;
    @Mock
    private IAgentInfo iAgentInfo;

    @Mock
    private IInfoService infoService;

    @Mock
    private ScriptVersionShareMapper scriptVersionShareMapper;

    @Mock
    private InfoMapper infoMapper;

    @Mock
    private Logger logger;

    @Mock
    private IRole iRole;

    @Mock
    private MyScriptServiceScriptsBuilder mockBuilder;
    @Mock
    private CategoryMapper categoryMapper;
    @Mock
    private IAfterScriptExecAuditing afterScriptExecAuditing;

    // 测试checkExecuserPermission方法需要的mock对象
    private ScriptVersionDto mockScriptVersionDto;
    private ScriptInfoDto mockScriptInfoDto;
    @Mock
    private MyScriptServiceScripts myScriptServiceScripts;

    @BeforeEach
    void setUp() throws Exception {
        taskApplyServiceImplUnderTest = new TaskApplyServiceImpl(infoMapper,scriptVersionShareMapper,mockInfoVersionMapper, mockTaskService,
                mockInfoVersionService, infoVersionTextService, mockAuditRelationService, mockFactory, mockAgentInfoService, mockRemoteCall,
                mockCategoryService, mockTaskAttachmentService, mockTaskParamsService, mockTaskGroupsService,
                mockTaskExecuteService, mockTaskIpsService,jobOperateService,taskScheduleService,userInfoApi,
                attachmentService,taskInstanceService,taskApplyService,iMyScriptService, auditSource, redisTemplate,scriptBusinessConfig,
                iUserInfoApi, iAgentInfo, infoService, myScriptServiceScripts,afterScriptExecAuditing);
        when(myScriptServiceScripts.getScriptBusinessConfig()).thenReturn(scriptBusinessConfig);
        when(scriptBusinessConfig.isSelectSelfDataFlag()).thenReturn(false);

        Field roleField = MyScriptServiceScriptsBuilder.class.getDeclaredField("iRole");
        roleField.setAccessible(true);
        roleField.set(mockBuilder, iRole);

        Field roleField1 = MyScriptServiceScripts.class.getDeclaredField("iRole");
        roleField1.setAccessible(true);
        roleField1.set(myScriptServiceScripts, iRole);

        Field categoryMapperrField = MyScriptServiceScriptsBuilder.class.getDeclaredField("categoryMapper");
        categoryMapperrField.setAccessible(true);
        categoryMapperrField.set(mockBuilder, categoryMapper);
    }

    @Test
    void testSelectTaskApplyList() {
        // Setup
        final TaskApplyQueryDto taskApplyQueryDto = new TaskApplyQueryDto();
        taskApplyQueryDto.setCategoryId(0L);
        taskApplyQueryDto.setScriptNameZh("scriptNameZh");
        taskApplyQueryDto.setScriptName("scriptName");
        taskApplyQueryDto.setScriptType("scriptType");
        taskApplyQueryDto.setScriptCategoryName("scriptCategoryName");
        taskApplyQueryDto.setDutyApply(false);

        final CurrentUser user = new CurrentUser();
        user.setId(0L);
        user.setLoginName("applyUser");
        user.setOrgCode("testOrgCode");
        user.setFullName("Test User");
        user.setSupervisor(false);

        when(myScriptServiceScripts.getiRole()).thenReturn(iRole);



        List<RoleApiDto> roleApiDtoList = new ArrayList<>();
        RoleApiDto roleApiDto = new RoleApiDto();
        roleApiDto.setId(1L);
        roleApiDtoList.add(roleApiDto);
        when(iRole.selectRoleListByLoginName(any())).thenReturn(roleApiDtoList);
        List<Long> idList = new ArrayList<>();
        idList.add(1L);
        when(categoryMapper.getCategoryIdsByRoleIds(any())).thenReturn(idList);

        // Mock categoryService.getAllCategoryIds
        List<Long> categoryIdList = Collections.singletonList(0L);
        when(mockCategoryService.getAllCategoryIds(0L)).thenReturn(categoryIdList);

        // Mock categoryService.getCategoryByOrgCode
        when(mockCategoryService.getCategoryByOrgCode("testOrgCode")).thenReturn(categoryIdList);

        // Mock categoryService.buildCategoryPath
        when(mockCategoryService.buildCategoryPath(any(Category.class))).thenReturn("/test/path");

        // Mock categoryService.handleCategoryPath
        when(mockCategoryService.handleCategoryPath("/test/path")).thenReturn("/test/path");

        // Mock scriptBusinessConfig.getScriptTaskApplyCycle
        when(scriptBusinessConfig.getScriptTaskApplyCycle()).thenReturn(15);

        // 创建Page对象并设置必要的分页信息
        Page<TaskApplyBean> page = new Page<>(0, 10);
        page.setTotal(1);

        // 构建TaskApplyBean并添加到Page中
        final TaskApplyBean taskApplyBean = new TaskApplyBean();
        taskApplyBean.setUserIdList(Collections.singletonList(0L));
        taskApplyBean.setCategoryIdList(categoryIdList);
        taskApplyBean.setScriptInfoId(0L);
        taskApplyBean.setCategoryId(0L);
        taskApplyBean.setScriptCategoryName("scriptCategoryName");
        taskApplyBean.setSysOrgCode(user.getOrgCode());
        taskApplyBean.setStartUser(user.getFullName());
        taskApplyBean.setUserId(user.getId());
        taskApplyBean.setDepartment(user.getOrgCode());
        taskApplyBean.setOrgCategoryPath(Collections.singletonList("/test/path%"));

        // 设置当前时间相关属性
        LocalDateTime currentDateTime = LocalDateTime.now();
        LocalDateTime preDateTime = currentDateTime.minusDays(15);
        taskApplyBean.setTaskCreatePreTime(Timestamp.valueOf(preDateTime));

        // 添加到Page中
        page.add(taskApplyBean);

        // Mock InfoVersionMapper.selectTaskApplyList
        when(mockInfoVersionMapper.selectTaskApplyList(any(TaskApplyBean.class))).thenReturn(page);

        // Mock categoryService.getCategoryFullPath
        when(mockCategoryService.getCategoryFullPath(0L)).thenReturn("scriptCategoryName");

        // Mock Category相关的组织信息
        CategoryOrgBean categoryOrgBean = new CategoryOrgBean();
        List<OrgBean> orgList = new ArrayList<>();
        OrgBean orgBean = new OrgBean();
        orgBean.setOrgId(1L);
        orgList.add(orgBean);
        categoryOrgBean.setOrgList(orgList);
        when(mockCategoryService.getCategoryOrgRelations(0L)).thenReturn(categoryOrgBean);

        // Run the test
        final PageInfo<TaskApplyDto> result = taskApplyServiceImplUnderTest.selectTaskApplyList(
            taskApplyQueryDto,
            0,  // pageNum
            10, // pageSize
            user
        );

        // Verify
        assertNotNull(result);
        assertEquals(0, result.getPageNum());
        assertEquals(10, result.getPageSize());
        assertEquals(1, result.getTotal());

        // Verify the results
        verify(mockCategoryService).setCategoryPermission(any(TaskApplyBean.class), any(CurrentUser.class));
        verify(scriptBusinessConfig).getScriptTaskApplyCycle();
        verify(mockInfoVersionMapper).selectTaskApplyList(any(TaskApplyBean.class));
        verify(mockCategoryService).getCategoryFullPath(0L);
    }

    @Test
    void testSelectTaskApplyListByRole() {
        // Setup
        final TaskApplyQueryDto taskApplyQueryDto = new TaskApplyQueryDto();
        taskApplyQueryDto.setCategoryId(0L);
        taskApplyQueryDto.setScriptNameZh("scriptNameZh");
        taskApplyQueryDto.setScriptName("scriptName");
        taskApplyQueryDto.setScriptType("scriptType");
        taskApplyQueryDto.setScriptCategoryName("scriptCategoryName");
        taskApplyQueryDto.setDutyApply(false);

        final CurrentUser user = new CurrentUser();
        user.setId(0L);
        user.setLoginName("applyUser");
        user.setOrgCode("testOrgCode");
        user.setFullName("Test User");
        user.setSupervisor(false);

        when(myScriptServiceScripts.getiRole()).thenReturn(iRole);



        List<RoleApiDto> roleApiDtoList = new ArrayList<>();
        RoleApiDto roleApiDto = new RoleApiDto();
        roleApiDto.setId(1L);
        roleApiDtoList.add(roleApiDto);
        when(iRole.selectRoleListByLoginName(any())).thenReturn(roleApiDtoList);
        List<Long> idList = new ArrayList<>();
        idList.add(1L);

        when(myScriptServiceScripts.getCategoryMapper()).thenReturn(categoryMapper);

        when(categoryMapper.getCategoryIdsByRoleIds(any())).thenReturn(idList);
        when(iMyScriptService.getRolePermission()).thenReturn(true);
        // Mock categoryService.getAllCategoryIds
        List<Long> categoryIdList = Collections.singletonList(0L);
        when(mockCategoryService.getAllCategoryIds(0L)).thenReturn(categoryIdList);

        // Mock categoryService.getCategoryByOrgCode
        when(mockCategoryService.getCategoryByOrgCode("testOrgCode")).thenReturn(categoryIdList);

        // Mock categoryService.buildCategoryPath
        when(mockCategoryService.buildCategoryPath(any(Category.class))).thenReturn("/test/path");

        // Mock categoryService.handleCategoryPath
        when(mockCategoryService.handleCategoryPath("/test/path")).thenReturn("/test/path");

        // Mock scriptBusinessConfig.getScriptTaskApplyCycle
        when(scriptBusinessConfig.getScriptTaskApplyCycle()).thenReturn(15);

        // 创建Page对象并设置必要的分页信息
        Page<TaskApplyBean> page = new Page<>(0, 10);
        page.setTotal(1);

        // 构建TaskApplyBean并添加到Page中
        final TaskApplyBean taskApplyBean = new TaskApplyBean();
        taskApplyBean.setUserIdList(Collections.singletonList(0L));
        taskApplyBean.setCategoryIdList(categoryIdList);
        taskApplyBean.setScriptInfoId(0L);
        taskApplyBean.setCategoryId(0L);
        taskApplyBean.setScriptCategoryName("scriptCategoryName");
        taskApplyBean.setSysOrgCode(user.getOrgCode());
        taskApplyBean.setStartUser(user.getFullName());
        taskApplyBean.setUserId(user.getId());
        taskApplyBean.setDepartment(user.getOrgCode());
        taskApplyBean.setOrgCategoryPath(Collections.singletonList("/test/path%"));

        // 设置当前时间相关属性
        LocalDateTime currentDateTime = LocalDateTime.now();
        LocalDateTime preDateTime = currentDateTime.minusDays(15);
        taskApplyBean.setTaskCreatePreTime(Timestamp.valueOf(preDateTime));

        // 添加到Page中
        page.add(taskApplyBean);

        // Mock InfoVersionMapper.selectTaskApplyList
        when(mockInfoVersionMapper.selectTaskApplyList(any(TaskApplyBean.class))).thenReturn(page);

        // Mock categoryService.getCategoryFullPath
        when(mockCategoryService.getCategoryFullPath(0L)).thenReturn("scriptCategoryName");

        // Mock Category相关的组织信息
        CategoryOrgBean categoryOrgBean = new CategoryOrgBean();
        List<OrgBean> orgList = new ArrayList<>();
        OrgBean orgBean = new OrgBean();
        orgBean.setOrgId(1L);
        orgList.add(orgBean);
        categoryOrgBean.setOrgList(orgList);
        when(mockCategoryService.getCategoryOrgRelations(0L)).thenReturn(categoryOrgBean);

        // Run the test
        final PageInfo<TaskApplyDto> result = taskApplyServiceImplUnderTest.selectTaskApplyList(
                taskApplyQueryDto,
                0,  // pageNum
                10, // pageSize
                user
        );

        // Verify
        assertNotNull(result);
        assertEquals(0, result.getPageNum());
        assertEquals(10, result.getPageSize());
        assertEquals(1, result.getTotal());

        // Verify the results
        verify(mockCategoryService).setCategoryPermission(any(TaskApplyBean.class), any(CurrentUser.class));
        verify(scriptBusinessConfig).getScriptTaskApplyCycle();
        verify(mockInfoVersionMapper).selectTaskApplyList(any(TaskApplyBean.class));
        verify(mockCategoryService).getCategoryFullPath(0L);
    }

    @Test
    void testSelectTaskApplyListWithDuty() {
        // Setup
        final TaskApplyQueryDto taskApplyQueryDto = new TaskApplyQueryDto();
        taskApplyQueryDto.setCategoryId(0L);
        taskApplyQueryDto.setScriptNameZh("scriptNameZh");
        taskApplyQueryDto.setScriptName("scriptName");
        taskApplyQueryDto.setScriptType("scriptType");
        taskApplyQueryDto.setScriptCategoryName("scriptCategoryName");
        taskApplyQueryDto.setDutyApply(true);

        final CurrentUser user = new CurrentUser();
        user.setId(0L);
        user.setLoginName("applyUser");
        user.setOrgCode("testOrgCode");
        user.setFullName("Test User");
        user.setSupervisor(false);

        // Mock categoryService.getAllCategoryIds
        List<Long> categoryIdList = Collections.singletonList(0L);
        when(mockCategoryService.getAllCategoryIds(0L)).thenReturn(categoryIdList);

        // Mock categoryService.getCategoryByOrgCode
        when(mockCategoryService.getCategoryByOrgCode("testOrgCode")).thenReturn(categoryIdList);

        // Mock categoryService.buildCategoryPath
        when(mockCategoryService.buildCategoryPath(any(Category.class))).thenReturn("/test/path");

        // Mock categoryService.handleCategoryPath
        when(mockCategoryService.handleCategoryPath("/test/path")).thenReturn("/test/path");

        // Mock scriptBusinessConfig.getScriptTaskApplyCycle
        when(scriptBusinessConfig.getScriptTaskApplyCycle()).thenReturn(15);

        // 创建Page对象并设置必要的分页信息
        Page<TaskApplyBean> page = new Page<>(0, 10);
        page.setTotal(1);

        // 构建TaskApplyBean并添加到Page中
        final TaskApplyBean taskApplyBean = new TaskApplyBean();
        taskApplyBean.setUserIdList(Collections.singletonList(0L));
        taskApplyBean.setCategoryIdList(categoryIdList);
        taskApplyBean.setScriptInfoId(0L);
        taskApplyBean.setCategoryId(0L);
        taskApplyBean.setScriptCategoryName("scriptCategoryName");
        taskApplyBean.setSysOrgCode(user.getOrgCode());
        taskApplyBean.setStartUser(user.getFullName());
        taskApplyBean.setUserId(user.getId());
        taskApplyBean.setDepartment(user.getOrgCode());
        taskApplyBean.setOrgCategoryPath(Collections.singletonList("/test/path%"));

        // 设置当前时间相关属性
        LocalDateTime currentDateTime = LocalDateTime.now();
        LocalDateTime preDateTime = currentDateTime.minusDays(15);
        taskApplyBean.setTaskCreatePreTime(Timestamp.valueOf(preDateTime));

        // 添加到Page中
        page.add(taskApplyBean);

        // Mock InfoVersionMapper.selectTaskApplyList
        when(mockInfoVersionMapper.selectTaskApplyList(any(TaskApplyBean.class))).thenReturn(page);

        // Mock categoryService.getCategoryFullPath
        when(mockCategoryService.getCategoryFullPath(0L)).thenReturn("scriptCategoryName");

        // Mock Category相关的组织信息
        CategoryOrgBean categoryOrgBean = new CategoryOrgBean();
        List<OrgBean> orgList = new ArrayList<>();
        OrgBean orgBean = new OrgBean();
        orgBean.setOrgId(1L);
        orgList.add(orgBean);
        categoryOrgBean.setOrgList(orgList);
        when(mockCategoryService.getCategoryOrgRelations(0L)).thenReturn(categoryOrgBean);

        // Run the test
        final PageInfo<TaskApplyDto> result = taskApplyServiceImplUnderTest.selectTaskApplyList(
                taskApplyQueryDto,
                0,  // pageNum
                10, // pageSize
                user
        );

        // Verify
        assertNotNull(result);
        assertEquals(0, result.getPageNum());
        assertEquals(10, result.getPageSize());
        assertEquals(1, result.getTotal());

        // Verify the results
        verify(mockCategoryService).setCategoryPermission(any(TaskApplyBean.class), any(CurrentUser.class));
        verify(scriptBusinessConfig).getScriptTaskApplyCycle();
        verify(mockInfoVersionMapper).selectTaskApplyList(any(TaskApplyBean.class));
        verify(mockCategoryService).getCategoryFullPath(0L);
    }

    @Test
    void testSelectTaskApplyListForAdmin() {
        // Setup
        final TaskApplyQueryDto taskApplyQueryDto = new TaskApplyQueryDto();
        taskApplyQueryDto.setCategoryId(0L);
        taskApplyQueryDto.setScriptNameZh("scriptNameZh");
        taskApplyQueryDto.setScriptName("scriptName");
        taskApplyQueryDto.setScriptType("scriptType");
        taskApplyQueryDto.setScriptCategoryName("scriptCategoryName");
        taskApplyQueryDto.setDutyApply(false);

        final CurrentUser user = new CurrentUser();
        user.setId(0L);
        user.setLoginName("applyUser");
        user.setOrgCode("testOrgCode");
        user.setFullName("Test User");
        user.setSupervisor(true);

        // Mock categoryService.getAllCategoryIds
        List<Long> categoryIdList = Collections.singletonList(0L);
        when(mockCategoryService.getAllCategoryIds(0L)).thenReturn(categoryIdList);

        // Mock categoryService.getCategoryByOrgCode
        when(mockCategoryService.getCategoryByOrgCode("testOrgCode")).thenReturn(categoryIdList);

        // Mock categoryService.buildCategoryPath
        when(mockCategoryService.buildCategoryPath(any(Category.class))).thenReturn("/test/path");

        // Mock categoryService.handleCategoryPath
        when(mockCategoryService.handleCategoryPath("/test/path")).thenReturn("/test/path");

        // Mock scriptBusinessConfig.getScriptTaskApplyCycle
        when(scriptBusinessConfig.getScriptTaskApplyCycle()).thenReturn(15);

        // 创建Page对象并设置必要的分页信息
        Page<TaskApplyBean> page = new Page<>(0, 10);
        page.setTotal(1);

        // 构建TaskApplyBean并添加到Page中
        final TaskApplyBean taskApplyBean = new TaskApplyBean();
        taskApplyBean.setUserIdList(Collections.singletonList(0L));
        taskApplyBean.setCategoryIdList(categoryIdList);
        taskApplyBean.setScriptInfoId(0L);
        taskApplyBean.setCategoryId(0L);
        taskApplyBean.setScriptCategoryName("scriptCategoryName");
        taskApplyBean.setSysOrgCode(user.getOrgCode());
        taskApplyBean.setStartUser(user.getFullName());
        taskApplyBean.setUserId(user.getId());
        taskApplyBean.setDepartment(user.getOrgCode());
        taskApplyBean.setOrgCategoryPath(Collections.singletonList("/test/path%"));

        // 设置当前时间相关属性
        LocalDateTime currentDateTime = LocalDateTime.now();
        LocalDateTime preDateTime = currentDateTime.minusDays(15);
        taskApplyBean.setTaskCreatePreTime(Timestamp.valueOf(preDateTime));

        // 添加到Page中
        page.add(taskApplyBean);

        // Mock InfoVersionMapper.selectTaskApplyList
        when(mockInfoVersionMapper.selectTaskApplyList(any(TaskApplyBean.class))).thenReturn(page);

        // Mock categoryService.getCategoryFullPath
        when(mockCategoryService.getCategoryFullPath(0L)).thenReturn("scriptCategoryName");

        // Mock Category相关的组织信息
        CategoryOrgBean categoryOrgBean = new CategoryOrgBean();
        List<OrgBean> orgList = new ArrayList<>();
        OrgBean orgBean = new OrgBean();
        orgBean.setOrgId(1L);
        orgList.add(orgBean);
        categoryOrgBean.setOrgList(orgList);
        when(mockCategoryService.getCategoryOrgRelations(0L)).thenReturn(categoryOrgBean);

        // Run the test
        final PageInfo<TaskApplyDto> result = taskApplyServiceImplUnderTest.selectTaskApplyList(
                taskApplyQueryDto,
                0,  // pageNum
                10, // pageSize
                user
        );

        // Verify
        assertNotNull(result);
        assertEquals(0, result.getPageNum());
        assertEquals(10, result.getPageSize());
        assertEquals(1, result.getTotal());

        // Verify the results
        verify(mockCategoryService).setCategoryPermission(any(TaskApplyBean.class), any(CurrentUser.class));
        verify(mockCategoryService).getCategoryFullPath(0L);
    }

    @Test
    void testSelectTaskApplyList_noCategoryId() {
        // Setup
        final TaskApplyQueryDto taskApplyQueryDto = new TaskApplyQueryDto();
        taskApplyQueryDto.setCategoryId(0L);
        taskApplyQueryDto.setScriptNameZh("scriptNameZh");
        taskApplyQueryDto.setScriptName("scriptName");
        taskApplyQueryDto.setScriptType("scriptType");
        taskApplyQueryDto.setScriptCategoryName("scriptCategoryName");
        taskApplyQueryDto.setDutyApply(false);

        final CurrentUser user = new CurrentUser();
        user.setId(0L);
        user.setLoginName("applyUser");
        user.setOrgCode("testOrgCode");
        user.setFullName("Test User");
        user.setSupervisor(false);

        // Mock categoryService.getAllCategoryIds
        List<Long> categoryIdList = Collections.singletonList(0L);
        when(mockCategoryService.getAllCategoryIds(anyLong())).thenReturn(categoryIdList);

        // Mock categoryService.getCategoryByOrgCode
        when(mockCategoryService.getCategoryByOrgCode(anyString())).thenReturn(categoryIdList);

        // Mock categoryService.buildCategoryPath
        when(mockCategoryService.buildCategoryPath(any(Category.class))).thenReturn("/test/path");

        // Mock categoryService.handleCategoryPath
        when(mockCategoryService.handleCategoryPath(anyString())).thenReturn("/test/path");

        // Mock scriptBusinessConfig.getScriptTaskApplyCycle
        when(scriptBusinessConfig.getScriptTaskApplyCycle()).thenReturn(15);

        // 创建Page对象并设置必要的分页信息
        Page<TaskApplyBean> page = new Page<>();
        page.setPageNum(0);
        page.setPageSize(10);
        page.setTotal(1);

        // 构建TaskApplyBean并添加到Page中
        final TaskApplyBean taskApplyBean = new TaskApplyBean();
        taskApplyBean.setUserIdList(Collections.singletonList(0L));
        taskApplyBean.setCategoryIdList(categoryIdList);
        taskApplyBean.setScriptInfoId(0L);
        taskApplyBean.setCategoryId(0L);
        taskApplyBean.setScriptCategoryName("scriptCategoryName");
        taskApplyBean.setSysOrgCode(user.getOrgCode());
        taskApplyBean.setStartUser(user.getFullName());
        taskApplyBean.setUserId(user.getId());
        taskApplyBean.setDepartment(user.getOrgCode());

        // 添加到Page中
        page.add(taskApplyBean);

        // Mock InfoVersionMapper.selectTaskApplyList，返回Page对象
        when(mockInfoVersionMapper.selectTaskApplyList(any(TaskApplyBean.class))).thenReturn(page);

        // Mock categoryService.getCategoryFullPath
        when(mockCategoryService.getCategoryFullPath(anyLong())).thenReturn("scriptCategoryName");

        // Mock Category相关的组织信息
        CategoryOrgBean categoryOrgBean = new CategoryOrgBean();
        List<OrgBean> orgList = new ArrayList<>();
        OrgBean orgBean = new OrgBean();
        orgBean.setOrgId(1L);
        orgList.add(orgBean);
        categoryOrgBean.setOrgList(orgList);
        when(mockCategoryService.getCategoryOrgRelations(anyLong())).thenReturn(categoryOrgBean);

        // Run the test
        final PageInfo<TaskApplyDto> result = taskApplyServiceImplUnderTest.selectTaskApplyList(
            taskApplyQueryDto,
            0,  // pageNum
            10, // pageSize
            user
        );

        // Verify
        assertNotNull(result);
        assertEquals(0, result.getPageNum());
        assertEquals(10, result.getPageSize());
        assertEquals(1, result.getTotal());

        // 验证所有mock方法都被调用
        verify(mockCategoryService).setCategoryPermission(any(TaskApplyBean.class), any(CurrentUser.class));
        verify(scriptBusinessConfig).getScriptTaskApplyCycle();
        verify(mockInfoVersionMapper).selectTaskApplyList(any(TaskApplyBean.class));
        verify(mockCategoryService).getCategoryFullPath(anyLong());
    }


    // 测试无效cron表达式
    @Test
    void testScriptExecAuditing_InvalidCron() {
        // Setup
        ScriptExecAuditDto scriptExecAuditDto = buildTestScriptExecAuditDto();
        scriptExecAuditDto.getTaskInfo().setTaskScheduler(Enums.TaskScheduler.PERIODIC.getValue());
        scriptExecAuditDto.getTaskInfo().setTaskCron("invalid cron");

        // Run & Verify
        assertThrows(ScriptException.class, () ->
                taskApplyServiceImplUnderTest.scriptExecAuditing(
                        scriptExecAuditDto,
                        new CurrentUser(),
                        auditSource
                )
        );
    }

    // 测试参数校验失败
    @Test
    void testScriptExecAuditing_ParamValidationFailed() throws ScriptException {
        // Setup
        ScriptExecAuditDto scriptExecAuditDto = buildTestScriptExecAuditDto();
        List<ParameterDto> dataList = new ArrayList<>();
        dataList.add(new ParameterDto());
        scriptExecAuditDto.setParams(dataList);

        when(mockTaskParamsService.validateParameterList(any())).thenReturn("参数错误");

        // Run & Verify
        assertThrows(ScriptException.class, () ->
                taskApplyServiceImplUnderTest.scriptExecAuditing(
                        scriptExecAuditDto,
                        new CurrentUser(),
                        auditSource
                )
        );
    }

    // 测试双人复核失败
    @Test
    void testScriptExecAuditing_DoubleCheckFailed() throws Exception {
        // Setup
        ScriptExecAuditDto scriptExecAuditDto = buildTestScriptExecAuditDto();

        when(auditSource.isInWhiteList(any())).thenReturn(false);
        when(mockRemoteCall.applyForDoubleCheck(any(), any()))
                .thenReturn(new ResultApiDto());

        // Run & Verify
        assertThrows(ScriptException.class, () ->
                taskApplyServiceImplUnderTest.scriptExecAuditing(
                        scriptExecAuditDto,
                        new CurrentUser(),
                        auditSource
                )
        );
    }

    // 测试资源组为空的情况
    @Test
    void testScriptExecAuditing_EmptyResourceGroup() {
        // Setup
        ScriptExecAuditDto scriptExecAuditDto = buildTestScriptExecAuditDto();
        scriptExecAuditDto.setResGroupFlag(true);
        scriptExecAuditDto.setChosedResGroups(Collections.emptyList());

        // Run & Verify
        assertThrows(ScriptException.class, () ->
                taskApplyServiceImplUnderTest.scriptExecAuditing(
                        scriptExecAuditDto,
                        new CurrentUser(),
                        auditSource
                )
        );
    }

    // 辅助方法：构建测试数据
    private ScriptExecAuditDto buildTestScriptExecAuditDto() {
        ScriptExecAuditDto dto = new ScriptExecAuditDto();
        dto.setScriptInfoVersionId(1L);

        TaskDto taskInfo = new TaskDto();
        taskInfo.setId(1L);
        taskInfo.setSrcScriptUuid("test-uuid");
        taskInfo.setTaskName("test-task");
        dto.setTaskInfo(taskInfo);

        AgentInfoDto agent = new AgentInfoDto();
        agent.setAgentIp("127.0.0.1");
        agent.setAgentPort(8080);
        List<AgentInfoDto> listAgent = new ArrayList<>();
        listAgent.add(agent);
        dto.setChosedAgentUsers(listAgent);

        return dto;
    }
    @Test
    public void testScriptExecAuditingForWeb() throws ScriptException {
        // 准备测试数据
        ScriptExecAuditDto scriptExecAuditDto = new ScriptExecAuditDto();
        CurrentUser user = new CurrentUser();
        AuditSource auditSource = mock(AuditSource.class);
       // 模拟 ITaskApplyService 的行为
        Long expectedTaskId = 123L;
        doReturn(expectedTaskId).when(taskApplyService).scriptExecAuditing(scriptExecAuditDto, user, auditSource);
        // 调用被测试的方法
        Map<String, Long> result = taskApplyServiceImplUnderTest.scriptExecAuditingForWeb(scriptExecAuditDto, user, auditSource);

        // 验证结果
        assertNotNull(result);
        assertEquals(expectedTaskId, result.get("taskId"));
        assertNull(result.get("auditId")); // ThreadLocal 在测试中可能无法直接验证

        // 验证 mockTaskApplyService 是否被正确调用
        verify(taskApplyService, times(1)).scriptExecAuditing(scriptExecAuditDto, user, auditSource);

    }

    @Test
    void testScriptExecAuditing() throws Exception {
        // Setup
        final ScriptExecAuditDto scriptExecAuditDto = new ScriptExecAuditDto();
        scriptExecAuditDto.setScriptInfoVersionId(0L);
        final TaskDto taskInfo = new TaskDto();
        taskInfo.setScriptTaskSource(0);
        taskInfo.setStartType(0);
        taskInfo.setReadyToExecute(0);
        taskInfo.setId(0L);
        taskInfo.setSrcScriptUuid("srcScriptUuid");
        taskInfo.setTaskName("taskName");
        taskInfo.setTaskScheduler(0);
        taskInfo.setTaskCron("taskCron");
        taskInfo.setPublishDesc("publishDesc");
        taskInfo.setTimeout(0L);
        taskInfo.setType(0);
        taskInfo.setTaskCron("* 5 * * * ? ");
        taskInfo.setTaskScheduler(1);
        scriptExecAuditDto.setTaskInfo(taskInfo);
        scriptExecAuditDto.setAuditUser("auditUser");
        scriptExecAuditDto.setAuditUserId(0L);
        final AgentInfoDto agentInfoDto = new AgentInfoDto();
        agentInfoDto.setAgentName("服务器");
        agentInfoDto.setAgentIp("127.0.0.1");
        scriptExecAuditDto.setChosedAgentUsers(Collections.singletonList(agentInfoDto));
        final TaskGroupsDto taskGroupsDto = new TaskGroupsDto();
        taskGroupsDto.setId(1L);
        taskGroupsDto.setCpname("资源组");
        scriptExecAuditDto.setChosedResGroups(Collections.singletonList(taskGroupsDto));
        scriptExecAuditDto.setResGroupFlag(true);


        final CurrentUser user = new CurrentUser();
        user.setId(0L);
        user.setLoginName("applyUser");

        // Configure IInfoVersionService.selectInfoVersionById(...).
        final ScriptVersionDto infoVersionDto = new ScriptVersionDto();
        infoVersionDto.setId(0L);
        infoVersionDto.setInfoUniqueUuid("infoUniqueUuid");
        infoVersionDto.setSrcScriptUuid("srcScriptUuid");
        infoVersionDto.setVersion("version");
        infoVersionDto.setEditState(0);
        when(mockInfoVersionService.selectInfoVersionById(0L)).thenReturn(infoVersionDto);

        // Configure SqlSessionFactory.openSession(...).
        final SqlSession mockSqlSession = mock(SqlSession.class);
        when(mockFactory.openSession(ExecutorType.BATCH, false)).thenReturn(mockSqlSession);

        // Configure ITaskGroupsService.retrieveUniqueAgentInfoList(...).
        final AgentInfoDto agentInfoDto1 = new AgentInfoDto();
        agentInfoDto1.setId(0L);
        agentInfoDto1.setSysmAgentInfoId(0L);
        agentInfoDto1.setAgentIp("agentIp");
        agentInfoDto1.setAgentName("agentName");
        agentInfoDto1.setAgentPort(0);
        final List<AgentInfoDto> agentInfoDtos = Collections.singletonList(agentInfoDto1);
        final TaskGroupsDto taskGroupsDto1 = new TaskGroupsDto();
        taskGroupsDto1.setId(0L);
        taskGroupsDto1.setScriptTaskId(0L);
        taskGroupsDto1.setSysmComputerGroupId(0L);
        taskGroupsDto1.setCpname("cpname");
        taskGroupsDto1.setCreateTime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        final List<TaskGroupsDto> taskGroupsDtoList = Collections.singletonList(taskGroupsDto1);
        when(mockTaskGroupsService.retrieveUniqueAgentInfoList(any(List.class))).thenReturn(agentInfoDtos);

        // Configure RemoteCall.applyForDoubleCheck(...).
        final ResultApiDto resultApiDto = new ResultApiDto();
        resultApiDto.setSuccess(false);
        resultApiDto.setMessage("message");
        resultApiDto.setTaskId(0L);
        when(mockRemoteCall.applyForDoubleCheck(any(String.class), any(DoubleCheckApiDto.class))).thenReturn(resultApiDto);

        // Run the test
        assertThatThrownBy(() -> taskApplyServiceImplUnderTest.scriptExecAuditing(scriptExecAuditDto, user,auditSource))
                .isInstanceOf(ScriptException.class);
        // Verify the results
        verify(mockSqlSession).close();
        verify(mockTaskAttachmentService).saveTaskAttachement(any(ScriptExecAuditDto.class), any(TaskDto.class),
                any(SqlSession.class));
        verify(mockTaskGroupsService).saveTaskGroups(any(ScriptExecAuditDto.class), any(TaskDto.class),
                any(SqlSession.class));
        verify(mockTaskIpsService).saveTaskIps(any(ScriptExecAuditDto.class), any(TaskDto.class),
                any(SqlSession.class));
        verify(mockTaskParamsService).saveTaskParams(any(ScriptExecAuditDto.class), any(TaskDto.class),
                any(SqlSession.class));
    }


    @Test
    void testScriptExecAuditing_noMessageException() throws Exception {
        // Setup
        final ScriptExecAuditDto scriptExecAuditDto = new ScriptExecAuditDto();
        scriptExecAuditDto.setScriptInfoVersionId(0L);
        final TaskDto taskInfo = new TaskDto();
        taskInfo.setScriptTaskSource(0);
        taskInfo.setStartType(0);
        taskInfo.setReadyToExecute(0);
        taskInfo.setId(0L);
        taskInfo.setSrcScriptUuid("srcScriptUuid");
        taskInfo.setTaskName("taskName");
        taskInfo.setTaskScheduler(0);
        taskInfo.setTaskCron("taskCron");
        taskInfo.setPublishDesc("publishDesc");
        taskInfo.setTimeout(0L);
        taskInfo.setType(0);
        taskInfo.setTaskCron("* 5 * * * ? ");
        taskInfo.setTaskScheduler(1);
        scriptExecAuditDto.setTaskInfo(taskInfo);
        scriptExecAuditDto.setAuditUser("auditUser");
        scriptExecAuditDto.setAuditUserId(0L);
        final AgentInfoDto agentInfoDto = new AgentInfoDto();
        agentInfoDto.setAgentName("服务器");
        agentInfoDto.setAgentIp("127.0.0.1");
        scriptExecAuditDto.setChosedAgentUsers(Collections.singletonList(agentInfoDto));
        final TaskGroupsDto taskGroupsDto = new TaskGroupsDto();
        taskGroupsDto.setId(1L);
        taskGroupsDto.setCpname("资源组");
        scriptExecAuditDto.setChosedResGroups(Collections.singletonList(taskGroupsDto));
        scriptExecAuditDto.setResGroupFlag(true);
        ParameterDto parameterDto = new ParameterDto();
        parameterDto.setId(1L);
        List<ParameterDto> parameterDtoList = new ArrayList<>();
        parameterDtoList.add(parameterDto);
        scriptExecAuditDto.setParams(parameterDtoList);


        final CurrentUser user = new CurrentUser();
        user.setId(0L);
        user.setLoginName("applyUser");

        // Configure IInfoVersionService.selectInfoVersionById(...).
        final ScriptVersionDto infoVersionDto = new ScriptVersionDto();
        infoVersionDto.setId(0L);
        infoVersionDto.setInfoUniqueUuid("infoUniqueUuid");
        infoVersionDto.setSrcScriptUuid("srcScriptUuid");
        infoVersionDto.setVersion("version");
        infoVersionDto.setEditState(0);
        when(mockInfoVersionService.selectInfoVersionById(0L)).thenReturn(infoVersionDto);

        // Configure SqlSessionFactory.openSession(...).
        final SqlSession mockSqlSession = mock(SqlSession.class);
        when(mockFactory.openSession(ExecutorType.BATCH, false)).thenReturn(mockSqlSession);

        // Configure ITaskGroupsService.retrieveUniqueAgentInfoList(...).
        final AgentInfoDto agentInfoDto1 = new AgentInfoDto();
        agentInfoDto1.setId(0L);
        agentInfoDto1.setSysmAgentInfoId(0L);
        agentInfoDto1.setAgentIp("agentIp");
        agentInfoDto1.setAgentName("agentName");
        agentInfoDto1.setAgentPort(0);
        final List<AgentInfoDto> agentInfoDtos = Collections.singletonList(agentInfoDto1);
        final TaskGroupsDto taskGroupsDto1 = new TaskGroupsDto();
        taskGroupsDto1.setId(0L);
        taskGroupsDto1.setScriptTaskId(0L);
        taskGroupsDto1.setSysmComputerGroupId(0L);
        taskGroupsDto1.setCpname("cpname");
        taskGroupsDto1.setCreateTime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        final List<TaskGroupsDto> taskGroupsDtoList = Collections.singletonList(taskGroupsDto1);
        when(mockTaskGroupsService.retrieveUniqueAgentInfoList(any(List.class))).thenReturn(agentInfoDtos);


        when(mockTaskParamsService.validateParameterList(any(List.class))).thenReturn("这个不为空");
        // Run the test
        assertThrows(ScriptException.class, () ->{
            taskApplyServiceImplUnderTest.scriptExecAuditing(scriptExecAuditDto, user,auditSource);
        });

        // Verify the results
        verify(mockSqlSession).close();
        verify(mockTaskAttachmentService).saveTaskAttachement(any(ScriptExecAuditDto.class), any(TaskDto.class),
                any(SqlSession.class));
        verify(mockTaskGroupsService).saveTaskGroups(any(ScriptExecAuditDto.class), any(TaskDto.class),
                any(SqlSession.class));
        verify(mockTaskIpsService).saveTaskIps(any(ScriptExecAuditDto.class), any(TaskDto.class),
                any(SqlSession.class));

    }

    @Test
    void testScriptExecAuditing_isWhite() throws Exception {
        // Setup
        final ScriptExecAuditDto scriptExecAuditDto = new ScriptExecAuditDto();
        scriptExecAuditDto.setScriptInfoVersionId(0L);
        final TaskDto taskInfo = new TaskDto();
        taskInfo.setScriptTaskSource(0);
        taskInfo.setStartType(0);
        taskInfo.setReadyToExecute(0);
        taskInfo.setId(0L);
        taskInfo.setSrcScriptUuid("srcScriptUuid");
        taskInfo.setTaskName("taskName");
        taskInfo.setTaskScheduler(0);
        taskInfo.setTaskCron("taskCron");
        taskInfo.setPublishDesc("publishDesc");
        taskInfo.setTimeout(0L);
        taskInfo.setType(0);
        scriptExecAuditDto.setTaskInfo(taskInfo);
        scriptExecAuditDto.setAuditUser("auditUser");
        scriptExecAuditDto.setAuditUserId(0L);
        final AgentInfoDto agentInfoDto = new AgentInfoDto();
        agentInfoDto.setAgentName("服务器");
        agentInfoDto.setAgentIp("127.0.0.1");
        scriptExecAuditDto.setChosedAgentUsers(Collections.singletonList(agentInfoDto));
        final TaskGroupsDto taskGroupsDto = new TaskGroupsDto();
        taskGroupsDto.setId(1L);
        taskGroupsDto.setCpname("资源组");
        scriptExecAuditDto.setChosedResGroups(Collections.singletonList(taskGroupsDto));
        scriptExecAuditDto.setResGroupFlag(true);


        final CurrentUser user = new CurrentUser();
        user.setId(0L);
        user.setLoginName("applyUser");

        // Configure IInfoVersionService.selectInfoVersionById(...).
        final ScriptVersionDto infoVersionDto = new ScriptVersionDto();
        infoVersionDto.setId(0L);
        infoVersionDto.setInfoUniqueUuid("infoUniqueUuid");
        infoVersionDto.setSrcScriptUuid("srcScriptUuid");
        infoVersionDto.setVersion("version");
        infoVersionDto.setEditState(0);
        when(mockInfoVersionService.selectInfoVersionById(0L)).thenReturn(infoVersionDto);

        // Configure SqlSessionFactory.openSession(...).
        final SqlSession mockSqlSession = mock(SqlSession.class);
        when(mockFactory.openSession(ExecutorType.BATCH, false)).thenReturn(mockSqlSession);

        // Configure ITaskGroupsService.retrieveUniqueAgentInfoList(...).
        final AgentInfoDto agentInfoDto1 = new AgentInfoDto();
        agentInfoDto1.setId(0L);
        agentInfoDto1.setSysmAgentInfoId(0L);
        agentInfoDto1.setAgentIp("agentIp");
        agentInfoDto1.setAgentName("agentName");
        agentInfoDto1.setAgentPort(0);
        final List<AgentInfoDto> agentInfoDtos = Collections.singletonList(agentInfoDto1);
        when(mockTaskGroupsService.retrieveUniqueAgentInfoList(any(List.class))).thenReturn(agentInfoDtos);

        // Configure RemoteCall.applyForDoubleCheck(...).
        final ResultApiDto resultApiDto = new ResultApiDto();
        resultApiDto.setSuccess(true);
        resultApiDto.setMessage("message");
        resultApiDto.setTaskId(0L);

        when(mockRemoteCall.applyForDoubleCheck(any(String.class), any(DoubleCheckApiDto.class))).thenReturn(resultApiDto);

        // Run the test
        final Long result = taskApplyServiceImplUnderTest.scriptExecAuditing(scriptExecAuditDto, user, auditSource);

        // Verify the results
        assertNotNull(result);
        verify(mockSqlSession).close();
        verify(mockTaskAttachmentService).saveTaskAttachement(any(ScriptExecAuditDto.class), any(TaskDto.class),
                any(SqlSession.class));
        verify(mockTaskGroupsService).saveTaskGroups(any(ScriptExecAuditDto.class), any(TaskDto.class),
                any(SqlSession.class));
        verify(mockTaskIpsService).saveTaskIps(any(ScriptExecAuditDto.class), any(TaskDto.class),
                any(SqlSession.class));
        verify(mockTaskParamsService).saveTaskParams(any(ScriptExecAuditDto.class), any(TaskDto.class),
                any(SqlSession.class));
    }


    @Test
    void testScriptExecAuditing_isWhite_schedule() throws Exception {
        // Setup
        final ScriptExecAuditDto scriptExecAuditDto = new ScriptExecAuditDto();
        scriptExecAuditDto.setScriptInfoVersionId(0L);
        final TaskDto taskInfo = new TaskDto();
        taskInfo.setScriptTaskSource(0);
        taskInfo.setStartType(0);
        taskInfo.setReadyToExecute(0);
        taskInfo.setId(0L);
        taskInfo.setSrcScriptUuid("srcScriptUuid");
        taskInfo.setTaskName("taskName");
        taskInfo.setTaskScheduler(0);
        taskInfo.setTaskCron("taskCron");
        taskInfo.setPublishDesc("publishDesc");
        taskInfo.setTimeout(0L);
        taskInfo.setType(0);
        scriptExecAuditDto.setTaskInfo(taskInfo);
        scriptExecAuditDto.setAuditUser("auditUser");
        scriptExecAuditDto.setAuditUserId(0L);
        final AgentInfoDto agentInfoDto = new AgentInfoDto();
        agentInfoDto.setAgentName("服务器");
        agentInfoDto.setAgentIp("127.0.0.1");
        scriptExecAuditDto.setChosedAgentUsers(Collections.singletonList(agentInfoDto));
        final TaskGroupsDto taskGroupsDto = new TaskGroupsDto();
        taskGroupsDto.setId(1L);
        taskGroupsDto.setCpname("资源组");
        scriptExecAuditDto.setChosedResGroups(Collections.singletonList(taskGroupsDto));
        scriptExecAuditDto.setResGroupFlag(true);


        final CurrentUser user = new CurrentUser();
        user.setId(0L);
        user.setLoginName("applyUser");

        // Configure IInfoVersionService.selectInfoVersionById(...).
        final ScriptVersionDto infoVersionDto = new ScriptVersionDto();
        infoVersionDto.setId(0L);
        infoVersionDto.setInfoUniqueUuid("infoUniqueUuid");
        infoVersionDto.setSrcScriptUuid("srcScriptUuid");
        infoVersionDto.setVersion("version");
        infoVersionDto.setEditState(0);
        when(mockInfoVersionService.selectInfoVersionById(0L)).thenReturn(infoVersionDto);

        // Configure SqlSessionFactory.openSession(...).
        final SqlSession mockSqlSession = mock(SqlSession.class);
        when(mockFactory.openSession(ExecutorType.BATCH, false)).thenReturn(mockSqlSession);

        // Configure ITaskGroupsService.retrieveUniqueAgentInfoList(...).
        final AgentInfoDto agentInfoDto1 = new AgentInfoDto();
        agentInfoDto1.setId(0L);
        agentInfoDto1.setSysmAgentInfoId(0L);
        agentInfoDto1.setAgentIp("agentIp");
        agentInfoDto1.setAgentName("agentName");
        agentInfoDto1.setAgentPort(0);
        final List<AgentInfoDto> agentInfoDtos = Collections.singletonList(agentInfoDto1);
        when(mockTaskGroupsService.retrieveUniqueAgentInfoList(any(List.class))).thenReturn(agentInfoDtos);

        // Configure RemoteCall.applyForDoubleCheck(...).
        final ResultApiDto resultApiDto = new ResultApiDto();
        resultApiDto.setSuccess(true);
        resultApiDto.setMessage("message");
        resultApiDto.setTaskId(0L);


        when(mockRemoteCall.applyForDoubleCheck(any(String.class), any(DoubleCheckApiDto.class))).thenReturn(resultApiDto);

        // Run the test
        final Long result = taskApplyServiceImplUnderTest.scriptExecAuditing(scriptExecAuditDto, user,auditSource);

        // Verify the results
        assertNotNull(result);
        verify(mockSqlSession).close();
        verify(mockTaskAttachmentService).saveTaskAttachement(any(ScriptExecAuditDto.class), any(TaskDto.class),
                any(SqlSession.class));
        verify(mockTaskGroupsService).saveTaskGroups(any(ScriptExecAuditDto.class), any(TaskDto.class),
                any(SqlSession.class));
        verify(mockTaskIpsService).saveTaskIps(any(ScriptExecAuditDto.class), any(TaskDto.class),
                any(SqlSession.class));
        verify(mockTaskParamsService).saveTaskParams(any(ScriptExecAuditDto.class), any(TaskDto.class),
                any(SqlSession.class));
    }
    @Test
    void testScriptExecAuditing_isWhite_noTaskDtoId() throws Exception {
        // Setup
        final ScriptExecAuditDto scriptExecAuditDto = new ScriptExecAuditDto();
        scriptExecAuditDto.setScriptInfoVersionId(0L);
        final TaskDto taskInfo = new TaskDto();
        taskInfo.setScriptTaskSource(0);
        taskInfo.setStartType(0);
        taskInfo.setReadyToExecute(0);
        taskInfo.setId(0L);
        taskInfo.setSrcScriptUuid("srcScriptUuid");
        taskInfo.setTaskName("taskName");
        taskInfo.setTaskScheduler(0);
        taskInfo.setTaskCron("taskCron");
        taskInfo.setPublishDesc("publishDesc");
        taskInfo.setTimeout(0L);
        taskInfo.setType(0);
        scriptExecAuditDto.setTaskInfo(taskInfo);
        scriptExecAuditDto.setAuditUser("auditUser");
        scriptExecAuditDto.setAuditUserId(0L);
        final AgentInfoDto agentInfoDto = new AgentInfoDto();
        agentInfoDto.setAgentName("服务器");
        agentInfoDto.setAgentIp("127.0.0.1");
        scriptExecAuditDto.setChosedAgentUsers(Collections.singletonList(agentInfoDto));
        final TaskGroupsDto taskGroupsDto = new TaskGroupsDto();
        taskGroupsDto.setId(1L);
        taskGroupsDto.setCpname("资源组");
        scriptExecAuditDto.setChosedResGroups(Collections.singletonList(taskGroupsDto));
        scriptExecAuditDto.setResGroupFlag(true);


        final CurrentUser user = new CurrentUser();
        user.setId(0L);
        user.setLoginName("applyUser");

        // Configure IInfoVersionService.selectInfoVersionById(...).
        final ScriptVersionDto infoVersionDto = new ScriptVersionDto();
        infoVersionDto.setId(0L);
        infoVersionDto.setInfoUniqueUuid("infoUniqueUuid");
        infoVersionDto.setSrcScriptUuid("srcScriptUuid");
        infoVersionDto.setVersion("version");
        infoVersionDto.setEditState(0);
        when(mockInfoVersionService.selectInfoVersionById(0L)).thenReturn(infoVersionDto);


        // Configure SqlSessionFactory.openSession(...).
        final SqlSession mockSqlSession = mock(SqlSession.class);
        when(mockFactory.openSession(ExecutorType.BATCH, false)).thenReturn(mockSqlSession);

        // Configure ITaskGroupsService.retrieveUniqueAgentInfoList(...).
        final AgentInfoDto agentInfoDto1 = new AgentInfoDto();
        agentInfoDto1.setId(0L);
        agentInfoDto1.setSysmAgentInfoId(0L);
        agentInfoDto1.setAgentIp("agentIp");
        agentInfoDto1.setAgentName("agentName");
        agentInfoDto1.setAgentPort(0);
        final List<AgentInfoDto> agentInfoDtos = Collections.singletonList(agentInfoDto1);
        when(mockTaskGroupsService.retrieveUniqueAgentInfoList(any(List.class))).thenReturn(agentInfoDtos);

        // Configure RemoteCall.applyForDoubleCheck(...).
        final ResultApiDto resultApiDto = new ResultApiDto();
        resultApiDto.setSuccess(false);
        resultApiDto.setMessage("message");
        resultApiDto.setTaskId(0L);


        TaskDto taskDto = new TaskDto();


        // Run the test
        assertThrows(ScriptException.class, () ->{
            taskApplyServiceImplUnderTest.scriptExecAuditing(scriptExecAuditDto, user, auditSource);
        });

        // Verify the results
        verify(mockSqlSession).close();
        verify(mockTaskAttachmentService).saveTaskAttachement(any(ScriptExecAuditDto.class), any(TaskDto.class),
                any(SqlSession.class));
        verify(mockTaskGroupsService).saveTaskGroups(any(ScriptExecAuditDto.class), any(TaskDto.class),
                any(SqlSession.class));
        verify(mockTaskIpsService).saveTaskIps(any(ScriptExecAuditDto.class), any(TaskDto.class),
                any(SqlSession.class));
        verify(mockTaskParamsService).saveTaskParams(any(ScriptExecAuditDto.class), any(TaskDto.class),
                any(SqlSession.class));
    }


    @Test
    void testScriptExecAuditing_noTaskInfo()  {
        // Setup
        final ScriptExecAuditDto scriptExecAuditDto = new ScriptExecAuditDto();
        scriptExecAuditDto.setScriptInfoVersionId(0L);

        scriptExecAuditDto.setAuditUser("auditUser");
        scriptExecAuditDto.setAuditUserId(0L);
        final AgentInfoDto agentInfoDto = new AgentInfoDto();
        agentInfoDto.setAgentName("服务器");
        agentInfoDto.setAgentIp("127.0.0.1");
        scriptExecAuditDto.setChosedAgentUsers(Collections.singletonList(agentInfoDto));
        scriptExecAuditDto.setResGroupFlag(false);
        final TaskGroupsDto taskGroupsDto = new TaskGroupsDto();
        taskGroupsDto.setId(1L);
        taskGroupsDto.setCpname("资源组");
        scriptExecAuditDto.setChosedResGroups(Collections.singletonList(taskGroupsDto));
        scriptExecAuditDto.setResGroupFlag(true);


        final CurrentUser user = new CurrentUser();
        user.setId(0L);
        user.setLoginName("applyUser");


        assertThrows(ScriptException.class ,() ->{
            taskApplyServiceImplUnderTest.scriptExecAuditing(scriptExecAuditDto, user, auditSource);
        });
    }


    @Test
    void testScriptExecAuditing_noAgentUsers() throws Exception {
        // Setup
        final ScriptExecAuditDto scriptExecAuditDto = new ScriptExecAuditDto();
        scriptExecAuditDto.setScriptInfoVersionId(0L);
        final TaskDto taskInfo = new TaskDto();
        taskInfo.setScriptTaskSource(0);
        taskInfo.setStartType(0);
        taskInfo.setReadyToExecute(0);
        taskInfo.setId(0L);
        taskInfo.setSrcScriptUuid("srcScriptUuid");
        taskInfo.setTaskName("taskName");
        taskInfo.setTaskScheduler(0);
        taskInfo.setTaskCron("taskCron");
        taskInfo.setPublishDesc("publishDesc");
        taskInfo.setTimeout(0L);
        taskInfo.setType(0);
        taskInfo.setTaskCron("* 5 * * * ? ");
        scriptExecAuditDto.setTaskInfo(taskInfo);
        scriptExecAuditDto.setAuditUser("auditUser");
        scriptExecAuditDto.setAuditUserId(0L);

        //scriptExecAuditDto.setChosedAgentUsers(Collections.singletonList(agentInfoDto));
        scriptExecAuditDto.setResGroupFlag(false);
        final TaskGroupsDto taskGroupsDto = new TaskGroupsDto();
        taskGroupsDto.setId(1L);
        taskGroupsDto.setCpname("资源组");
        scriptExecAuditDto.setChosedResGroups(Collections.singletonList(taskGroupsDto));
        scriptExecAuditDto.setResGroupFlag(false);


        final CurrentUser user = new CurrentUser();
        user.setId(0L);
        user.setLoginName("applyUser");

        // Configure IInfoVersionService.selectInfoVersionById(...).
        final ScriptVersionDto infoVersionDto = new ScriptVersionDto();
        infoVersionDto.setId(0L);
        infoVersionDto.setInfoUniqueUuid("infoUniqueUuid");
        infoVersionDto.setSrcScriptUuid("srcScriptUuid");
        infoVersionDto.setVersion("version");
        infoVersionDto.setEditState(0);
        when(mockInfoVersionService.selectInfoVersionById(0L)).thenReturn(infoVersionDto);


        // Configure SqlSessionFactory.openSession(...).
        final SqlSession mockSqlSession = mock(SqlSession.class);
        when(mockFactory.openSession(ExecutorType.BATCH, false)).thenReturn(mockSqlSession);

        // Run the test
        assertThrows(ScriptException.class ,() ->{
            taskApplyServiceImplUnderTest.scriptExecAuditing(scriptExecAuditDto, user, auditSource);
        });

        // Verify the results
        verify(mockSqlSession).close();
        verify(mockTaskAttachmentService).saveTaskAttachement(any(ScriptExecAuditDto.class), any(TaskDto.class),
                any(SqlSession.class));
        verify(mockTaskGroupsService).saveTaskGroups(any(ScriptExecAuditDto.class), any(TaskDto.class),
                any(SqlSession.class));
    }
    @Test
    void testScriptExecAuditing_isEmptyAgentUsers() throws Exception {
        // Setup
        final ScriptExecAuditDto scriptExecAuditDto = new ScriptExecAuditDto();
        scriptExecAuditDto.setScriptInfoVersionId(0L);
        final TaskDto taskInfo = new TaskDto();
        taskInfo.setScriptTaskSource(0);
        taskInfo.setStartType(0);
        taskInfo.setReadyToExecute(0);
        taskInfo.setId(0L);
        taskInfo.setSrcScriptUuid("srcScriptUuid");
        taskInfo.setTaskName("taskName");
        taskInfo.setTaskScheduler(0);
        taskInfo.setTaskCron("taskCron");
        taskInfo.setPublishDesc("publishDesc");
        taskInfo.setTimeout(0L);
        taskInfo.setType(0);
        taskInfo.setTaskCron("* 5 * * * ? ");
        taskInfo.setTaskScheduler(1);
        scriptExecAuditDto.setTaskInfo(taskInfo);
        scriptExecAuditDto.setAuditUser("auditUser");
        scriptExecAuditDto.setAuditUserId(0L);
        scriptExecAuditDto.setChosedAgentUsers(new ArrayList<>());

        final TaskGroupsDto taskGroupsDto = new TaskGroupsDto();
        scriptExecAuditDto.setChosedResGroups(Collections.singletonList(taskGroupsDto));
        scriptExecAuditDto.setResGroupFlag(false);

        final CurrentUser user = new CurrentUser();
        user.setId(0L);
        user.setLoginName("applyUser");

        // Configure IInfoVersionService.selectInfoVersionById(...).
        final ScriptVersionDto infoVersionDto = new ScriptVersionDto();
        infoVersionDto.setId(0L);
        infoVersionDto.setInfoUniqueUuid("infoUniqueUuid");
        infoVersionDto.setSrcScriptUuid("srcScriptUuid");
        infoVersionDto.setVersion("version");
        infoVersionDto.setEditState(0);
        when(mockInfoVersionService.selectInfoVersionById(0L)).thenReturn(infoVersionDto);

        // Configure SqlSessionFactory.openSession(...).
        final SqlSession mockSqlSession = mock(SqlSession.class);
        when(mockFactory.openSession(ExecutorType.BATCH, false)).thenReturn(mockSqlSession);

        // Run the test
        assertThrows(ScriptException.class ,() ->{
            taskApplyServiceImplUnderTest.scriptExecAuditing(scriptExecAuditDto, user, auditSource);
        });

        // Verify the results
        verify(mockSqlSession).close();
        verify(mockTaskAttachmentService).saveTaskAttachement(any(ScriptExecAuditDto.class), any(TaskDto.class),
                any(SqlSession.class));
        verify(mockTaskGroupsService).saveTaskGroups(any(ScriptExecAuditDto.class), any(TaskDto.class),
                any(SqlSession.class));
    }


    @Test
    void testScriptExecAuditing_isEmptyAgentGroups() throws Exception {
        // Setup
        final ScriptExecAuditDto scriptExecAuditDto = new ScriptExecAuditDto();
        scriptExecAuditDto.setScriptInfoVersionId(0L);
        final TaskDto taskInfo = new TaskDto();
        taskInfo.setScriptTaskSource(0);
        taskInfo.setStartType(0);
        taskInfo.setReadyToExecute(0);
        taskInfo.setId(0L);
        taskInfo.setSrcScriptUuid("srcScriptUuid");
        taskInfo.setTaskName("taskName");
        taskInfo.setTaskScheduler(0);
        taskInfo.setTaskCron("taskCron");
        taskInfo.setPublishDesc("publishDesc");
        taskInfo.setTimeout(0L);
        taskInfo.setType(0);
        taskInfo.setTaskCron("* 5 * * * ? ");
        taskInfo.setTaskScheduler(1);
        scriptExecAuditDto.setTaskInfo(taskInfo);
        scriptExecAuditDto.setAuditUser("auditUser");
        scriptExecAuditDto.setAuditUserId(0L);

        final AgentInfoDto agentInfoDto = new AgentInfoDto();
        agentInfoDto.setAgentName("服务器");
        agentInfoDto.setAgentIp("127.0.0.1");
        scriptExecAuditDto.setChosedAgentUsers(Collections.singletonList(agentInfoDto));

        scriptExecAuditDto.setChosedResGroups(new ArrayList<>());
        scriptExecAuditDto.setResGroupFlag(true);

        final CurrentUser user = new CurrentUser();
        user.setId(0L);
        user.setLoginName("applyUser");

        // Configure IInfoVersionService.selectInfoVersionById(...).
        final ScriptVersionDto infoVersionDto = new ScriptVersionDto();
        infoVersionDto.setId(0L);
        infoVersionDto.setInfoUniqueUuid("infoUniqueUuid");
        infoVersionDto.setSrcScriptUuid("srcScriptUuid");
        infoVersionDto.setVersion("version");
        infoVersionDto.setEditState(0);
        when(mockInfoVersionService.selectInfoVersionById(0L)).thenReturn(infoVersionDto);


        // Configure SqlSessionFactory.openSession(...).
        final SqlSession mockSqlSession = mock(SqlSession.class);
        when(mockFactory.openSession(ExecutorType.BATCH, false)).thenReturn(mockSqlSession);

        // Run the test
        assertThrows(ScriptException.class ,() ->{
            taskApplyServiceImplUnderTest.scriptExecAuditing(scriptExecAuditDto, user, auditSource);
        });

        // Verify the results
        verify(mockSqlSession).close();
        verify(mockTaskAttachmentService).saveTaskAttachement(any(ScriptExecAuditDto.class), any(TaskDto.class),
                any(SqlSession.class));
        verify(mockTaskGroupsService).saveTaskGroups(any(ScriptExecAuditDto.class), any(TaskDto.class),
                any(SqlSession.class));
    }

    @Test
    void testScriptExecAuditing_noAgentGroups() throws Exception {
        // Setup
        final ScriptExecAuditDto scriptExecAuditDto = new ScriptExecAuditDto();
        scriptExecAuditDto.setScriptInfoVersionId(0L);
        final TaskDto taskInfo = new TaskDto();
        taskInfo.setScriptTaskSource(0);
        taskInfo.setStartType(0);
        taskInfo.setReadyToExecute(0);
        taskInfo.setId(0L);
        taskInfo.setSrcScriptUuid("srcScriptUuid");
        taskInfo.setTaskName("taskName");
        taskInfo.setTaskScheduler(0);
        taskInfo.setTaskCron("taskCron");
        taskInfo.setPublishDesc("publishDesc");
        taskInfo.setTimeout(0L);
        taskInfo.setType(0);
        taskInfo.setTaskCron("* 5 * * * ? ");
        taskInfo.setTaskScheduler(1);
        scriptExecAuditDto.setTaskInfo(taskInfo);
        scriptExecAuditDto.setAuditUser("auditUser");
        scriptExecAuditDto.setAuditUserId(0L);

        final AgentInfoDto agentInfoDto = new AgentInfoDto();
        agentInfoDto.setAgentName("服务器");
        agentInfoDto.setAgentIp("127.0.0.1");
        scriptExecAuditDto.setChosedAgentUsers(Collections.singletonList(agentInfoDto));

        scriptExecAuditDto.setResGroupFlag(true);

        final CurrentUser user = new CurrentUser();
        user.setId(0L);
        user.setLoginName("applyUser");

        // Configure IInfoVersionService.selectInfoVersionById(...).
        final ScriptVersionDto infoVersionDto = new ScriptVersionDto();
        infoVersionDto.setId(0L);
        infoVersionDto.setInfoUniqueUuid("infoUniqueUuid");
        infoVersionDto.setSrcScriptUuid("srcScriptUuid");
        infoVersionDto.setVersion("version");
        infoVersionDto.setEditState(0);
        when(mockInfoVersionService.selectInfoVersionById(0L)).thenReturn(infoVersionDto);

        // Configure SqlSessionFactory.openSession(...).
        final SqlSession mockSqlSession = mock(SqlSession.class);
        when(mockFactory.openSession(ExecutorType.BATCH, false)).thenReturn(mockSqlSession);

        // Run the test
        assertThrows(ScriptException.class ,() ->{
            taskApplyServiceImplUnderTest.scriptExecAuditing(scriptExecAuditDto, user, auditSource);
        });

        // Verify the results
        verify(mockSqlSession).close();
        verify(mockTaskAttachmentService).saveTaskAttachement(any(ScriptExecAuditDto.class), any(TaskDto.class),
                any(SqlSession.class));
        verify(mockTaskGroupsService).saveTaskGroups(any(ScriptExecAuditDto.class), any(TaskDto.class),
                any(SqlSession.class));
    }



    @Test
    void testScriptExecAuditing_errorCorn() throws Exception {
        // Setup
        final ScriptExecAuditDto scriptExecAuditDto = new ScriptExecAuditDto();
        scriptExecAuditDto.setScriptInfoVersionId(0L);
        final TaskDto taskInfo = new TaskDto();
        taskInfo.setScriptTaskSource(0);
        taskInfo.setStartType(0);
        taskInfo.setReadyToExecute(0);
        taskInfo.setId(0L);
        taskInfo.setSrcScriptUuid("srcScriptUuid");
        taskInfo.setTaskName("taskName");
        taskInfo.setTaskScheduler(0);
        taskInfo.setTaskCron("taskCron");
        taskInfo.setPublishDesc("publishDesc");
        taskInfo.setTimeout(0L);
        taskInfo.setType(0);
        taskInfo.setTaskCron("*/2 * * * *");
        taskInfo.setTaskScheduler(1);
        scriptExecAuditDto.setTaskInfo(taskInfo);
        scriptExecAuditDto.setAuditUser("auditUser");
        scriptExecAuditDto.setAuditUserId(0L);
        final AgentInfoDto agentInfoDto = new AgentInfoDto();
        agentInfoDto.setAgentName("服务器");
        agentInfoDto.setAgentIp("127.0.0.1");
        scriptExecAuditDto.setChosedAgentUsers(Collections.singletonList(agentInfoDto));
        final TaskGroupsDto taskGroupsDto = new TaskGroupsDto();
        taskGroupsDto.setId(1L);
        taskGroupsDto.setCpname("资源组");
        scriptExecAuditDto.setChosedResGroups(Collections.singletonList(taskGroupsDto));
        scriptExecAuditDto.setResGroupFlag(true);


        final CurrentUser user = new CurrentUser();
        user.setId(0L);
        user.setLoginName("applyUser");


        // Configure ITaskGroupsService.retrieveUniqueAgentInfoList(...).
        final AgentInfoDto agentInfoDto1 = new AgentInfoDto();
        agentInfoDto1.setId(0L);
        agentInfoDto1.setSysmAgentInfoId(0L);
        agentInfoDto1.setAgentIp("agentIp");
        agentInfoDto1.setAgentName("agentName");
        agentInfoDto1.setAgentPort(0);
        final TaskGroupsDto taskGroupsDto1 = new TaskGroupsDto();
        taskGroupsDto1.setId(0L);
        taskGroupsDto1.setScriptTaskId(0L);
        taskGroupsDto1.setSysmComputerGroupId(0L);
        taskGroupsDto1.setCpname("cpname");
        taskGroupsDto1.setCreateTime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        final List<TaskGroupsDto> taskGroupsDtoList = Collections.singletonList(taskGroupsDto1);
        ScriptVersionDto infoVersionDto = new ScriptVersionDto();
        infoVersionDto.setId(1L);


        // Run the test
        assertThrows(ScriptException.class ,() ->{
            taskApplyServiceImplUnderTest.scriptExecAuditing(scriptExecAuditDto, user, auditSource);
        });
    }

    @Test
    void receiveAuditResult() throws ScriptException, SystemException {
        // 准备测试数据
        AuditResultBean auditResultBean = new AuditResultBean();
        auditResultBean.setAuditRelationId(1L);
        auditResultBean.setState(1);
        auditResultBean.setApprWorkitemId(2L);

        AuditRelationDto auditRelationDto = new AuditRelationDto();
        auditRelationDto.setId(1L);
        auditRelationDto.setState(1);
        auditRelationDto.setApprWorkitemId(2L);
        auditRelationDto.setScriptTaskId(1L);

        TaskDto taskDto = new TaskDto();
        taskDto.setId(3L);
        taskDto.setReadyToExecute(1);
        taskDto.setTaskScheduler(1);

        // 模拟方法调用和返回值
        when(mockAuditRelationService.selectAuditRelationById(anyLong())).thenReturn(auditRelationDto);
        when(mockTaskService.selectTaskById(anyLong())).thenReturn(taskDto);

        // 执行被测试的方法
        taskApplyServiceImplUnderTest.receiveAuditResult(auditResultBean);

        // 验证方法调用次数和参数
        verify(mockTaskService).selectTaskById(anyLong());
    }

    @Test
    void receiveAuditResult_throwScriptException() throws ScriptException, SystemException {
        // 准备测试数据
        AuditResultBean auditResultBean = new AuditResultBean();
        auditResultBean.setAuditRelationId(1L);
        auditResultBean.setState(1);
        auditResultBean.setApprWorkitemId(2L);

        AuditRelationDto auditRelationDto = new AuditRelationDto();
        auditRelationDto.setId(1L);
        auditRelationDto.setState(1);
        auditRelationDto.setApprWorkitemId(2L);
        auditRelationDto.setScriptTaskId(1L);

        TaskDto taskDto = new TaskDto();


        // 模拟方法调用和返回值
        when(mockAuditRelationService.selectAuditRelationById(anyLong())).thenReturn(auditRelationDto);
        when(mockTaskService.selectTaskById(anyLong())).thenReturn(taskDto);

        // 执行被测试的方法
        assertThrows(ScriptException.class, () ->{
            taskApplyServiceImplUnderTest.receiveAuditResult(auditResultBean);
        });

        // 验证方法调用次数和参数
        verify(mockTaskService).selectTaskById(anyLong());
    }

    @Test
    void getAuditDetail() throws ScriptException {
        // 准备测试数据
        Long serviceId = 123L;
        Long taskId = 123L;
        TaskDto mockTaskDto = new TaskDto(); // 构造一个模拟的TaskDto
        mockTaskDto.setSrcScriptUuid("srcScriptUuid");
        mockTaskDto.setId(123L);
        List<TaskAttachmentDto> mockTaskAttachmentDtoList = new ArrayList<>(); // 构造模拟的TaskAttachmentDto列表
        List<TaskParamsDto> mockTaskParamsDtoList = new ArrayList<>(); // 构造模拟的TaskParamsDto列表
        List<AgentInfoDto> mockAgentInfoDtoList = new ArrayList<>(); // 构造模拟的AgentInfoDto列表
        List<TaskGroupsDto> mockTaskGroupsDtoList = new ArrayList<>(); // 构造模拟的TaskGroupsDto列表
        // 模拟依赖方法调用
        when(redisTemplate.hasKey("script:checkBefore:"+taskId)).thenReturn(false);
        when(mockTaskService.selectTaskByServiceId(serviceId, taskId)).thenReturn(mockTaskDto);
        when(taskApplyServiceImplUnderTest.buildTaskAttachment(serviceId,taskId)).thenReturn(mockTaskAttachmentDtoList);
        when(taskApplyServiceImplUnderTest.buildTaskParam(serviceId,taskId)).thenReturn(mockTaskParamsDtoList);
        when(taskApplyServiceImplUnderTest.buildAgent(serviceId,taskId)).thenReturn(mockAgentInfoDtoList);
        when(taskApplyServiceImplUnderTest.buildTaskGroup(serviceId,taskId)).thenReturn(mockTaskGroupsDtoList);

        InfoVersionTextDto infoVersionTextDto = new InfoVersionTextDto();
        infoVersionTextDto.setContent("content");
        when(infoVersionTextService.selectInfoVersionTextByScriptUuid(any(String.class))).thenReturn(infoVersionTextDto);
        // 执行被测试方法
        ScriptAuditDetailDto result = taskApplyServiceImplUnderTest.getAuditDetail(serviceId, taskId);

        // 验证结果
        assertEquals(mockTaskDto, result.getTaskDto());
        assertEquals(mockTaskAttachmentDtoList, result.getTaskAttachmentDtoList());
        assertEquals(mockTaskParamsDtoList, result.getTaskParamsDtoList());
        assertEquals(mockAgentInfoDtoList, result.getAgentInfoDtoList());
        assertEquals(mockTaskGroupsDtoList, result.getTaskGroupsDtoList());
    }

    @Test
    void doubleCheckCallBack() throws ScriptException, SystemException, JsonProcessingException, ParseException {
        // 准备测试数据

        AuditRelationDto auditRelationDto = new AuditRelationDto();
        auditRelationDto.setId(1L);
        auditRelationDto.setState(1);
        auditRelationDto.setApprWorkitemId(2L);
        auditRelationDto.setScriptTaskId(1L);

        when(mockAuditRelationService.selectAuditRelationById(anyLong())).thenReturn(auditRelationDto);
        TaskDto taskDto = new TaskDto();
        taskDto.setId(3L);
        taskDto.setReadyToExecute(1);
        taskDto.setTaskScheduler(1);
        when(mockTaskService.selectTaskById(anyLong())).thenReturn(taskDto);
        DoubleCheckApiDto doubleCheckApiDto = new DoubleCheckApiDto();
        doubleCheckApiDto.setApprovalState(1);
        doubleCheckApiDto.setServiceId(456L);
        doubleCheckApiDto.setId(123L);
        doubleCheckApiDto.setTaskSubject("Your Task Subject");
        doubleCheckApiDto.setDetailUrl("Your Detail URL");
        doubleCheckApiDto.setOriginatorName("Originator Name");
        doubleCheckApiDto.setOriginatorId(789L);
        String dateString = "2024-04-11 12:00:00";
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date expectedDate = dateFormat.parse(dateString);
        doubleCheckApiDto.setOriginateTime(expectedDate);
        String dateString1 = "2024-04-11 13:00:00";
        Date expectedDate1 = dateFormat.parse(dateString1);
        doubleCheckApiDto.setAuditTime(expectedDate1);
        doubleCheckApiDto.setApprovalComment("Your Approval Comment");
        doubleCheckApiDto.setItemType("1");
        doubleCheckApiDto.setCallbackUrl("Your Callback URL");
        // 调用被测试方法
        taskApplyServiceImplUnderTest.doubleCheckCallBack(doubleCheckApiDto);

        // 验证方法调用和行为
        verify(mockTaskService).selectTaskById(anyLong());
    }

    @Test
    void queryUserInfoListByPermissionCode() {

        final List<UserInfoDto> result = taskApplyServiceImplUnderTest.queryUserInfoListByPermissionCode("测试");
        assertNotNull(result);
    }

    @Test
    void getUserByUserId() {
        final List<UserInfoDto> result = taskApplyServiceImplUnderTest.getUserByUserId(1L);
        assertNotNull(result);
    }


    @Test
    void scriptTaskApplyApi() throws ScriptException {
        //组织参数
        ScriptTaskApplyApiDto scriptTaskApplyDto = new ScriptTaskApplyApiDto();
        scriptTaskApplyDto.setScriptUuid("srcScriptUuid");

        scriptTaskApplyDto.setTaskName("taskName");

        List<ParameterValidationDto> paramList = new ArrayList<>();
        ParameterValidationDto parameterValidationDto = new ParameterValidationDto();
        parameterValidationDto.setParamDefaultValue("123");
        parameterValidationDto.setParamOrder(1);
        parameterValidationDto.setParamType("String");
        paramList.add(parameterValidationDto);
        scriptTaskApplyDto.setParams(paramList);

        scriptTaskApplyDto.setStartType(0);

        List<ScriptTaskApplyAgentApiDto> scriptTaskApplyAgentApiDtoList = new ArrayList<>();
        ScriptTaskApplyAgentApiDto scriptTaskApplyAgentApiDto = new ScriptTaskApplyAgentApiDto();
        scriptTaskApplyAgentApiDto.setAgentId("1");
        scriptTaskApplyAgentApiDto.setAgentPort("8080");
        scriptTaskApplyAgentApiDto.setAgentIp("*******");
        scriptTaskApplyAgentApiDtoList.add(scriptTaskApplyAgentApiDto);
        scriptTaskApplyDto.setChosedAgentUsers(scriptTaskApplyAgentApiDtoList);

        scriptTaskApplyDto.setExecuser("root");

        ScriptVersionDto infoVersionDto = new ScriptVersionDto();
        infoVersionDto.setId(1L);
        infoVersionDto.setInfoUniqueUuid("uniqueUuid");

        TaskInstanceDto taskInstanceDto = new TaskInstanceDto();
        taskInstanceDto.setId(1L);
        taskInstanceDto.setScriptTaskId(2L);


        when(mockInfoVersionService.selectInfoVersionBySrcScriptUuid(anyString())).thenReturn(infoVersionDto);

        when(taskInstanceService.selectTaskInstanceByTaskId(any())).thenReturn(taskInstanceDto);

        Long scriptTaskApplyApi = taskApplyServiceImplUnderTest.scriptTaskApplyApi(scriptTaskApplyDto);

        assertNotNull(scriptTaskApplyApi);
    }


    @Test
    void scriptTaskApplyApiStartTypeThree() throws ScriptException {
        //组织参数
        ScriptTaskApplyApiDto scriptTaskApplyDto = new ScriptTaskApplyApiDto();
        scriptTaskApplyDto.setScriptUuid("srcScriptUuid");

        scriptTaskApplyDto.setTaskName("taskName");

        List<ParameterValidationDto> paramList = new ArrayList<>();
        ParameterValidationDto parameterValidationDto = new ParameterValidationDto();
        parameterValidationDto.setParamDefaultValue("123");
        parameterValidationDto.setParamOrder(1);
        parameterValidationDto.setParamType("String");
        paramList.add(parameterValidationDto);
        scriptTaskApplyDto.setParams(paramList);

        scriptTaskApplyDto.setStartType(2);
        scriptTaskApplyDto.setCallerTaskId(1L);

        List<ScriptTaskApplyAgentApiDto> scriptTaskApplyAgentApiDtoList = new ArrayList<>();
        ScriptTaskApplyAgentApiDto scriptTaskApplyAgentApiDto = new ScriptTaskApplyAgentApiDto();
        scriptTaskApplyAgentApiDto.setAgentId("1");
        scriptTaskApplyAgentApiDto.setAgentPort("8080");
        scriptTaskApplyAgentApiDto.setAgentIp("*******");
        scriptTaskApplyAgentApiDtoList.add(scriptTaskApplyAgentApiDto);
        scriptTaskApplyDto.setChosedAgentUsers(scriptTaskApplyAgentApiDtoList);

        scriptTaskApplyDto.setExecuser("root");

        ScriptVersionDto infoVersionDto = new ScriptVersionDto();
        infoVersionDto.setId(1L);
        infoVersionDto.setInfoUniqueUuid("uniqueUuid");

        TaskInstanceDto taskInstanceDto = new TaskInstanceDto();
        taskInstanceDto.setId(1L);
        taskInstanceDto.setScriptTaskId(2L);


        when(mockInfoVersionService.selectInfoVersionBySrcScriptUuid(anyString())).thenReturn(infoVersionDto);

        when(taskInstanceService.selectTaskInstanceByTaskId(any())).thenReturn(taskInstanceDto);

        Long scriptTaskApplyApi = taskApplyServiceImplUnderTest.scriptTaskApplyApi(scriptTaskApplyDto);

        assertNotNull(scriptTaskApplyApi);
    }

    @Test
    void testReTryScriptTask() throws ScriptException {
        List<RetryScriptInstanceApiDto> retryScriptInstanceApiDto = new ArrayList<>();
        RetryScriptInstanceApiDto retryScriptInstanceApiDto1 = new RetryScriptInstanceApiDto();
        retryScriptInstanceApiDto1.setAgentIp("127.0.0.1");
        retryScriptInstanceApiDto.add(retryScriptInstanceApiDto1);

        TaskRuntime taskRuntime = new TaskRuntime();

        when(mockTaskExecuteService.getTaskRuntime(any())).thenReturn(taskRuntime);
        doNothing().when(mockTaskExecuteService).retryScriptServiceShell(any(), any(), any());
        taskApplyServiceImplUnderTest.reTryScriptTask(retryScriptInstanceApiDto);
    }

    @Test
    void testQueryDepartmentUserInfoList() {
        // 准备测试数据
        Long scriptInfoId = 1L;

        // 模拟 InfoMapper 的行为
        Info mockInfo = new Info();
        mockInfo.setId(scriptInfoId);
        mockInfo.setCreatorId(2L); // 假设脚本创建者的 ID 是 2
        when(infoMapper.selectInfoById(scriptInfoId)).thenReturn(mockInfo);

        // 模拟当前用户
        CurrentUser currentUser = new CurrentUser();
        currentUser.setId(1L); // 当前用户的 ID 是 1
        currentUser.setOrgCode("ORG001"); // 设置部门编码

        // 模拟 IUserInfo 的行为
        List<UserInfoApiDto> allUsers = new ArrayList<>();

        // 用户1：主管，与当前用户ID相同，应该被过滤掉
        UserInfoApiDto user1 = new UserInfoApiDto();
        user1.setId(1L);
        user1.setLoginName("user1");
        user1.setLevel(1); // 主管
        user1.setOrgId(1L); // 部门 ID
        allUsers.add(user1);

        // 用户2：普通用户，应该被过滤掉（不是主管）
        UserInfoApiDto user2 = new UserInfoApiDto();
        user2.setId(2L);
        user2.setLoginName("user2");
        user2.setLevel(0); // 普通用户
        user2.setOrgId(1L); // 部门 ID
        allUsers.add(user2);

        // 用户3：主管，不同部门，应该被过滤掉（在其他部门列表中）
        UserInfoApiDto user3 = new UserInfoApiDto();
        user3.setId(3L);
        user3.setLoginName("user3");
        user3.setLevel(1); // 主管
        user3.setOrgId(2L); // 不同部门
        allUsers.add(user3);

        // 用户4：主管，不同部门，不在其他部门列表中，应该被保留
        UserInfoApiDto user4 = new UserInfoApiDto();
        user4.setId(4L);
        user4.setLoginName("user4");
        user4.setLevel(1); // 主管
        user4.setOrgId(3L); // 不同部门
        allUsers.add(user4);

        // 模拟 queryUserInfoListByOtherOrgManagement 的行为
        List<UserInfoApiDto> otherOrgUsers = new ArrayList<>();
        otherOrgUsers.add(user3); // 只有 user3 在其他部门
        when(iUserInfoApi.getUserInfoList(any())).thenReturn(allUsers);
        when(iUserInfoApi.queryUserInfoListByOtherOrgManagement(mockInfo.getCreatorId())).thenReturn(otherOrgUsers);

        try (MockedStatic<CurrentUserUtil> mockedStatic = mockStatic(CurrentUserUtil.class)) {
            // 模拟 CurrentUserUtil.getCurrentUser() 返回我们的测试用户
            mockedStatic.when(CurrentUserUtil::getCurrentUser).thenReturn(currentUser);

            // 执行被测试的方法
            List<UserInfoApiDto> result = taskApplyServiceImplUnderTest.queryDepartmentUserInfoList(scriptInfoId);

            // 验证结果
            assertNotNull(result);
            assertEquals(1, result.size(), "预期结果应该只有一个用户"); // 预期只有 user4 符合条件
            assertEquals("user4", result.get(0).getLoginName(), "预期结果应该是 user4"); // 验证返回的用户是 user4

            // 验证方法调用
            verify(infoMapper).selectInfoById(scriptInfoId);
            verify(iUserInfoApi).getUserInfoList(any());
            verify(iUserInfoApi).queryUserInfoListByOtherOrgManagement(mockInfo.getCreatorId());
        }
    }

    @Test
    void testGetScriptInfoByAuditRelationId() {
        // 准备测试数据
        Long auditRelationId = 1L;
        Long expectedScriptCategoryId = 123L;
        // 模拟 InfoVersionMapper 的行为
        when(mockInfoVersionMapper.getScriptInfoByAuditRelationId(auditRelationId)).thenReturn(expectedScriptCategoryId);
        // 调用被测试的方法
        Long actualScriptCategoryId = taskApplyServiceImplUnderTest.getScriptInfoByAuditRelationId(auditRelationId);
        // 验证结果
        assertEquals(expectedScriptCategoryId, actualScriptCategoryId, "返回的脚本分类 ID 不符合预期");
        // 验证方法调用
        verify(mockInfoVersionMapper).getScriptInfoByAuditRelationId(auditRelationId);
    }

    @Test
    @DisplayName("测试 getUserIdList 方法 - 两个列表都有数据")
    void testGetUserIdList_BothListsHaveData() throws Exception {
        // 使用反射获取私有方法
        Method getUserIdListMethod = TaskApplyServiceImpl.class.getDeclaredMethod(
                "getUserIdList",
                List.class,
                List.class
        );
        getUserIdListMethod.setAccessible(true);

        // 准备测试数据
        List<PermissionUserInfoApiDto> permissionUserList = new ArrayList<>();
        PermissionUserInfoApiDto user1 = new PermissionUserInfoApiDto();
        user1.setId(1L);
        PermissionUserInfoApiDto user2 = new PermissionUserInfoApiDto();
        user2.setId(2L);
        permissionUserList.add(user1);
        permissionUserList.add(user2);

        List<UserBean> userBeanList = new ArrayList<>();
        UserBean userBean1 = new UserBean();
        userBean1.setUserId(3L);
        UserBean userBean2 = new UserBean();
        userBean2.setUserId(4L);
        userBeanList.add(userBean1);
        userBeanList.add(userBean2);

        // 执行方法
        @SuppressWarnings("unchecked")
        List<Long> result = (List<Long>) getUserIdListMethod.invoke(
                taskApplyServiceImplUnderTest,
                permissionUserList,
                userBeanList
        );

        // 验证结果
        assertNotNull(result);
        assertEquals(4, result.size());
        assertTrue(result.contains(1L));
        assertTrue(result.contains(2L));
        assertTrue(result.contains(3L));
        assertTrue(result.contains(4L));
    }

    @Test
    @DisplayName("测试 getUserIdList 方法 - permissionUserList 为空")
    void testGetUserIdList_EmptyPermissionUserList() throws Exception {
        // 使用反射获取私有方法
        Method getUserIdListMethod = TaskApplyServiceImpl.class.getDeclaredMethod(
                "getUserIdList",
                List.class,
                List.class
        );
        getUserIdListMethod.setAccessible(true);

        // 准备测试数据
        List<PermissionUserInfoApiDto> permissionUserList = new ArrayList<>();

        List<UserBean> userBeanList = new ArrayList<>();
        UserBean userBean1 = new UserBean();
        userBean1.setUserId(1L);
        UserBean userBean2 = new UserBean();
        userBean2.setUserId(2L);
        userBeanList.add(userBean1);
        userBeanList.add(userBean2);

        // 执行方法
        @SuppressWarnings("unchecked")
        List<Long> result = (List<Long>) getUserIdListMethod.invoke(
                taskApplyServiceImplUnderTest,
                permissionUserList,
                userBeanList
        );

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());
        assertTrue(result.contains(1L));
        assertTrue(result.contains(2L));
    }

    @Test
    @DisplayName("测试 getUserIdList 方法 - userBeanList 为空")
    void testGetUserIdList_EmptyUserBeanList() throws Exception {
        // 使用反射获取私有方法
        Method getUserIdListMethod = TaskApplyServiceImpl.class.getDeclaredMethod(
                "getUserIdList",
                List.class,
                List.class
        );
        getUserIdListMethod.setAccessible(true);

        // 准备测试数据
        List<PermissionUserInfoApiDto> permissionUserList = new ArrayList<>();
        PermissionUserInfoApiDto user1 = new PermissionUserInfoApiDto();
        user1.setId(1L);
        PermissionUserInfoApiDto user2 = new PermissionUserInfoApiDto();
        user2.setId(2L);
        permissionUserList.add(user1);
        permissionUserList.add(user2);

        List<UserBean> userBeanList = new ArrayList<>();

        // 执行方法
        @SuppressWarnings("unchecked")
        List<Long> result = (List<Long>) getUserIdListMethod.invoke(
                taskApplyServiceImplUnderTest,
                permissionUserList,
                userBeanList
        );

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());
        assertTrue(result.contains(1L));
        assertTrue(result.contains(2L));
    }

    @Test
    @DisplayName("测试 getUserIdList 方法 - 两个列表都为空")
    void testGetUserIdList_BothListsEmpty() throws Exception {
        // 使用反射获取私有方法
        Method getUserIdListMethod = TaskApplyServiceImpl.class.getDeclaredMethod(
                "getUserIdList",
                List.class,
                List.class
        );
        getUserIdListMethod.setAccessible(true);

        // 准备测试数据
        List<PermissionUserInfoApiDto> permissionUserList = new ArrayList<>();
        List<UserBean> userBeanList = new ArrayList<>();

        // 执行方法
        @SuppressWarnings("unchecked")
        List<Long> result = (List<Long>) getUserIdListMethod.invoke(
                taskApplyServiceImplUnderTest,
                permissionUserList,
                userBeanList
        );

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void testQueryPermissionUserInfoList_Success() {
        // 准备测试数据
        ServicePermissionApiQueryDto queryDto = new ServicePermissionApiQueryDto();
        Long categoryId = 1L;

        // 模拟当前用户
        CurrentUser currentUser = new CurrentUser();
        currentUser.setOrgId(1L);
        currentUser.setSupervisor(false);
        currentUser.setOrgCode("testOrgCode");

        try (MockedStatic<CurrentUserUtil> currentUserUtilMock = mockStatic(CurrentUserUtil.class)) {
            currentUserUtilMock.when(CurrentUserUtil::getCurrentUser).thenReturn(currentUser);

            // 设置 orgManagementFullNames
            List<String> orgManagementFullNames = Collections.singletonList("testOrg");
            queryDto.setOrgManagementFullNames(orgManagementFullNames);

            // 模拟权限用户查询
            PermissionUserInfoApiDto userInfo = new PermissionUserInfoApiDto();
            userInfo.setId(1L);
            userInfo.setLoginName("testUser");
            userInfo.setFullName("Test User");
            List<PermissionUserInfoApiDto> userInfoList = Collections.singletonList(userInfo);
            when(userInfoApi.queryPermissionUserInfoList(any(ServicePermissionApiQueryDto.class)))
                    .thenReturn(userInfoList);

            // 执行测试
            List<UserInfoApiDto> result = taskApplyServiceImplUnderTest.queryPermissionUserInfoList(queryDto, categoryId);

            // 验证结果
            assertNotNull(result);
            assertEquals(0, result.size());

        }
    }


    @Test
    void testQueryPermissionUserInfoList_Role() {
        // 准备测试数据
        ServicePermissionApiQueryDto queryDto = new ServicePermissionApiQueryDto();
        Long categoryId = 1L;

        // 模拟当前用户
        CurrentUser currentUser = new CurrentUser();
        currentUser.setOrgId(1L);
        currentUser.setSupervisor(false);
        currentUser.setOrgCode("testOrgCode");

        try (MockedStatic<CurrentUserUtil> currentUserUtilMock = mockStatic(CurrentUserUtil.class)) {
            currentUserUtilMock.when(CurrentUserUtil::getCurrentUser).thenReturn(currentUser);
            when(myScriptServiceScripts.getScriptBusinessConfig()).thenReturn(scriptBusinessConfig);

            when(myScriptServiceScripts.getCategoryMapper()).thenReturn(categoryMapper);


            List<CategoryRoleBean> categoryRoleRelations = new ArrayList<>();
            CategoryRoleBean categoryRoleBean = new CategoryRoleBean();
            categoryRoleBean.setRoleId(1L);
            categoryRoleRelations.add(categoryRoleBean);


            when(categoryMapper.getCategoryRoleRelations(any())).thenReturn(categoryRoleRelations);

            // 设置 orgManagementFullNames
            List<String> orgManagementFullNames = Collections.singletonList("testOrg");
            queryDto.setOrgManagementFullNames(orgManagementFullNames);

            // 模拟权限用户查询
            PermissionUserInfoApiDto userInfo = new PermissionUserInfoApiDto();
            userInfo.setId(1L);
            userInfo.setLoginName("testUser");
            userInfo.setFullName("Test User");
            List<PermissionUserInfoApiDto> userInfoList = Collections.singletonList(userInfo);
            when(userInfoApi.queryPermissionUserInfoList(any(ServicePermissionApiQueryDto.class)))
                    .thenReturn(userInfoList);

            // 执行测试
            List<UserInfoApiDto> result = taskApplyServiceImplUnderTest.queryPermissionUserInfoList(queryDto, categoryId);

            // 验证结果
            assertNotNull(result);
            assertEquals(0, result.size());

        }
    }

    @Test
    void testQueryPermissionUserInfoList_NullCategoryId() {
        // 准备测试数据
        ServicePermissionApiQueryDto queryDto = new ServicePermissionApiQueryDto();
        Long categoryId = 1L;

        when(userInfoApi.queryUserInfoListByPermissionCode(anyString()))
                .thenReturn(new ArrayList<>());

        // 执行测试并验证异常
        Exception exception = assertThrows(Exception.class,
                () -> taskApplyServiceImplUnderTest.queryPermissionUserInfoList(queryDto,categoryId));
        assertNull(exception.getMessage());
    }

    @Test
    void testQueryPermissionUserInfoList_NoOrgRelations() {
        // 准备测试数据
        ServicePermissionApiQueryDto queryDto = new ServicePermissionApiQueryDto();
        Long categoryId = 1L;

        // 模拟当前用户
        CurrentUser currentUser = new CurrentUser();
        currentUser.setOrgId(1L);
        currentUser.setSupervisor(false);
        currentUser.setOrgCode("testOrgCode");

        try (MockedStatic<CurrentUserUtil> currentUserUtilMock = mockStatic(CurrentUserUtil.class)) {
            currentUserUtilMock.when(CurrentUserUtil::getCurrentUser).thenReturn(currentUser);

            // 模拟空的分类部门关系
            List<OrgBean> orgBeansList = new ArrayList<>();
            OrgBean orgBean = new OrgBean();
            orgBean.setCategoryId(1L);
            orgBean.setId(1L);
            orgBean.setOrgId(1L);
            orgBean.setCode("abc");
            orgBean.setName("testName");
            orgBeansList.add(orgBean);

            CategoryOrgBean categoryOrgBean = new CategoryOrgBean();
            categoryOrgBean.setOrgList(orgBeansList);
            when(mockCategoryService.getCategoryOrgRelations(1L))
                    .thenReturn(categoryOrgBean);

            // 执行测试
            List<UserInfoApiDto> result = taskApplyServiceImplUnderTest.queryPermissionUserInfoList(queryDto, categoryId);

            // 验证结果
            assertNotNull(result);
            assertTrue(result.isEmpty());

            // 验证调用
            verify(userInfoApi, never()).queryPermissionUserInfoList(any(ServicePermissionApiQueryDto.class));
        }
    }

    @Test
    void testQueryPermissionUserInfoList_NoPermissionUsers() {
        // 准备测试数据
        ServicePermissionApiQueryDto queryDto = new ServicePermissionApiQueryDto();
        Long categoryId = 1L;

        // 模拟当前用户
        CurrentUser currentUser = new CurrentUser();
        currentUser.setOrgId(1L);
        currentUser.setSupervisor(false);
        currentUser.setOrgCode("testOrgCode");

        try (MockedStatic<CurrentUserUtil> currentUserUtilMock = mockStatic(CurrentUserUtil.class)) {
            currentUserUtilMock.when(CurrentUserUtil::getCurrentUser).thenReturn(currentUser);

            // 模拟空的权限用户列表
            when(userInfoApi.queryPermissionUserInfoList(any(ServicePermissionApiQueryDto.class)))
                    .thenReturn(Collections.emptyList());


            List<OrgBean> orgBeansList = new ArrayList<>();
            OrgBean orgBean = new OrgBean();
            orgBean.setCategoryId(1L);
            orgBean.setId(1L);
            orgBean.setOrgId(1L);
            orgBean.setCode("abc");
            orgBean.setName("testName");
            orgBeansList.add(orgBean);

            CategoryOrgBean categoryOrgBean = new CategoryOrgBean();
            categoryOrgBean.setOrgList(orgBeansList);

            when(mockCategoryService.getCategoryOrgRelations(1L))
                    .thenReturn(categoryOrgBean);

            // 执行测试
            List<UserInfoApiDto> result = taskApplyServiceImplUnderTest.queryPermissionUserInfoList(queryDto, categoryId);

            // 验证结果
            assertNotNull(result);
            assertTrue(result.isEmpty());

        }
    }

    @Test
    @DisplayName("测试下载导入模板 - 成功场景")
    void testDownloadImportTemplate_Success() throws IOException {
        // Arrange
        MockHttpServletResponse response = new MockHttpServletResponse();

        // Act
        taskApplyServiceImplUnderTest.downloadImportTemplate(response);

        // Assert
        assertEquals("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8", response.getContentType());
        assertEquals("utf-8", response.getCharacterEncoding());
        assertEquals("attachment;filename=" + URLEncoder.encode("服务器导入模板", "UTF-8") + ".xlsx",
                response.getHeader("Content-disposition"));
        assertTrue(response.getContentAsByteArray().length > 0);
    }

    @Test
    @DisplayName("测试下载导入模板 - IO异常场景")
    void testDownloadImportTemplate_IOException() throws IOException, IOException {
        // Arrange
        HttpServletResponse mockResponse = mock(HttpServletResponse.class);
        when(mockResponse.getOutputStream()).thenThrow(new IOException("模拟IO异常"));

        // Act
        taskApplyServiceImplUnderTest.downloadImportTemplate(mockResponse);

        // Assert
        verify(mockResponse).setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        verify(mockResponse).setCharacterEncoding("utf-8");
        verify(mockResponse).setHeader(eq("Content-disposition"),
                eq("attachment;filename=" + URLEncoder.encode("服务器导入模板", "UTF-8") + ".xlsx"));
//        verify(logger).error(eq("下载模板失败"), any(IOException.class));
    }

    @Test
    @DisplayName("测试下载导入模板 - 验证Excel内容")
    void testDownloadImportTemplate_ValidateContent() throws IOException {
        // Arrange
        MockHttpServletResponse response = new MockHttpServletResponse();

        // Act
        taskApplyServiceImplUnderTest.downloadImportTemplate(response);

        // Assert
        byte[] content = response.getContentAsByteArray();

        // 使用EasyExcel读取生成的Excel文件
        try (InputStream inputStream = new ByteArrayInputStream(content)) {
            List<Map<Integer, String>> dataList = EasyExcel.read(inputStream).sheet("服务器").doReadSync();

            // 验证表头
            assertTrue(dataList.isEmpty());
        }
    }

    @Test
    @DisplayName("测试下载导入模板 - 验证中文文件名编码")
    void testDownloadImportTemplate_ValidateChineseFilename() throws IOException {
        // Arrange
        MockHttpServletResponse response = new MockHttpServletResponse();

        // Act
        taskApplyServiceImplUnderTest.downloadImportTemplate(response);

        // Assert
        String contentDisposition = response.getHeader("Content-disposition");
        String expectedFilename = URLEncoder.encode("服务器导入模板", "UTF-8") + ".xlsx";
        if (contentDisposition != null) {
            assertTrue(contentDisposition.contains(expectedFilename));
        }

        // 验证编码后的文件名不包含非法字符
        if (contentDisposition != null) {
            assertFalse(contentDisposition.contains(" "));
        }
        assertTrue(contentDisposition.matches("attachment;filename=[\\w.%-]+\\.xlsx"));
    }

    @Test
    @DisplayName("测试端口号验证 - 通过反射调用私有方法")
    void testIsValidPort() throws Exception {
        // 使用反射获取私有方法
        Method isValidPortMethod = TaskApplyServiceImpl.class.getDeclaredMethod("isValidPort", String.class);
        isValidPortMethod.setAccessible(true);

        // 测试有效端口号
        assertTrue((Boolean) isValidPortMethod.invoke(taskApplyServiceImplUnderTest, "80"));
        assertTrue((Boolean) isValidPortMethod.invoke(taskApplyServiceImplUnderTest, "8080"));
        assertTrue((Boolean) isValidPortMethod.invoke(taskApplyServiceImplUnderTest, "0"));
        assertTrue((Boolean) isValidPortMethod.invoke(taskApplyServiceImplUnderTest, "65535"));

        // 测试无效端口号
        assertFalse((Boolean) isValidPortMethod.invoke(taskApplyServiceImplUnderTest, ""));
        assertFalse((Boolean) isValidPortMethod.invoke(taskApplyServiceImplUnderTest, "-1"));
        assertFalse((Boolean) isValidPortMethod.invoke(taskApplyServiceImplUnderTest, "65536"));
        assertFalse((Boolean) isValidPortMethod.invoke(taskApplyServiceImplUnderTest, "abc"));
    }

    @Test
    @DisplayName("测试校验代理信息")
    void testCheckAgentInfo() throws Exception {
        // 使用反射获取私有方法
        Method checkAgentInfoMethod = TaskApplyServiceImpl.class.getDeclaredMethod("checkAgentInfo",
                List.class, Map.class, List.class, List.class);
        checkAgentInfoMethod.setAccessible(true);

        // 准备测试数据
        List<String> addressList = Arrays.asList("***********", "***********");
        Map<String, AgentInfoDto> agentInfoDtoMap = new HashMap<>();
        List<AgentInfoDto> agentInfoDtoList = new ArrayList<>();
        List<String> notExistMessage = new ArrayList<>();

        // 添加一个存在的Agent
        AgentInfoDto agentInfoDto = new AgentInfoDto();
        agentInfoDto.setAgentIp("***********");
        agentInfoDto.setAgentState(0);
        agentInfoDtoMap.put("***********", agentInfoDto);

        // 执行测试
        checkAgentInfoMethod.invoke(taskApplyServiceImplUnderTest,
                addressList, agentInfoDtoMap, agentInfoDtoList, notExistMessage);

        // 验证结果
        assertEquals(1, agentInfoDtoList.size());
        assertEquals(1, notExistMessage.size());
        assertTrue(notExistMessage.get(0).contains("***********"));
        assertEquals(agentInfoDto, agentInfoDtoList.get(0));
    }

    @Test
    @DisplayName("测试buildAgentInfoMap方法")
    void testBuildAgentInfoMap() throws Exception {
        // 使用反射获取私有方法
        Method buildAgentInfoMapMethod = TaskApplyServiceImpl.class.getDeclaredMethod("buildAgentInfoMap",
                List.class);
        buildAgentInfoMapMethod.setAccessible(true);

        // 准备测试数据
        PageInfo<SystemAgentInfoApiDto> pageInfo = new PageInfo<>();
        List<SystemAgentInfoApiDto> agentList = new ArrayList<>();

        // 创建测试用例
        SystemAgentInfoApiDto agent1 = new SystemAgentInfoApiDto();
        agent1.setAgentIp("***********");
        agent1.setAgentPort("8080");
        agent1.setAgentName("Agent1");

        SystemAgentInfoApiDto agent2 = new SystemAgentInfoApiDto();
        agent2.setAgentIp("***********");
        agent2.setAgentPort("8081");
        agent2.setAgentName("Agent2");

        agentList.add(agent1);
        agentList.add(agent2);
        pageInfo.setList(agentList);

        // Mock taskGroupsService的convertToAgentInfoDto方法
        AgentInfoDto agentInfoDto1 = new AgentInfoDto();
        agentInfoDto1.setAgentIp("***********");

        AgentInfoDto agentInfoDto2 = new AgentInfoDto();
        agentInfoDto2.setAgentIp("***********");

        when(mockTaskGroupsService.convertToAgentInfoDto(agent1)).thenReturn(agentInfoDto1);
        when(mockTaskGroupsService.convertToAgentInfoDto(agent2)).thenReturn(agentInfoDto2);

        // 执行方法
        @SuppressWarnings("unchecked")
        Map<String, AgentInfoDto> result = (Map<String, AgentInfoDto>) buildAgentInfoMapMethod.invoke(
                taskApplyServiceImplUnderTest, agentList);

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());
        assertTrue(result.containsKey("***********"));
        assertTrue(result.containsKey("***********"));
        assertEquals(agentInfoDto1, result.get("***********"));
        assertEquals(agentInfoDto2, result.get("***********"));

        // 验证convertToAgentInfoDto方法被调用
        verify(mockTaskGroupsService).convertToAgentInfoDto(agent1);
        verify(mockTaskGroupsService).convertToAgentInfoDto(agent2);
    }

    @Test
    @DisplayName("测试buildAgentInfoMap方法 - 空列表场景")
    void testBuildAgentInfoMap_EmptyList() throws Exception {
        // 使用反射获取私有方法
        Method buildAgentInfoMapMethod = TaskApplyServiceImpl.class.getDeclaredMethod("buildAgentInfoMap",
                List.class);
        buildAgentInfoMapMethod.setAccessible(true);

        // 准备空列表测试数据
        PageInfo<SystemAgentInfoApiDto> pageInfo = new PageInfo<>();
        pageInfo.setList(new ArrayList<>());

        // 执行方法
        @SuppressWarnings("unchecked")
        Map<String, AgentInfoDto> result = (Map<String, AgentInfoDto>) buildAgentInfoMapMethod.invoke(
                taskApplyServiceImplUnderTest, pageInfo.getList());

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    @DisplayName("测试validateExcelData方法 - 正常IP地址")
    void testValidateExcelData_ValidIP() throws Exception {
        // 使用反射获取私有方法
        Method validateExcelDataMethod = TaskApplyServiceImpl.class.getDeclaredMethod("validateExcelData",
                List.class, List.class, List.class);
        validateExcelDataMethod.setAccessible(true);

        // 准备测试数据
        List<Object> firstSheetDataList = new ArrayList<>();
        Map<Integer, String> row1 = new HashMap<>();
        row1.put(0, "***********");
        firstSheetDataList.add(row1);

        List<String> addressList = new ArrayList<>();
        List<String> errorMessage = new CopyOnWriteArrayList<>();

        // 执行方法
        validateExcelDataMethod.invoke(taskApplyServiceImplUnderTest, firstSheetDataList, addressList, errorMessage);

        // 验证结果
        assertTrue(errorMessage.isEmpty());
        assertEquals(1, addressList.size());
        assertEquals("***********", addressList.get(0));
    }

    @Test
    @DisplayName("测试validateExcelData方法 - 无效IP地址")
    void testValidateExcelData_InvalidIP() throws Exception {
        // 使用反射获取私有方法
        Method validateExcelDataMethod = TaskApplyServiceImpl.class.getDeclaredMethod("validateExcelData",
                List.class, List.class, List.class);
        validateExcelDataMethod.setAccessible(true);

        // 准备测试数据
        List<Object> firstSheetDataList = new ArrayList<>();
        Map<Integer, String> row1 = new HashMap<>();
        row1.put(0, "256.256.256.256");  // 无效IP
        firstSheetDataList.add(row1);

        List<String> addressList = new ArrayList<>();
        List<String> errorMessage = new CopyOnWriteArrayList<>();

        // 执行方法
        validateExcelDataMethod.invoke(taskApplyServiceImplUnderTest, firstSheetDataList, addressList, errorMessage);

        // 验证结果
        assertEquals(1, errorMessage.size());
        assertTrue(errorMessage.get(0).contains("未能通过ip校验"));
        assertTrue(addressList.isEmpty());
    }

    @Test
    @DisplayName("测试validateExcelData方法 - 多行数据混合场景")
    void testValidateExcelData_MixedData() throws Exception {
        // 使用反射获取私有方法
        Method validateExcelDataMethod = TaskApplyServiceImpl.class.getDeclaredMethod("validateExcelData",
                List.class, List.class, List.class);
        validateExcelDataMethod.setAccessible(true);

        // 准备测试数据
        List<Object> firstSheetDataList = new ArrayList<>();

        Map<Integer, String> row1 = new HashMap<>();
        row1.put(0, "***********");  // 有效IP

        Map<Integer, String> row2 = new HashMap<>();
        row2.put(0, "256.256.256.256");  // 无效IP

        Map<Integer, String> row3 = new HashMap<>();
        row3.put(0, "********");  // 有效IP

        firstSheetDataList.add(row1);
        firstSheetDataList.add(row2);
        firstSheetDataList.add(row3);

        List<String> addressList = new ArrayList<>();
        List<String> errorMessage = new CopyOnWriteArrayList<>();

        // 执行方法
        validateExcelDataMethod.invoke(taskApplyServiceImplUnderTest, firstSheetDataList, addressList, errorMessage);

        // 验证结果
        assertEquals(1, errorMessage.size());
        assertEquals(2, addressList.size());
        assertTrue(addressList.contains("***********"));
        assertTrue(addressList.contains("********"));
        assertTrue(errorMessage.get(0).contains("256.256.256.256"));
    }

    @Test
    @DisplayName("测试validateExcelData方法 - 空数据场景")
    void testValidateExcelData_EmptyData() throws Exception {
        // 使用反射获取私有方法
        Method validateExcelDataMethod = TaskApplyServiceImpl.class.getDeclaredMethod("validateExcelData",
                List.class, List.class, List.class);
        validateExcelDataMethod.setAccessible(true);

        // 准备测试数据
        List<Object> firstSheetDataList = new ArrayList<>();
        List<String> addressList = new ArrayList<>();
        List<String> errorMessage = new CopyOnWriteArrayList<>();

        // 执行方法
        validateExcelDataMethod.invoke(taskApplyServiceImplUnderTest, firstSheetDataList, addressList, errorMessage);

        // 验证结果
        assertTrue(errorMessage.isEmpty());
        assertTrue(addressList.isEmpty());
    }

    @Test
    @DisplayName("测试importServerExcel方法 - 成功场景")
    void testImportServerExcel_Success() throws Exception {
        // 准备测试数据
        MockMultipartFile file = new MockMultipartFile(
                "file",
                "test.xlsx",
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                "test data".getBytes()
        );

        // Mock CurrentUser
        CurrentUser mockUser = new CurrentUser();
        mockUser.setId(1L);
        try (MockedStatic<CurrentUserUtil> mockedStatic = mockStatic(CurrentUserUtil.class)) {
            mockedStatic.when(CurrentUserUtil::getCurrentUser).thenReturn(mockUser);

            // Mock AgentGroupRoleQueryBean
            AgentGroupRoleQueryBean queryBean = new AgentGroupRoleQueryBean();
            queryBean.setUserId(1L);
            queryBean.setPageNum(1);
            queryBean.setPageSize(50000);
            queryBean.setBusinessType("脚本服务化");

            // Mock PageInfo
            PageInfo<SystemAgentInfoApiDto> pageInfo = new PageInfo<>();
            List<SystemAgentInfoApiDto> agentList = new ArrayList<>();
            SystemAgentInfoApiDto agent = new SystemAgentInfoApiDto();
            agent.setAgentIp("***********");
            agent.setAgentState(0);
            agentList.add(agent);
            pageInfo.setList(agentList);

            // Mock EasyExcel读取结果
            List<Object> mockData = new ArrayList<>();
            Map<Integer, String> row = new HashMap<>();
            row.put(0, "***********");
            mockData.add(row);

            try (MockedStatic<FastExcelFactory> easyExcelMock = mockStatic(FastExcelFactory.class)) {
                ExcelReaderBuilder mockBuilder = mock(ExcelReaderBuilder.class);
                ExcelReaderSheetBuilder mockSheetBuilder = mock(ExcelReaderSheetBuilder.class);

                easyExcelMock.when(() -> FastExcelFactory.read(any(InputStream.class)))
                        .thenReturn(mockBuilder);
                when(mockBuilder.sheet(0)).thenReturn(mockSheetBuilder);
                when(mockSheetBuilder.autoTrim(true)).thenReturn(mockSheetBuilder);
                when(mockSheetBuilder.doReadSync()).thenReturn(mockData);

                // Mock iAgentInfo
                when(iAgentInfo.queryAgentInfoGroupRole(any(AgentGroupRoleQueryBean.class)))
                        .thenReturn(pageInfo);

                // Mock taskGroupsService
                AgentInfoDto agentInfoDto = new AgentInfoDto();
                agentInfoDto.setAgentIp("***********");
                agentInfoDto.setAgentState(0);
                when(mockTaskGroupsService.convertToAgentInfoDto(any(SystemAgentInfoApiDto.class)))
                        .thenReturn(agentInfoDto);

                // 执行方法
                Map<String, Object> result = taskApplyServiceImplUnderTest.importServerExcel(file);

                // 验证结果
                assertNotNull(result);
                assertTrue(((List<String>)result.get("errorMessage")).isEmpty());
                assertTrue(((List<String>)result.get("notExistMessage")).isEmpty());
                assertEquals(1, ((List<AgentInfoDto>)result.get("agentInfoDtoList")).size());

                // 验证调用
                verify(iAgentInfo).queryAgentInfoGroupRole(any(AgentGroupRoleQueryBean.class));
                verify(mockTaskGroupsService).convertToAgentInfoDto(any(SystemAgentInfoApiDto.class));
            }
        }
    }

    @Test
    @DisplayName("测试importServerExcel方法 - 无效IP场景")
    void testImportServerExcel_InvalidIP() throws Exception {
        // 准备测试数据
        MockMultipartFile file = new MockMultipartFile(
                "file",
                "test.xlsx",
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                "test data".getBytes()
        );

        // Mock CurrentUser
        CurrentUser mockUser = new CurrentUser();
        mockUser.setId(1L);
        try (MockedStatic<CurrentUserUtil> mockedStatic = mockStatic(CurrentUserUtil.class)) {
            mockedStatic.when(CurrentUserUtil::getCurrentUser).thenReturn(mockUser);

            // Mock EasyExcel读取结果 - 无效IP
            List<Object> mockData = new ArrayList<>();
            Map<Integer, String> row = new HashMap<>();
            row.put(0, "256.256.256.256");  // 无效IP
            mockData.add(row);

            try (MockedStatic<FastExcelFactory> easyExcelMock = mockStatic(FastExcelFactory.class)) {
                ExcelReaderBuilder mockBuilder = mock(ExcelReaderBuilder.class);
                ExcelReaderSheetBuilder mockSheetBuilder = mock(ExcelReaderSheetBuilder.class);

                easyExcelMock.when(() -> FastExcelFactory.read(any(InputStream.class)))
                        .thenReturn(mockBuilder);
                when(mockBuilder.sheet(0)).thenReturn(mockSheetBuilder);
                when(mockSheetBuilder.autoTrim(true)).thenReturn(mockSheetBuilder);
                when(mockSheetBuilder.doReadSync()).thenReturn(mockData);

                // Mock PageInfo
                PageInfo<SystemAgentInfoApiDto> pageInfo = new PageInfo<>();
                pageInfo.setList(new ArrayList<>());
                when(iAgentInfo.queryAgentInfoGroupRole(any(AgentGroupRoleQueryBean.class)))
                        .thenReturn(pageInfo);

                // 执行方法
                Map<String, Object> result = taskApplyServiceImplUnderTest.importServerExcel(file);

                // 验证结果
                assertNotNull(result);
                assertEquals(1, ((List<String>)result.get("errorMessage")).size());
                assertTrue(((List<String>)result.get("notExistMessage")).isEmpty());
                assertTrue(((List<AgentInfoDto>)result.get("agentInfoDtoList")).isEmpty());
            }
        }
    }

    @Test
    @DisplayName("测试importServerExcel方法 - 服务器不存在场景")
    void testImportServerExcel_ServerNotExist() throws Exception {
        // 准备测试数据
        MockMultipartFile file = new MockMultipartFile(
                "file",
                "test.xlsx",
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                "test data".getBytes()
        );

        // Mock CurrentUser
        CurrentUser mockUser = new CurrentUser();
        mockUser.setId(1L);
        try (MockedStatic<CurrentUserUtil> mockedStatic = mockStatic(CurrentUserUtil.class)) {
            mockedStatic.when(CurrentUserUtil::getCurrentUser).thenReturn(mockUser);

            // Mock EasyExcel读取结果
            List<Object> mockData = new ArrayList<>();
            Map<Integer, String> row = new HashMap<>();
            row.put(0, "***********");
            mockData.add(row);

            try (MockedStatic<FastExcelFactory> easyExcelMock = mockStatic(FastExcelFactory.class)) {
                ExcelReaderBuilder mockBuilder = mock(ExcelReaderBuilder.class);
                ExcelReaderSheetBuilder mockSheetBuilder = mock(ExcelReaderSheetBuilder.class);

                easyExcelMock.when(() -> FastExcelFactory.read(any(InputStream.class)))
                        .thenReturn(mockBuilder);
                when(mockBuilder.sheet(0)).thenReturn(mockSheetBuilder);
                when(mockSheetBuilder.autoTrim(true)).thenReturn(mockSheetBuilder);
                when(mockSheetBuilder.doReadSync()).thenReturn(mockData);

                // Mock PageInfo - 返回空列表表示服务器不存在
                PageInfo<SystemAgentInfoApiDto> pageInfo = new PageInfo<>();
                pageInfo.setList(new ArrayList<>());
                when(iAgentInfo.queryAgentInfoGroupRole(any(AgentGroupRoleQueryBean.class)))
                        .thenReturn(pageInfo);

                // 执行方法
                Map<String, Object> result = taskApplyServiceImplUnderTest.importServerExcel(file);

                // 验证结果
                assertNotNull(result);
                assertTrue(((List<String>)result.get("errorMessage")).isEmpty());
                assertEquals(1, ((List<String>)result.get("notExistMessage")).size());
                assertTrue(((List<AgentInfoDto>)result.get("agentInfoDtoList")).isEmpty());
            }
        }
    }

    @Test
    @DisplayName("测试deleteAttachment方法")
    void testDeleteAttachment() {
        // 准备测试数据
        Long attachmentId = 1L;

        // Mock SetOperations
        SetOperations<String, String> setOperations = mock(SetOperations.class);
        when(redisTemplate.opsForSet()).thenReturn(setOperations);

        // 执行方法
        taskApplyServiceImplUnderTest.deleteAttachment(attachmentId);

        // 验证调用
        verify(mockTaskAttachmentService).deleteTaskAttachmentById(attachmentId);
        verify(redisTemplate.opsForSet()).remove(
                eq(Constants.REDIS_SCRIPT_TEMP_ATTACHMENT),
                eq(String.valueOf(attachmentId))
        );
    }

    @Test
    @DisplayName("测试deleteAttachment方法 - 处理异常情况")
    void testDeleteAttachment_HandleException() {
        // 准备测试数据
        Long attachmentId = 1L;

        // Mock SetOperations
        SetOperations<String, String> setOperations = mock(SetOperations.class);
        when(redisTemplate.opsForSet()).thenReturn(setOperations);

        // Mock taskAttachmentService抛出异常
        doThrow(new RuntimeException("Delete failed"))
                .when(mockTaskAttachmentService)
                .deleteTaskAttachmentById(attachmentId);

        // 执行方法并验证异常被抛出
        assertThrows(RuntimeException.class, () ->
                taskApplyServiceImplUnderTest.deleteAttachment(attachmentId)
        );

        // 验证调用
        verify(mockTaskAttachmentService).deleteTaskAttachmentById(attachmentId);
        // 验证redis操作没有被执行
        verify(redisTemplate.opsForSet(), never()).remove(
                eq(Constants.REDIS_SCRIPT_TEMP_ATTACHMENT),
                any()
        );
    }

    @Test
    @DisplayName("测试getLevelStr方法 - 高级风险")
    void testGetLevelStr_HighRisk() throws Exception {
        // 准备测试数据
        ScriptInfoDto scriptDetail = new ScriptInfoDto();
        ScriptVersionDto versionDto = new ScriptVersionDto();
        versionDto.setLevel(Enums.ScriptLevel.HIGH_RISK.getValue());
        scriptDetail.setScriptVersionDto(versionDto);

        // 使用反射调用私有方法
        Method getLevelStrMethod = TaskApplyServiceImpl.class.getDeclaredMethod("getLevelStr", ScriptInfoDto.class);
        getLevelStrMethod.setAccessible(true);
        String result = (String) getLevelStrMethod.invoke(taskApplyServiceImplUnderTest, scriptDetail);

        // 验证结果
        assertEquals("高级风险", result);
    }

    @Test
    @DisplayName("测试getLevelStr方法 - 中级风险")
    void testGetLevelStr_MediumRisk() throws Exception {
        // 准备测试数据
        ScriptInfoDto scriptDetail = new ScriptInfoDto();
        ScriptVersionDto versionDto = new ScriptVersionDto();
        versionDto.setLevel(Enums.ScriptLevel.MEDIUM_RISK.getValue());
        scriptDetail.setScriptVersionDto(versionDto);

        // 使用反射调用私有方法
        Method getLevelStrMethod = TaskApplyServiceImpl.class.getDeclaredMethod("getLevelStr", ScriptInfoDto.class);
        getLevelStrMethod.setAccessible(true);
        String result = (String) getLevelStrMethod.invoke(taskApplyServiceImplUnderTest, scriptDetail);

        // 验证结果
        assertEquals("中级风险", result);
    }

    @Test
    @DisplayName("测试getLevelStr方法 - 低级风险")
    void testGetLevelStr_LowRisk() throws Exception {
        // 准备测试数据
        ScriptInfoDto scriptDetail = new ScriptInfoDto();
        ScriptVersionDto versionDto = new ScriptVersionDto();
        versionDto.setLevel(Enums.ScriptLevel.LOW_RISK.getValue());
        scriptDetail.setScriptVersionDto(versionDto);

        // 使用反射调用私有方法
        Method getLevelStrMethod = TaskApplyServiceImpl.class.getDeclaredMethod("getLevelStr", ScriptInfoDto.class);
        getLevelStrMethod.setAccessible(true);
        String result = (String) getLevelStrMethod.invoke(taskApplyServiceImplUnderTest, scriptDetail);

        // 验证结果
        assertEquals("低级风险", result);
    }

    @Test
    @DisplayName("测试getLevelStr方法 - 白名单")
    void testGetLevelStr_WhiteList() throws Exception {
        // 准备测试数据
        ScriptInfoDto scriptDetail = new ScriptInfoDto();
        ScriptVersionDto versionDto = new ScriptVersionDto();
        versionDto.setLevel(Enums.ScriptLevel.WHITE_SCRIPT.getValue());
        scriptDetail.setScriptVersionDto(versionDto);

        // 使用反射调用私有方法
        Method getLevelStrMethod = TaskApplyServiceImpl.class.getDeclaredMethod("getLevelStr", ScriptInfoDto.class);
        getLevelStrMethod.setAccessible(true);
        String result = (String) getLevelStrMethod.invoke(taskApplyServiceImplUnderTest, scriptDetail);

        // 验证结果
        assertEquals("", result);
    }

    @Test
    @DisplayName("测试getLevelStr方法 - 无效风险等级")
    void testGetLevelStr_InvalidLevel() throws Exception {
        // 准备测试数据
        ScriptInfoDto scriptDetail = new ScriptInfoDto();
        ScriptVersionDto versionDto = new ScriptVersionDto();
        versionDto.setLevel(999); // 无效的风险等级
        scriptDetail.setScriptVersionDto(versionDto);

        // 使用反射调用私有方法
        Method getLevelStrMethod = TaskApplyServiceImpl.class.getDeclaredMethod("getLevelStr", ScriptInfoDto.class);
        getLevelStrMethod.setAccessible(true);
        String result = (String) getLevelStrMethod.invoke(taskApplyServiceImplUnderTest, scriptDetail);

        // 验证结果
        assertEquals("", result);
    }

    @Test
    @DisplayName("测试getLevelStr方法 - 空值处理")
    void testGetLevelStr_NullLevel() throws Exception {
        // 准备测试数据
        ScriptInfoDto scriptDetail = new ScriptInfoDto();
        ScriptVersionDto versionDto = new ScriptVersionDto();
        versionDto.setLevel(null);
        scriptDetail.setScriptVersionDto(versionDto);

        // 使用反射调用私有方法
        Method getLevelStrMethod = TaskApplyServiceImpl.class.getDeclaredMethod("getLevelStr", ScriptInfoDto.class);
        getLevelStrMethod.setAccessible(true);

        // 验证抛出异常
        assertThrows(InvocationTargetException.class, () ->
                getLevelStrMethod.invoke(taskApplyServiceImplUnderTest, scriptDetail)
        );
    }

    @Test
    @DisplayName("测试queryPermissionUserInfoList方法 - 高覆盖率测试")
    void testQueryPermissionUserInfoList_all() throws Exception {
        // Setup - 创建测试数据
        ServicePermissionApiQueryDto servicePermissionApiQueryDto = new ServicePermissionApiQueryDto();
        servicePermissionApiQueryDto.setRoleIds(Arrays.asList(1L, 2L));
        servicePermissionApiQueryDto.setOrgManagementFullNames(Arrays.asList("张三", "李四"));
        servicePermissionApiQueryDto.setPermissionCodes(Arrays.asList("PERMISSION_CODE"));
        servicePermissionApiQueryDto.setUserLevel(1);
        servicePermissionApiQueryDto.setUserIds(Arrays.asList(1L, 2L));
        servicePermissionApiQueryDto.setOrgManagementIds(Arrays.asList(1L, 2L));
        
        Long categoryId = 1L;
        
        // Mock CurrentUser
        CurrentUser currentUser = new CurrentUser();
        currentUser.setId(1L);
        currentUser.setLoginName("testUser");
        currentUser.setSupervisor(false);
        currentUser.setOrgId(1L);
        currentUser.setOrgCode("ORG001");
        
        // Mock ScriptBusinessConfig
        ScriptBusinessConfig scriptBusinessConfig = new ScriptBusinessConfig();
        scriptBusinessConfig.setDataPermissionPolicy("userRoleGroup");

        // Mock MyScriptServiceScripts
        when(myScriptServiceScripts.getScriptBusinessConfig()).thenReturn(scriptBusinessConfig);
        when(myScriptServiceScripts.getCategoryMapper()).thenReturn(categoryMapper);
        when(myScriptServiceScripts.getiRole()).thenReturn(iRole);

        List<CategoryRoleBean> categoryRoleRelations = new ArrayList<>();
        CategoryRoleBean categoryRoleBean = new CategoryRoleBean();
        categoryRoleBean.setCategoryId(1L);
        categoryRoleBean.setRoleId(1L);
        categoryRoleBean.setId(1L);
        categoryRoleRelations.add(categoryRoleBean);

        when(categoryMapper.getCategoryRoleRelations(any())).thenReturn(categoryRoleRelations);


        List<UserBean> userBeanList = new ArrayList<>();
        UserBean userBean = new UserBean();
        userBean.setId(1L);
        userBean.setLoginName("loginName");
        userBean.setUserId(1L);
        userBean.setOrgCode("orgCode");
        userBeanList.add(userBean);

        when(categoryMapper.getCategoryUsertRelations(any())).thenReturn(userBeanList);

        List<UserInfoApiDto> userInfoList1 = new ArrayList<>();
        UserInfoApiDto userInfoApiDto = new UserInfoApiDto();
        userInfoApiDto.setId(1L);
        userInfoList1.add(userInfoApiDto);

        when(iUserInfoApi.getUserInfoList(any())).thenReturn(userInfoList1);
        
        // Mock CurrentUserUtil
        try (MockedStatic<CurrentUserUtil> mockedCurrentUserUtil = mockStatic(CurrentUserUtil.class)) {
            mockedCurrentUserUtil.when(CurrentUserUtil::getCurrentUser).thenReturn(currentUser);
            
            // 测试场景1：角色权限模式，有角色ID
            List<PermissionUserInfoApiDto> permissionUserList1 = new ArrayList<>();
            PermissionUserInfoApiDto permissionUser1 = new PermissionUserInfoApiDto();
            permissionUser1.setId(2L);
            permissionUser1.setLoginName("user2");
            permissionUserList1.add(permissionUser1);
            
            when(iUserInfoApi.queryPermissionUserInfoList(any(ServicePermissionApiQueryDto.class)))
                    .thenReturn(permissionUserList1);
            
            List<UserInfoApiDto> result1 = taskApplyServiceImplUnderTest.queryPermissionUserInfoList(servicePermissionApiQueryDto, categoryId);
            
            // 验证结果
            assertNotNull(result1);
            assertEquals(1, result1.size());
            assertEquals(2L, result1.get(0).getId());
        }
    }


    @Test
    @DisplayName("测试queryPermissionUserInfoList方法 - 高覆盖率测试")
    void testQueryPermissionUserInfoList_all_sec() throws Exception {
        // Setup - 创建测试数据
        ServicePermissionApiQueryDto servicePermissionApiQueryDto = new ServicePermissionApiQueryDto();
//        servicePermissionApiQueryDto.setRoleIds(Arrays.asList(1L, 2L));
        servicePermissionApiQueryDto.setOrgManagementFullNames(Arrays.asList("张三", "李四"));
        servicePermissionApiQueryDto.setPermissionCodes(Arrays.asList("PERMISSION_CODE"));
        servicePermissionApiQueryDto.setUserLevel(1);
        servicePermissionApiQueryDto.setUserIds(Arrays.asList(1L, 2L));
        servicePermissionApiQueryDto.setOrgManagementIds(Arrays.asList(1L, 2L));

        Long categoryId = 1L;

        // Mock CurrentUser
        CurrentUser currentUser = new CurrentUser();
        currentUser.setId(1L);
        currentUser.setLoginName("testUser");
        currentUser.setSupervisor(false);
        currentUser.setOrgId(1L);
        currentUser.setOrgCode("ORG001");

        // Mock ScriptBusinessConfig
        ScriptBusinessConfig scriptBusinessConfig = new ScriptBusinessConfig();
        scriptBusinessConfig.setDataPermissionPolicy("userRoleGroup");

        // Mock MyScriptServiceScripts
        when(myScriptServiceScripts.getScriptBusinessConfig()).thenReturn(scriptBusinessConfig);
        when(myScriptServiceScripts.getCategoryMapper()).thenReturn(categoryMapper);
        when(myScriptServiceScripts.getiRole()).thenReturn(iRole);

        List<CategoryRoleBean> categoryRoleRelations = new ArrayList<>();
        CategoryRoleBean categoryRoleBean = new CategoryRoleBean();
        categoryRoleBean.setCategoryId(1L);
        categoryRoleBean.setRoleId(1L);
        categoryRoleBean.setId(1L);
        categoryRoleRelations.add(categoryRoleBean);

        when(categoryMapper.getCategoryRoleRelations(any())).thenReturn(categoryRoleRelations);


        List<UserBean> userBeanList = new ArrayList<>();
        UserBean userBean = new UserBean();
        userBean.setId(1L);
        userBean.setLoginName("loginName");
        userBean.setUserId(1L);
        userBean.setOrgCode("orgCode");
        userBeanList.add(userBean);

        when(categoryMapper.getCategoryUsertRelations(any())).thenReturn(userBeanList);

        List<UserInfoApiDto> userInfoList1 = new ArrayList<>();
        UserInfoApiDto userInfoApiDto = new UserInfoApiDto();
        userInfoApiDto.setId(1L);
        userInfoList1.add(userInfoApiDto);

        when(iUserInfoApi.getUserInfoList(any())).thenReturn(userInfoList1);

        // Mock CurrentUserUtil
        try (MockedStatic<CurrentUserUtil> mockedCurrentUserUtil = mockStatic(CurrentUserUtil.class)) {
            mockedCurrentUserUtil.when(CurrentUserUtil::getCurrentUser).thenReturn(currentUser);

            // 测试场景1：角色权限模式，有角色ID
            List<PermissionUserInfoApiDto> permissionUserList1 = new ArrayList<>();
            PermissionUserInfoApiDto permissionUser1 = new PermissionUserInfoApiDto();
            permissionUser1.setId(2L);
            permissionUser1.setLoginName("user2");
            permissionUserList1.add(permissionUser1);

            when(iUserInfoApi.queryPermissionUserInfoList(any(ServicePermissionApiQueryDto.class)))
                    .thenReturn(permissionUserList1);

            List<UserInfoApiDto> result1 = taskApplyServiceImplUnderTest.queryPermissionUserInfoList(servicePermissionApiQueryDto, categoryId);

            // 验证结果
            assertNotNull(result1);
            assertEquals(0, result1.size());
        }
    }

    @Test
    @DisplayName("测试queryPermissionUserInfoList方法 - 高覆盖率测试")
    void testQueryPermissionUserInfoList_all_userBeanList_null() throws Exception {
        // Setup - 创建测试数据
        ServicePermissionApiQueryDto servicePermissionApiQueryDto = new ServicePermissionApiQueryDto();
//        servicePermissionApiQueryDto.setRoleIds(Arrays.asList(1L, 2L));
        servicePermissionApiQueryDto.setOrgManagementFullNames(Arrays.asList("张三", "李四"));
        servicePermissionApiQueryDto.setPermissionCodes(Arrays.asList("PERMISSION_CODE"));
        servicePermissionApiQueryDto.setUserLevel(1);
        servicePermissionApiQueryDto.setUserIds(Arrays.asList(1L, 2L));
        servicePermissionApiQueryDto.setOrgManagementIds(Arrays.asList(1L, 2L));

        Long categoryId = 1L;

        // Mock CurrentUser
        CurrentUser currentUser = new CurrentUser();
        currentUser.setId(1L);
        currentUser.setLoginName("testUser");
        currentUser.setSupervisor(false);
        currentUser.setOrgId(1L);
        currentUser.setOrgCode("ORG001");

        // Mock ScriptBusinessConfig
        ScriptBusinessConfig scriptBusinessConfig = new ScriptBusinessConfig();
        scriptBusinessConfig.setDataPermissionPolicy("userRoleGroup");

        // Mock MyScriptServiceScripts
        when(myScriptServiceScripts.getScriptBusinessConfig()).thenReturn(scriptBusinessConfig);
        when(myScriptServiceScripts.getCategoryMapper()).thenReturn(categoryMapper);
        when(myScriptServiceScripts.getiRole()).thenReturn(iRole);

        List<CategoryRoleBean> categoryRoleRelations = new ArrayList<>();
        CategoryRoleBean categoryRoleBean = new CategoryRoleBean();
        categoryRoleBean.setCategoryId(1L);
        categoryRoleBean.setRoleId(1L);
        categoryRoleBean.setId(1L);
        categoryRoleRelations.add(categoryRoleBean);

        when(categoryMapper.getCategoryRoleRelations(any())).thenReturn(categoryRoleRelations);


        List<UserBean> userBeanList = new ArrayList<>();

        when(categoryMapper.getCategoryUsertRelations(any())).thenReturn(userBeanList);

        List<UserInfoApiDto> userInfoList1 = new ArrayList<>();
        UserInfoApiDto userInfoApiDto = new UserInfoApiDto();
        userInfoApiDto.setId(1L);
        userInfoList1.add(userInfoApiDto);

        when(iUserInfoApi.getUserInfoList(any())).thenReturn(userInfoList1);

        // Mock CurrentUserUtil
        try (MockedStatic<CurrentUserUtil> mockedCurrentUserUtil = mockStatic(CurrentUserUtil.class)) {
            mockedCurrentUserUtil.when(CurrentUserUtil::getCurrentUser).thenReturn(currentUser);

            // 测试场景1：角色权限模式，有角色ID
            List<PermissionUserInfoApiDto> permissionUserList1 = new ArrayList<>();
            PermissionUserInfoApiDto permissionUser1 = new PermissionUserInfoApiDto();
            permissionUser1.setId(2L);
            permissionUser1.setLoginName("user2");
            permissionUserList1.add(permissionUser1);

            when(iUserInfoApi.queryPermissionUserInfoList(any(ServicePermissionApiQueryDto.class)))
                    .thenReturn(permissionUserList1);

            List<UserInfoApiDto> result1 = taskApplyServiceImplUnderTest.queryPermissionUserInfoList(servicePermissionApiQueryDto, categoryId);

            // 验证结果
            assertNotNull(result1);
            assertEquals(0, result1.size());
        }
    }

    @Test
    @DisplayName("获取用户可执行的agent")
    void testGetLoginUserAgent() {
        // Mock CurrentUser
        CurrentUser mockUser = new CurrentUser();
        mockUser.setId(1L);
        try (MockedStatic<CurrentUserUtil> mockedStatic = mockStatic(CurrentUserUtil.class)) {
            mockedStatic.when(CurrentUserUtil::getCurrentUser).thenReturn(mockUser);

            // Mock AgentGroupRoleQueryBean
            AgentGroupRoleQueryBean queryBean = new AgentGroupRoleQueryBean();
            queryBean.setUserId(1L);
            queryBean.setPageNum(1);
            queryBean.setPageSize(50000);
            queryBean.setBusinessType("脚本服务化");

            // Mock PageInfo - 第一次调用返回有下一页，第二次调用返回没有下一页
            PageInfo<SystemAgentInfoApiDto> pageInfo1 = new PageInfo<>();
            pageInfo1.setHasNextPage(true);
            List<SystemAgentInfoApiDto> agentList1 = new ArrayList<>();
            SystemAgentInfoApiDto agent1 = new SystemAgentInfoApiDto();
            agent1.setAgentIp("***********");
            agent1.setAgentState(0);
            agentList1.add(agent1);
            pageInfo1.setList(agentList1);

            PageInfo<SystemAgentInfoApiDto> pageInfo2 = new PageInfo<>();
            pageInfo2.setHasNextPage(false);
            List<SystemAgentInfoApiDto> agentList2 = new ArrayList<>();
            SystemAgentInfoApiDto agent2 = new SystemAgentInfoApiDto();
            agent2.setAgentIp("***********");
            agent2.setAgentState(0);
            agentList2.add(agent2);
            pageInfo2.setList(agentList2);

            // 使用链式调用模拟分页行为
            when(iAgentInfo.queryAgentInfoGroupRole(any(AgentGroupRoleQueryBean.class)))
                    .thenReturn(pageInfo1)
                    .thenReturn(pageInfo2);

            List<SystemAgentInfoApiDto> result = taskApplyServiceImplUnderTest.getLoginUserAgent(mockUser);

            // 验证结果
            assertNotNull(result);
            assertEquals(2, result.size());
            assertEquals("***********", result.get(0).getAgentIp());
            assertEquals("***********", result.get(1).getAgentIp());

            // 验证方法被调用了两次
            verify(iAgentInfo, times(2)).queryAgentInfoGroupRole(any(AgentGroupRoleQueryBean.class));
        }
    }

}
