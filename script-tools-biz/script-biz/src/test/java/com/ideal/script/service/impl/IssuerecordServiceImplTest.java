package com.ideal.script.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import com.ideal.agent.gateway.api.AgentOperateApi;
import com.ideal.agent.gateway.model.AgentOperateDto;
import com.ideal.agent.gateway.model.AgentSyncOperateResultDto;
import com.ideal.script.common.util.BatchDataUtil;
import com.ideal.script.dto.*;
import com.ideal.script.exception.ScriptException;
import com.ideal.script.mapper.IssuerecordMapper;
import com.ideal.script.model.dto.AgentInfoDto;
import com.ideal.system.common.component.model.CurrentUser;
import com.ideal.script.model.bean.IssuerecordBean;
import com.ideal.script.model.dto.*;
import com.ideal.script.model.entity.Category;
import com.ideal.script.model.entity.IssuerecordEntity;
import com.ideal.script.service.*;
import org.apache.ibatis.session.ExecutorType;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.sql.Timestamp;
import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class IssuerecordServiceImplTest {

    @Mock
    private IssuerecordMapper mockIssuerecordMapper;
    @Mock
    private SqlSessionFactory mockFactory;
    @Mock
    private BatchDataUtil mockBatchDataUtil;
    @Mock
    private IInfoVersionService mockInfoVersionService;
    @Mock
    private IInfoService mockInfoService;
    @Mock
    private IInfoVersionTextService mockInfoVersionTextService;
    @Mock
    private AgentOperateApi mockAgentOperateApi;
    @Mock
    private IAttachmentService mockAttachmentService;
    @Mock
    private ITaskExecuteService mockTaskExecuteService;

    private IssuerecordServiceImpl issuerecordServiceImplUnderTest;

    @BeforeEach
    void setUp() throws Exception {
        issuerecordServiceImplUnderTest = new IssuerecordServiceImpl(mockIssuerecordMapper, mockFactory,
                mockBatchDataUtil, mockInfoVersionService, mockInfoService, mockInfoVersionTextService,
                mockAgentOperateApi, mockAttachmentService, mockTaskExecuteService);
    }

    @Test
    void testSelectIssuerecordById() {
        // Setup
        // Configure IssuerecordMapper.selectIssuerecordById(...).
        final IssuerecordEntity issuerecordEntity = new IssuerecordEntity();
        issuerecordEntity.setBizId("bizId");
        issuerecordEntity.setBatchNumber("batchNumber");
        issuerecordEntity.setId(0L);
        issuerecordEntity.setSrcScriptUuid("srcScriptUuid");
        issuerecordEntity.setAgentIp("agentIp");
        when(mockIssuerecordMapper.selectIssuerecordById(0L)).thenReturn(issuerecordEntity);

        // Run the test
        final IssuerecordDto result = issuerecordServiceImplUnderTest.selectIssuerecordById(0L);

        // Verify the results
        assertNotNull(result);
    }

    @Test
    void testSelectIssuerecordList() {
        // Setup
        final IssuerecordQueryDto issuerecordQueryDto = new IssuerecordQueryDto();
        issuerecordQueryDto.setSrcScriptUuids(new String[]{"srcScriptUuids"});
        final AgentInfoDto agentInfoDto = new AgentInfoDto();
        agentInfoDto.setSysmAgentInfoId(0L);
        agentInfoDto.setAgentIp("agentIp");
        agentInfoDto.setAgentPort(0);
        agentInfoDto.setOsName("osName");
        issuerecordQueryDto.setAgentInfoDtoList(Collections.singletonList(agentInfoDto));
        issuerecordQueryDto.setSendPath("sendPath");
        issuerecordQueryDto.setChmod("chmod");
        issuerecordQueryDto.setUserPermission("userPermission");
        issuerecordQueryDto.setGroupPermission("groupPermission");
        Timestamp timestamp = new Timestamp(System.currentTimeMillis());
        issuerecordQueryDto.setSendTime(timestamp);

        // Configure IssuerecordMapper.selectIssuerecordList(...).
        final IssuerecordEntity issuerecordEntity = new IssuerecordEntity();
        issuerecordEntity.setBizId("bizId");
        issuerecordEntity.setBatchNumber("batchNumber");
        issuerecordEntity.setId(0L);
        issuerecordEntity.setSrcScriptUuid("srcScriptUuid");
        issuerecordEntity.setAgentIp("agentIp");
        Page<IssuerecordEntity> page = new Page<>();
        page.add(issuerecordEntity);
        //final List<IssuerecordEntity> issuerecordEntities = Arrays.asList(issuerecordEntity);
        when(mockIssuerecordMapper.selectIssuerecordList(any(IssuerecordBean.class))).thenReturn(page);

        // Run the test
        final PageInfo<IssuerecordDto> result = issuerecordServiceImplUnderTest.selectIssuerecordList(
                issuerecordQueryDto, 0, 0);

        assertNotNull(result);
        // Verify the results
    }

    @Test
    void testInsertIssuerecord() {
        // Setup
        final IssuerecordDto issuerecordDto = new IssuerecordDto();
        issuerecordDto.setBizId("bizId");
        issuerecordDto.setBatchNumber("batchNumber");
        issuerecordDto.setSrcScriptUuid("srcScriptUuid");
        issuerecordDto.setAgentIp("agentIp");
        issuerecordDto.setAgentPort(0);
        issuerecordDto.setStatus(0);
        issuerecordDto.setSendPath("sendPath");
        issuerecordDto.setChmod("chmod");
        issuerecordDto.setUserPermission("userPermission");
        issuerecordDto.setGroupPermission("groupPermission");
        issuerecordDto.setSendUserId(0L);
        issuerecordDto.setSendUserName("loginName");

        when(mockIssuerecordMapper.insertIssuerecord(any(IssuerecordEntity.class))).thenReturn(0);

        // Run the test
        final int result = issuerecordServiceImplUnderTest.insertIssuerecord(issuerecordDto);

        // Verify the results
        assertThat(result).isZero();
    }

    @Test
    void testUpdateIssuerecord() {
        // Setup
        final IssuerecordDto issuerecordDto = new IssuerecordDto();
        issuerecordDto.setBizId("bizId");
        issuerecordDto.setBatchNumber("batchNumber");
        issuerecordDto.setSrcScriptUuid("srcScriptUuid");
        issuerecordDto.setAgentIp("agentIp");
        issuerecordDto.setAgentPort(0);
        issuerecordDto.setStatus(0);
        issuerecordDto.setSendPath("sendPath");
        issuerecordDto.setChmod("chmod");
        issuerecordDto.setUserPermission("userPermission");
        issuerecordDto.setGroupPermission("groupPermission");
        issuerecordDto.setSendUserId(0L);
        issuerecordDto.setSendUserName("loginName");

        when(mockIssuerecordMapper.updateIssuerecord(any(IssuerecordEntity.class))).thenReturn(0);

        // Run the test
        final int result = issuerecordServiceImplUnderTest.updateIssuerecord(issuerecordDto);

        // Verify the results
        assertThat(result).isZero();
    }

    @Test
    void testUpdateIssuerecordByBizId() {
        // Setup
        final IssuerecordDto issuerecordDto = new IssuerecordDto();
        issuerecordDto.setBizId("bizId");
        issuerecordDto.setBatchNumber("batchNumber");
        issuerecordDto.setSrcScriptUuid("srcScriptUuid");
        issuerecordDto.setAgentIp("agentIp");
        issuerecordDto.setAgentPort(0);
        issuerecordDto.setStatus(0);
        issuerecordDto.setSendPath("sendPath");
        issuerecordDto.setChmod("chmod");
        issuerecordDto.setUserPermission("userPermission");
        issuerecordDto.setGroupPermission("groupPermission");
        issuerecordDto.setSendUserId(0L);
        issuerecordDto.setSendUserName("loginName");

        when(mockIssuerecordMapper.updateIssuerecordByBatchNumber(any(IssuerecordEntity.class))).thenReturn(0);

        // Run the test
        final int result = issuerecordServiceImplUnderTest.updateIssuerecordByBatchNumber(issuerecordDto);

        // Verify the results
        assertThat(result).isZero();
    }

    @Test
    void testDeleteIssuerecordByIds() {
        // Setup
        when(mockIssuerecordMapper.deleteIssuerecordByIds(any(Long[].class))).thenReturn(0);

        // Run the test
        final int result = issuerecordServiceImplUnderTest.deleteIssuerecordByIds(new Long[]{0L});

        // Verify the results
        assertThat(result).isZero();
    }

    @ParameterizedTest
    @CsvSource({
            "true, Linux",
            "true, ''"
    })
    void testSendScriptToAgents(boolean flag,String osName) throws Exception {
        if(Objects.equals(osName, "")){
            osName = null;
        }
        // Setup
        final IssuerecordQueryDto issuerecordQueryDto = new IssuerecordQueryDto();
        issuerecordQueryDto.setSrcScriptUuids(new String[]{"srcScriptUuids"});
        final AgentInfoDto agentInfoDto = new AgentInfoDto();
        agentInfoDto.setSysmAgentInfoId(0L);
        agentInfoDto.setAgentIp("agentIp");
        agentInfoDto.setAgentPort(0);
        agentInfoDto.setOsName(osName);
        issuerecordQueryDto.setAgentInfoDtoList(Collections.singletonList(agentInfoDto));
        issuerecordQueryDto.setSendPath("sendPath");
        issuerecordQueryDto.setChmod("chmod");
        issuerecordQueryDto.setUserPermission("");
        issuerecordQueryDto.setGroupPermission("groupPermission");

        final CurrentUser currentUser = new CurrentUser();
        currentUser.setId(0L);
        currentUser.setLoginName("loginName");
        currentUser.setOrgCode("orgCode");

        // Configure SqlSessionFactory.openSession(...).
        final SqlSession mockSqlSession = mock(SqlSession.class);
        when(mockFactory.openSession(ExecutorType.BATCH, false)).thenReturn(mockSqlSession);

        // Configure IInfoVersionService.selectInfoVersionBySrcScriptUuid(...).
        final ScriptVersionDto infoVersionDto = new ScriptVersionDto();
        infoVersionDto.setId(0L);
        infoVersionDto.setInfoUniqueUuid("infoUniqueUuid");
        infoVersionDto.setSrcScriptUuid("srcScriptUuid");
        infoVersionDto.setVersion("version");
        infoVersionDto.setEditState(0);
        when(mockInfoVersionService.selectInfoVersionBySrcScriptUuid(any(String.class))).thenReturn(infoVersionDto);

        // Configure IInfoVersionTextService.selectInfoVersionTextByScriptUuid(...).
        final InfoVersionTextDto infoVersionTextDto = new InfoVersionTextDto();
        infoVersionTextDto.setId(0L);
        infoVersionTextDto.setSrcScriptUuid("srcScriptUuid");
        infoVersionTextDto.setContent("content");
        infoVersionTextDto.setCreatorId(0L);
        infoVersionTextDto.setCreatorName("creatorName");
        when(mockInfoVersionTextService.selectInfoVersionTextByScriptUuid(any(String.class)))
                .thenReturn(infoVersionTextDto);

        // Configure IInfoService.selectInfoByUniqueUuid(...).
        final ScriptInfoDto infoDto = new ScriptInfoDto();
        final Category category = new Category();
        final CategoryApiDto categoryApiDto = new CategoryApiDto();
        categoryApiDto.setChildren(Collections.singletonList(new CategoryApiDto()));
        categoryApiDto.setId(0L);
        category.setChildren(Collections.singletonList(categoryApiDto));
        infoDto.setCategoryDto(new CategoryDto());
        infoDto.setScriptNameZh("scriptNameZh");
        when(mockInfoService.selectInfoByUniqueUuid("infoUniqueUuid")).thenReturn(infoDto);

        when(mockInfoVersionService.getScriptTypeBySrcScriptUuid(any(String.class))).thenReturn("result");

        // Configure IAttachmentService.getAttachmentByUuid(...).
        final AttachmentDto attachmentDto = new AttachmentDto();
        attachmentDto.setIsTempFlag(0);
        attachmentDto.setId(0L);
        attachmentDto.setSrcScriptUuid("srcScriptUuid");
        attachmentDto.setName("name");
        attachmentDto.setContents("content".getBytes());
        final List<AttachmentDto> attachmentDtos = Collections.singletonList(attachmentDto);
        when(mockAttachmentService.getAttachmentByUuid(any(String.class))).thenReturn(attachmentDtos);

        when(mockTaskExecuteService.convertObjectToJsonString(any(List.class))).thenReturn("content");

        // Configure AgentOperateApi.sendSync(...).
        final AgentSyncOperateResultDto agentSyncOperateResultDto = new AgentSyncOperateResultDto();
        agentSyncOperateResultDto.setTaskId(0L);
        agentSyncOperateResultDto.setBizId("bizId");
        agentSyncOperateResultDto.setContent(flag);
        agentSyncOperateResultDto.setAgentHost("agentHost");
        agentSyncOperateResultDto.setAgentPort(0L);
        when(mockAgentOperateApi.sendSync(any(AgentOperateDto.class))).thenReturn(agentSyncOperateResultDto);

        //when(mockIssuerecordMapper.updateIssuerecordByBizId(any(IssuerecordEntity.class))).thenReturn(1);

        // Run the test
        final String result = issuerecordServiceImplUnderTest.sendScriptToAgents(issuerecordQueryDto, currentUser);

        // Verify the results
        assertNotNull(result);
        verify(mockSqlSession).close();

        // Confirm BatchDataUtil.batchData(...).
        final IssuerecordEntity issuerecordEntity = new IssuerecordEntity();
        issuerecordEntity.setBizId("bizId");
        issuerecordEntity.setBatchNumber("batchNumber");
        issuerecordEntity.setId(0L);
        issuerecordEntity.setSrcScriptUuid("srcScriptUuid");
        issuerecordEntity.setAgentIp("agentIp");
        final List<IssuerecordEntity> listEntity = Collections.singletonList(issuerecordEntity);
        assertNotNull(listEntity);
    }

    @ParameterizedTest
    @CsvSource({
            "true"
    })
    void testSendScriptToAgents_noSrcScriptUuids(boolean flag) throws Exception {
        // Setup
        final IssuerecordQueryDto issuerecordQueryDto = new IssuerecordQueryDto();
        final AgentInfoDto agentInfoDto = new AgentInfoDto();
        agentInfoDto.setSysmAgentInfoId(0L);
        agentInfoDto.setAgentIp("agentIp");
        agentInfoDto.setAgentPort(0);
        agentInfoDto.setOsName("AIX");
        issuerecordQueryDto.setAgentInfoDtoList(Collections.singletonList(agentInfoDto));
        issuerecordQueryDto.setSendPath("sendPath");
        issuerecordQueryDto.setChmod("chmod");
        issuerecordQueryDto.setUserPermission("");
        issuerecordQueryDto.setGroupPermission("groupPermission");

        final CurrentUser currentUser = new CurrentUser();
        currentUser.setId(0L);
        currentUser.setLoginName("loginName");
        currentUser.setOrgCode("orgCode");


        when(mockTaskExecuteService.convertObjectToJsonString(any(List.class))).thenReturn("content");

        // Configure AgentOperateApi.sendSync(...).
        final AgentSyncOperateResultDto agentSyncOperateResultDto = new AgentSyncOperateResultDto();
        agentSyncOperateResultDto.setTaskId(0L);
        agentSyncOperateResultDto.setBizId("bizId");
        agentSyncOperateResultDto.setContent(flag);
        agentSyncOperateResultDto.setAgentHost("agentHost");
        agentSyncOperateResultDto.setAgentPort(0L);
        when(mockAgentOperateApi.sendSync(any(AgentOperateDto.class))).thenReturn(agentSyncOperateResultDto);

        // Run the test
        final String result = issuerecordServiceImplUnderTest.sendScriptToAgents(issuerecordQueryDto, currentUser);

        // Verify the results
        assertNotNull(result);

        // Confirm BatchDataUtil.batchData(...).
        final IssuerecordEntity issuerecordEntity = new IssuerecordEntity();
        issuerecordEntity.setBizId("bizId");
        issuerecordEntity.setBatchNumber("batchNumber");
        issuerecordEntity.setId(0L);
        issuerecordEntity.setSrcScriptUuid("srcScriptUuid");
        issuerecordEntity.setAgentIp("agentIp");
        final List<IssuerecordEntity> listEntity = Collections.singletonList(issuerecordEntity);
        assertNotNull(listEntity);
    }

    @ParameterizedTest
    @CsvSource({
            "true"
    })
    void testSendScriptToAgents_noAttachmentDtoResult(boolean flag) throws Exception {
        // Setup
        final IssuerecordQueryDto issuerecordQueryDto = new IssuerecordQueryDto();
        issuerecordQueryDto.setSrcScriptUuids(new String[]{"srcScriptUuids"});
        final AgentInfoDto agentInfoDto = new AgentInfoDto();
        agentInfoDto.setSysmAgentInfoId(0L);
        agentInfoDto.setAgentIp("agentIp");
        agentInfoDto.setAgentPort(0);
        agentInfoDto.setOsName("AIX");
        issuerecordQueryDto.setAgentInfoDtoList(Collections.singletonList(agentInfoDto));
        issuerecordQueryDto.setSendPath("sendPath");
        issuerecordQueryDto.setChmod("chmod");
        issuerecordQueryDto.setUserPermission("");
        issuerecordQueryDto.setGroupPermission("groupPermission");

        final CurrentUser currentUser = new CurrentUser();
        currentUser.setId(0L);
        currentUser.setLoginName("loginName");
        currentUser.setOrgCode("orgCode");

        // Configure SqlSessionFactory.openSession(...).
        final SqlSession mockSqlSession = mock(SqlSession.class);
        when(mockFactory.openSession(ExecutorType.BATCH, false)).thenReturn(mockSqlSession);

        // Configure IInfoVersionService.selectInfoVersionBySrcScriptUuid(...).
        final ScriptVersionDto infoVersionDto = new ScriptVersionDto();
        infoVersionDto.setId(0L);
        infoVersionDto.setInfoUniqueUuid("infoUniqueUuid");
        infoVersionDto.setSrcScriptUuid("srcScriptUuid");
        infoVersionDto.setVersion("version");
        infoVersionDto.setEditState(0);
        when(mockInfoVersionService.selectInfoVersionBySrcScriptUuid(any(String.class))).thenReturn(infoVersionDto);

        // Configure IInfoVersionTextService.selectInfoVersionTextByScriptUuid(...).
        final InfoVersionTextDto infoVersionTextDto = new InfoVersionTextDto();
        infoVersionTextDto.setId(0L);
        infoVersionTextDto.setSrcScriptUuid("srcScriptUuid");
        infoVersionTextDto.setContent("content");
        infoVersionTextDto.setCreatorId(0L);
        infoVersionTextDto.setCreatorName("creatorName");
        when(mockInfoVersionTextService.selectInfoVersionTextByScriptUuid(any(String.class)))
                .thenReturn(infoVersionTextDto);

        // Configure IInfoService.selectInfoByUniqueUuid(...).
        final ScriptInfoDto infoDto = new ScriptInfoDto();
        final Category category = new Category();
        final CategoryApiDto categoryApiDto = new CategoryApiDto();
        categoryApiDto.setChildren(Collections.singletonList(new CategoryApiDto()));
        categoryApiDto.setId(0L);
        category.setChildren(Collections.singletonList(categoryApiDto));
        infoDto.setCategoryDto(new CategoryDto());
        infoDto.setScriptNameZh("scriptNameZh");
        when(mockInfoService.selectInfoByUniqueUuid("infoUniqueUuid")).thenReturn(infoDto);

        when(mockInfoVersionService.getScriptTypeBySrcScriptUuid(any(String.class))).thenReturn("result");


        final List<AttachmentDto> attachmentDtos = new ArrayList<>();
        when(mockAttachmentService.getAttachmentByUuid(any(String.class))).thenReturn(attachmentDtos);

        when(mockTaskExecuteService.convertObjectToJsonString(any(List.class))).thenReturn("content");

        // Configure AgentOperateApi.sendSync(...).
        final AgentSyncOperateResultDto agentSyncOperateResultDto = new AgentSyncOperateResultDto();
        agentSyncOperateResultDto.setTaskId(0L);
        agentSyncOperateResultDto.setBizId("bizId");
        agentSyncOperateResultDto.setContent(flag);
        agentSyncOperateResultDto.setAgentHost("agentHost");
        agentSyncOperateResultDto.setAgentPort(0L);
        when(mockAgentOperateApi.sendSync(any(AgentOperateDto.class))).thenReturn(agentSyncOperateResultDto);

        // Run the test
        final String result = issuerecordServiceImplUnderTest.sendScriptToAgents(issuerecordQueryDto, currentUser);

        // Verify the results
        assertNotNull(result);
        verify(mockSqlSession).close();

        // Confirm BatchDataUtil.batchData(...).
        final IssuerecordEntity issuerecordEntity = new IssuerecordEntity();
        issuerecordEntity.setBizId("bizId");
        issuerecordEntity.setBatchNumber("batchNumber");
        issuerecordEntity.setId(0L);
        issuerecordEntity.setSrcScriptUuid("srcScriptUuid");
        issuerecordEntity.setAgentIp("agentIp");
        final List<IssuerecordEntity> listEntity = Collections.singletonList(issuerecordEntity);
        assertNotNull(listEntity);
    }

    @Test
    void testSendScriptToAgents_batchData_exception() throws Exception {
        // Setup
        final IssuerecordQueryDto issuerecordQueryDto = new IssuerecordQueryDto();
        issuerecordQueryDto.setSrcScriptUuids(new String[]{"srcScriptUuids"});
        final AgentInfoDto agentInfoDto = new AgentInfoDto();
        agentInfoDto.setSysmAgentInfoId(0L);
        agentInfoDto.setAgentIp("agentIp");
        agentInfoDto.setAgentPort(0);
        agentInfoDto.setOsName("AIX");
        issuerecordQueryDto.setAgentInfoDtoList(Collections.singletonList(agentInfoDto));
        issuerecordQueryDto.setSendPath("sendPath");
        issuerecordQueryDto.setChmod("chmod");
        issuerecordQueryDto.setUserPermission("");
        issuerecordQueryDto.setGroupPermission("groupPermission");

        final CurrentUser currentUser = new CurrentUser();
        currentUser.setId(0L);
        currentUser.setLoginName("loginName");
        currentUser.setOrgCode("orgCode");

        // Configure SqlSessionFactory.openSession(...).
        final SqlSession mockSqlSession = mock(SqlSession.class);
        when(mockFactory.openSession(ExecutorType.BATCH, false)).thenReturn(mockSqlSession);

        //when(mockIssuerecordMapper.updateIssuerecordByBizId(any(IssuerecordEntity.class))).thenReturn(1);

        doThrow(new ScriptException("111")).when(mockBatchDataUtil)
                .batchData(any(Class.class), anyList(), any(Class.class), anyString(), any(SqlSession.class), any());
        // Run the test
        assertThrows(ScriptException.class,()->{
            issuerecordServiceImplUnderTest.sendScriptToAgents(issuerecordQueryDto, currentUser);
        });

        // Verify the results
        verify(mockSqlSession).close();

        // Confirm BatchDataUtil.batchData(...).
        final IssuerecordEntity issuerecordEntity = new IssuerecordEntity();
        issuerecordEntity.setBizId("bizId");
        issuerecordEntity.setBatchNumber("batchNumber");
        issuerecordEntity.setId(0L);
        issuerecordEntity.setSrcScriptUuid("srcScriptUuid");
        issuerecordEntity.setAgentIp("agentIp");
        final List<IssuerecordEntity> listEntity = Collections.singletonList(issuerecordEntity);
        assertNotNull(listEntity);
    }




}
