package com.ideal.script.mapper;

import com.ideal.script.model.entity.ScriptReferrerInfo;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface ScriptReferrerInfoMapper {
    /**
     * 查询脚本引用情况分页数据
     * @param scriptReferrerInfo 查询条件
     */
    List<ScriptReferrerInfo> selectPageList(ScriptReferrerInfo scriptReferrerInfo);

    /**
     * 根据引用对象id删除数据
     * @param scriptReferrerInfo 数据对象
     */
    int deleteByReferrerBizId(ScriptReferrerInfo scriptReferrerInfo);

    /**
     * 新增引用脚本数据
     * @param scriptReferrerInfo 数据对象
     */
    int insertReferrerInfo(ScriptReferrerInfo scriptReferrerInfo);
}
