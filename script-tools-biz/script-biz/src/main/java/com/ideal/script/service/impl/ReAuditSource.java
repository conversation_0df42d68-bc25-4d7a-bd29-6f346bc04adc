package com.ideal.script.service.impl;

import com.ideal.script.common.constant.Constants;
import com.ideal.script.common.constant.enums.AuditStateEnum;
import com.ideal.script.common.constant.enums.Enums;
import com.ideal.script.common.util.CronDateUtils;
import com.ideal.script.exception.ScriptException;
import com.ideal.script.model.dto.AuditRelationDto;
import com.ideal.script.model.dto.ScriptExecAuditDto;
import com.ideal.script.model.dto.TaskDto;
import com.ideal.script.service.*;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.util.List;
import java.util.Objects;

/**
 * 重新审核
 */
@Service("reAuditSource")
public class ReAuditSource implements AuditSource {
    private final IAuditRelationService auditRelationService;
    private final ITaskIpsService taskIpsService;
    private final ITaskGroupsService taskGroupsService;
    private final ITaskParamsService taskParamsService;
    private final ITaskAttachmentService taskAttachmentService;
    private final RedisTemplate<String, Object> redisTemplate;
    private final ITaskService iTaskService;

    public ReAuditSource(IAuditRelationService auditRelationService, ITaskIpsService taskIpsService, ITaskGroupsService taskGroupsService, ITaskParamsService taskParamsService, ITaskAttachmentService taskAttachmentService, RedisTemplate<String, Object> redisTemplate, ITaskService iTaskService) {
        this.auditRelationService = auditRelationService;
        this.taskIpsService = taskIpsService;
        this.taskGroupsService = taskGroupsService;
        this.taskParamsService = taskParamsService;
        this.taskAttachmentService = taskAttachmentService;
        this.redisTemplate = redisTemplate;
        this.iTaskService = iTaskService;
    }

    @Override
    public void preHandle(ScriptExecAuditDto scriptExecAuditDto) {
        // 更新agent、agent组绑定
        taskIpsService.deleteTaskIpsByTaskId(scriptExecAuditDto.getTaskInfo().getId());
        taskGroupsService.deleteTaskGroupsByTaskId(scriptExecAuditDto.getTaskInfo().getId());

        // 更新参数
        taskParamsService.deleteTaskParamsByTaskId(scriptExecAuditDto.getTaskInfo().getId());
    }

    @Override
    public Long getRelationId(ScriptExecAuditDto scriptExecAuditDto, TaskDto taskInfo, String srcScriptUuid) throws ScriptException {
        // 更新审核表审核人
        AuditRelationDto auditRelationDto = new AuditRelationDto();
        auditRelationDto.setAuditUserId(scriptExecAuditDto.getAuditUserId());
        auditRelationDto.setAuditUser(scriptExecAuditDto.getAuditUser());
        auditRelationDto.setState(AuditStateEnum.PENDING.getType());
        AuditRelationDto auditRelationDto1 = auditRelationService.selectAuditRelationByTaskId(scriptExecAuditDto.getTaskInfo().getId());
        auditRelationDto.setId(auditRelationDto1.getId());
        auditRelationService.updateAuditRelation(auditRelationDto);
        return auditRelationDto1.getId();
    }

    @Override
    public void bindTaskId(ScriptExecAuditDto scriptExecAuditDto) {
        // taskId 绑定
        for (int i = 0; i < scriptExecAuditDto.getChosedAgentUsers().size(); i++) {
            scriptExecAuditDto.getChosedAgentUsers().get(i).setTaskIpsId(scriptExecAuditDto.getTaskInfo().getId());
        }
        for (int i = 0; i < scriptExecAuditDto.getChosedResGroups().size(); i++) {
            scriptExecAuditDto.getChosedResGroups().get(i).setScriptTaskId(scriptExecAuditDto.getTaskInfo().getId());
        }
    }

    @Override
    public boolean isInWhiteList(ScriptExecAuditDto scriptExecAuditDto) {
        return false;
    }

    @Override
    public void saveOrUpdateTask(ScriptExecAuditDto scriptExecAuditDto, TaskDto taskDto1) throws ScriptException {
        if (Objects.equals(scriptExecAuditDto.getTaskInfo().getTaskScheduler(), Enums.TaskScheduler.PERIODIC.getValue())) {
            // 周期执行
            taskDto1.setTaskTime(null);
        }

        if (Objects.equals(taskDto1.getTaskScheduler(), Enums.TaskScheduler.TIMED.getValue())) {
            // 定时执行
            if (taskDto1.getTaskTime().before(new Timestamp(System.currentTimeMillis()))) {
                throw new ScriptException("execTime.before.now");
            }
            taskDto1.setTaskCron(CronDateUtils.getCron(scriptExecAuditDto.getTaskInfo().getTaskTime()));
        }

        if (Objects.equals(taskDto1.getTaskScheduler(), Enums.TaskScheduler.TRIGGER.getValue())) {
            taskDto1.setTaskCron(null);
            taskDto1.setTaskTime(null);
        }

        iTaskService.updateTask(taskDto1);
    }

    @Override
    public void preHandleAttachment(Long taskId) {
        List<Long> ids = taskAttachmentService.getIdsByTaskId(taskId);
        if (!ids.isEmpty()) {
            redisTemplate.opsForSet().add(Constants.REDIS_SCRIPT_TEMP_ATTACHMENT, ids.toArray(new Long[0]));
            taskAttachmentService.updateTaskIdEmptyByTaskId(taskId);
        }
    }
}
