package com.ideal.script.mapper;

import com.ideal.script.model.entity.ScriptTemplate;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface ScriptTemplateMapper {
    /**
     * 查询【脚本模版】列表
     *
     * @param scriptTemplate 【脚本模版实体】
     * @return 【脚本模版】集合
     */
    List<ScriptTemplate> selectScriptTemplateList(ScriptTemplate scriptTemplate);

    /**
     * 新增【脚本模版】
     *
     * @param scriptTemplate 【脚本模版实体】
     * @return 结果
     */
    int insertScriptTemplate(ScriptTemplate scriptTemplate);

    /**
     * 修改【脚本模版】
     *
     * @param scriptTemplate 【脚本模版实体】
     * @return 结果
     */
    int updateScriptTemplate(ScriptTemplate scriptTemplate);

    /**
     * 批量删除【脚本模版】
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteScriptTemplateByIds(@Param("ids")Long[] ids);

    /**
     * 获取脚本模版详细信息
     * @param id    脚本模版主键id
     * @return  脚本模版详细信息
     */
    ScriptTemplate getScriptTemplateDetail(Long id);

    /**
     * 获取脚本模版重名数量
     * @param name·脚本名称
     * @return  脚本
     */
    List<ScriptTemplate> getScriptTemplateByName(String name);
}
