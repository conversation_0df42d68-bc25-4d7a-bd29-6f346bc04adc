package com.ideal.script.mapper;

import com.ideal.script.model.entity.TaskParams;

import java.util.List;

/**
 * 常用/克隆关联关系，包含任务执行的参数信息Mapper接口
 *
 * <AUTHOR>
 */
public interface TaskParamsTemplateMapper {

    /**
     * 新增关联关系，包含任务执行的参数信息
     *
     * @param taskParams 关联关系，包含任务执行的参数信息
     * @return 结果
     */
    int insertTaskParams(TaskParams taskParams);


    /**
     * 根据taskId获取参数信息
     * @param taskId 任务id
     * @return 参数集合
     */
    List<TaskParams> selectTaskParamsByTaskId(Long taskId);

    /**
     * 根据任务id删除克隆任务
     * @param taskId 任务id
     * @return 执行结果
     */
    int deleteByTaskId(Long taskId);

    /**
     * 批量更新参数数据
     * @param list 参数信息
     * @return 执行成功条数
     */
    int batchUpdateByIds(List<TaskParams> list);


}
