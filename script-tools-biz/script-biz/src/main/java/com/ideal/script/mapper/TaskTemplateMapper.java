package com.ideal.script.mapper;

import com.ideal.script.model.bean.TaskCloneBean;
import com.ideal.script.model.dto.TaskApplyQueryDto;
import com.ideal.script.model.entity.Task;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 常用/克隆任务表mapper
 *
 * <AUTHOR>
 */
public interface TaskTemplateMapper {

    /**
     * 新增脚本任务
     *
     * @param task 脚本任务
     * @return 结果
     */
    int insertTaskTemplate(Task task);

    /**
     * 获取克隆数据列表
     * @param taskApplyQueryDto 查询条件dto
     * @param  orgCode 组织机构编码
     * @return 返回常用/克隆任务数据列表
     */
    List<TaskCloneBean> selectTaskTemplateList(@Param("params") TaskApplyQueryDto taskApplyQueryDto,@Param("orgCode") String orgCode,@Param("userId") Long userId);

    /**
     * 根据主键删除克隆任务
     * @param iid 任务id
     * @return 执行结果
     */
    int deleteById(Long iid);

    /**
     * 根据主键更新task内容
     * @return 执行成功条数
     */
    int updateById(Task task);

    /**
     * 根据id获取任务信息
     * @param taskTemplateId 任务id
     * @return 任务对象
     */
    TaskCloneBean selectTaskTemplateById(Long taskTemplateId);

    /**
     * 根据任务名获取常用任务
     * @param taskName 常用任务名
     * @return 常用任务数据
     */
    List<TaskCloneBean> selectTaskTemplateByTaskName(String taskName);

}
