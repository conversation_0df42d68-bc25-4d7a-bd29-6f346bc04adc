package com.ideal.script.mapper;

import com.ideal.script.model.entity.ScriptVersionShare;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 【脚本共享】Mapper接口
 * 
 * <AUTHOR>
 */
public interface ScriptVersionShareMapper
{

    /**
     * 新增
     *
     * @param scriptVersionShare 【共享信息】
     * @return 结果
     */
     int insertScriptVersionShare(ScriptVersionShare scriptVersionShare);

    /**
     * 获取共享数据
     * @param scriptVersionShare 参数
     * @return 共享数据
     */
    List<ScriptVersionShare> selectShareScriptData(@Param("scriptVersionShare")ScriptVersionShare scriptVersionShare);

    /**
     * 获取共享用户
     * @param scriptInfoId 脚本版本id
     * @return 用户id集合
     */
    List<Long> getSharedUser(@Param("scriptInfoId")Long scriptInfoId);

    /**
     * 查询共享脚本
     * @param userId 用户id
     * @param departmentId 部门id
     * @return 共享的脚本id
     */
    List<Long> getShareScriptIds(@Param("userId")Long userId,@Param("departmentId")Long departmentId);
    /**
     * 删除
     * 
     * @param ids 【共享表id】主键
     * @return 结果
     */
     int deleteScriptVersionShareByIds(@Param("ids")Long[] ids);

    /**
     * 获取共享的用户id或者部门id
     * @param scriptInfoId 脚本版本id
     * @param shareType 共享类型 0共享用户，1共享部门，2共享所有人
     * @return 返回共享的用户id或者部门id
     */
    List<String> getObjectIdList(@Param("scriptInfoId")Long scriptInfoId, @Param("shareType")Short shareType);
}
