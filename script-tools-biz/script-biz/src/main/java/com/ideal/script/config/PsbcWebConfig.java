package com.ideal.script.config;

import com.ideal.sc.annotation.CustomerIdentity;
import com.ideal.sc.constants.Constants;
import com.ideal.sc.constants.CustomerConstants;
import com.ideal.sc.constants.StrPool;
import com.ideal.script.filter.HttpServletCachedRequestFilter;
import com.ideal.script.interceptor.PsbcItsmTicketInterceptor;
import com.ideal.script.service.impl.builders.MyScriptServiceScripts;
import com.ideal.system.api.IUserInfo;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * psbc注册拦截器
 *
 * <AUTHOR>
 */
@Configuration
@CustomerIdentity(CustomerConstants.PSBC)
public class PsbcWebConfig implements WebMvcConfigurer {

    private static final String TASK_APPLY_URL = "/taskApply/scriptExecAuditing/*";

    private final IUserInfo userInfo;
    private final MyScriptServiceScripts scripts;
    public PsbcWebConfig(IUserInfo userInfo,MyScriptServiceScripts scripts){
        this.userInfo = userInfo;
        this.scripts = scripts;
    }

    @Bean
    @ConfigurationProperties(prefix = Constants.CUSTOMER_PREFIX + StrPool.DOT + CustomerConstants.PSBC)
    public PsbcProperties psbcProperties() {
        return new PsbcProperties();
    }


    @Bean
    public PsbcItsmTicketInterceptor psbcItsmTicketInterceptor() {
        return new PsbcItsmTicketInterceptor(userInfo,scripts);
    }


    @Bean
    public HttpServletCachedRequestFilter httpServletCachedRequestFilter() {
        return new HttpServletCachedRequestFilter();
    }

    @Bean
    public FilterRegistrationBean<HttpServletCachedRequestFilter> registrationBean() {
        FilterRegistrationBean<HttpServletCachedRequestFilter> registrationBean = new FilterRegistrationBean<>();
        registrationBean.setFilter(httpServletCachedRequestFilter());
        registrationBean.setOrder(1);
        registrationBean.setName("httpServletCachedRequestFilter");
        registrationBean.addUrlPatterns(TASK_APPLY_URL);
        return registrationBean;
    }


    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        //只拦截任务申请功能的请求地址
        registry.addInterceptor(psbcItsmTicketInterceptor()).addPathPatterns(TASK_APPLY_URL);
    }
}