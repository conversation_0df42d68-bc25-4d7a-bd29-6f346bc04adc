package com.ideal.script.xxljob;

import com.alibaba.fastjson.JSON;
import com.ideal.jobapi.core.apiclient.IdealXxlJobApiUtil;
import com.ideal.script.common.constant.enums.Enums;
import com.ideal.script.exception.ScriptException;
import com.ideal.script.model.dto.ScheduleJobTaskDto;
import com.ideal.script.model.dto.TaskDto;
import com.ideal.script.model.dto.TaskScheduleDto;
import com.ideal.script.service.ITaskExecuteService;
import com.ideal.script.service.ITaskScheduleService;
import com.ideal.script.service.ITaskService;
import com.ideal.system.common.component.model.CurrentUser;
import com.xxl.job.core.context.XxlJobContext;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * 任务执行服务触发任务回调处理类，用于处理回调业务
 *
 * <AUTHOR>
 */
@Component("ScriptTaskXxlJobHandler")
@SuppressWarnings("unused")
public class ScriptTaskXxlJobHandler {
    private final ITaskExecuteService taskExecuteService;

    private static final Logger logger = LoggerFactory.getLogger(ScriptTaskXxlJobHandler.class);
    private final ITaskService taskService;
    private final ITaskScheduleService taskScheduleService;

    public ScriptTaskXxlJobHandler(ITaskExecuteService taskExecuteService, ITaskService taskService, ITaskScheduleService taskScheduleService) {
        this.taskExecuteService = taskExecuteService;
        this.taskService = taskService;
        this.taskScheduleService = taskScheduleService;
    }

    @XxlJob("scriptJobHandler")
    public void scheduleJobHandler() {
        //获取参数
        String param = XxlJobHelper.getJobParam();
        logger.info("receive schedule job have to execute ,task has params : {}", param);
        XxlJobHelper.log("receive schedule job have to execute ,task has params : {}", param);
        if (StringUtils.isBlank(param)) {
            logger.error("receive schedule job have to execute ,task has params is null");
            XxlJobHelper.handleResult(XxlJobContext.HANDLE_CODE_FAIL, "params is null");
            return;
        }

        ScheduleJobTaskDto scheduleJobTaskDto;

        //验证返回执行参数是否符合规范要求
        try {
            scheduleJobTaskDto = JSON.parseObject(param, ScheduleJobTaskDto.class);
        } catch (Exception e) {
            logger.error("receive schedule job have to execute ,task has params is illegal");
            XxlJobHelper.handleResult(XxlJobContext.HANDLE_CODE_FAIL, "params is illegal");
            return;
        }

        if (scheduleJobTaskDto == null) {
            logger.error("receive schedule job have to execute ,task has execute params is empty");
            XxlJobHelper.handleResult(XxlJobContext.HANDLE_CODE_FAIL, "task has execute params is empty");
            return;
        }
        //封装上定时服务对应任务的主键ID
        scheduleJobTaskDto.setScheduleJobId(XxlJobHelper.getJobId());

        try {
            //调用待执行任务启动接口，进行任务启动.此处编写调用service启动方法的逻辑
            Long taskId = scheduleJobTaskDto.getTaskId();

            //业务方法调用
            CurrentUser user = new CurrentUser();
            user.setId(scheduleJobTaskDto.getCreatorId());
            user.setLoginName(scheduleJobTaskDto.getCreateName());

            TaskDto taskDto = taskService.selectTaskById(taskId);
            executeTaskLogic(user, taskDto, scheduleJobTaskDto);


            // 调用业务方法成功后，我们调用终止任务方法
            if (Enums.TaskScheduler.TIMED.getValue().equals(taskDto.getTaskScheduler())) {
                IdealXxlJobApiUtil.stopJob(Math.toIntExact(XxlJobHelper.getJobId()));
            }
            // 返回给调度中心执行结果正常，执行备注信息
            XxlJobHelper.handleSuccess("task start is success!");

        } catch (Exception e) {
            logger.error("receive schedule job have to execute ,execute is exception");
            //返回给调度中心执行结果异常，执行备注信息
            XxlJobHelper.handleFail(e.getMessage());
        }
    }

    /**
     * 启动定时任务以及更新定时任务与脚本任务关系表状态
     *
     * @param user 用户
     * @param taskDto 脚本任务对象
     * @param scheduleJobTaskDto 定时任务对象
     * <AUTHOR>
     */
    private void executeTaskLogic(CurrentUser user, TaskDto taskDto, ScheduleJobTaskDto scheduleJobTaskDto) throws ScriptException {
        try {
            // 定时任务回调脚本任务启动，驱动模式使用忽略异常分批驱动
            taskDto.setDriveMode(Enums.DriverModel.IGNORE_ERROR_BATCH_EXEC.getValue());
            // 定时任务回调启动，采用白名单启动方式
            taskExecuteService.scriptWhiteTaskStart(user, taskDto, taskDto.getSrcScriptUuid());

            // 更新定时任务脚本任务关系表的状态为运行中
            TaskScheduleDto taskScheduleDto = new TaskScheduleDto();
            taskScheduleDto.setScheduleId(scheduleJobTaskDto.getScheduleJobId());
            taskScheduleDto.setState(Enums.TaskScheduleEnum.TASK_RUNNING.getValue());
            taskScheduleService.updateTaskScheduleByScheduleId(taskScheduleDto);

        } catch (ScriptException e) {
            logger.error("receive schedule job have to execute ,Task execution failure",e);
            XxlJobHelper.handleResult(XxlJobContext.HANDLE_CODE_FAIL, "Task execution failure");
            throw new ScriptException("schedule.job.execution.failure");
        }
    }
}
