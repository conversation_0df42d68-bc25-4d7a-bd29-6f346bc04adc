package com.ideal.script.service.impl;

import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.ideal.common.util.BeanUtils;
import com.ideal.common.util.PageDataUtil;
import com.ideal.sc.util.CurrentUserUtil;
import com.ideal.script.common.constant.enums.Enums;
import com.ideal.script.exception.ScriptException;
import com.ideal.script.mapper.DangerCmdMapper;
import com.ideal.script.model.dto.DangerCmdDto;
import com.ideal.script.model.entity.DangerCmd;
import com.ideal.script.service.IDangerCmdService;
import com.ideal.system.common.component.model.CurrentUser;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.regex.Pattern;
import java.util.regex.PatternSyntaxException;
import java.util.stream.Collectors;

/**
 * 关键命令(高危命令)Service业务层处理
 *
 * <AUTHOR>
 */
@Service
public class DangerCmdServiceImpl implements IDangerCmdService {

    private final DangerCmdMapper dangerCmdMapper;
    private static final Logger logger = LoggerFactory.getLogger(DangerCmdServiceImpl.class);


    public DangerCmdServiceImpl(DangerCmdMapper dangerCmdMapper) {
        this.dangerCmdMapper = dangerCmdMapper;
    }

    /**
     * 查询关键命令(高危命令)
     *
     * @param id 关键命令(高危命令)主键
     * @return 关键命令(高危命令)
     */
    @Override
    public DangerCmdDto selectDangerCmdById(Long id) {
        return BeanUtils.copy(dangerCmdMapper.selectDangerCmdById(id), DangerCmdDto.class);
    }

    /**
     * 查询关键命令(高危命令)列表
     *
     * @param dangerCmdDto 关键命令(高危命令)
     * @return 关键命令(高危命令)
     */
    @Override
    public PageInfo<DangerCmdDto> selectDangerCmdList(DangerCmdDto dangerCmdDto, int pageNum, int pageSize) {
        DangerCmd dangerCmd = BeanUtils.copy(dangerCmdDto, DangerCmd.class);
        PageMethod.startPage(pageNum, pageSize);
        List<DangerCmd> dangerCmdList = dangerCmdMapper.selectDangerCmdList(dangerCmd);
        return PageDataUtil.toDtoPage(dangerCmdList, DangerCmdDto.class);
    }

    /**
     * 新增关键命令(高危命令)
     *
     * @param dangerCmdDto 关键命令(高危命令)
     * @return 结果
     */
    @Override
    public int insertDangerCmd(DangerCmdDto dangerCmdDto) throws ScriptException {
        convertScriptType(dangerCmdDto);
        DangerCmd dangerCmd = BeanUtils.copy(dangerCmdDto, DangerCmd.class);
        //关键命令合法校验
        checkDangerCmd(dangerCmd);
        //校验重复关键命令
        checkDuplicateDangerCmd(dangerCmd);
        return dangerCmdMapper.insertDangerCmd(dangerCmd);
    }

    /**
     * 关键命令重复校验
     * @param dangerCmd 关键命令实体
     * @throws ScriptException 自定义脚本异常
     */
    private void checkDuplicateDangerCmd(DangerCmd dangerCmd) throws ScriptException {
        //通过脚本类型和命名级别查询出所有对应的数据
        DangerCmd cmd = new DangerCmd();
        cmd.setScriptType(dangerCmd.getScriptType());
        cmd.setScriptCmdLevel(dangerCmd.getScriptCmdLevel());
        List<DangerCmd> dangerCmdList = dangerCmdMapper.selectDangerCmdList(cmd);
        
        //用筛选后的数据在这里只需要校验名称就可以
        for (DangerCmd currentDangerCmd : dangerCmdList) {
            if(dangerCmd.getScriptCmd().equals(currentDangerCmd.getScriptCmd()) && !Objects.equals(dangerCmd.getId(), currentDangerCmd.getId())){
                throw new ScriptException("duplicate.dangerCmd");
            }
        }
    }

    /**
     * 校验关键命令合法
     * @param dangerCmd 关键命令实体
     * @throws ScriptException  自定义脚本异常
     */
    private void checkDangerCmd(DangerCmd dangerCmd) throws ScriptException {
        try {
            Pattern pattern = Pattern.compile(dangerCmd.getScriptCmd(), Pattern.DOTALL);
            logger.info("valid Key Command {}" ,pattern);
        }catch(PatternSyntaxException e){
            throw new ScriptException("Invalid Key Command");
        }
    }
    /**
     * 修改关键命令(高危命令)
     *
     * @param dangerCmdDto 关键命令(高危命令)
     * @return 结果
     */
    @Override
    public int updateDangerCmd(DangerCmdDto dangerCmdDto) throws ScriptException {
        convertScriptType(dangerCmdDto);
        DangerCmd dangerCmd = BeanUtils.copy(dangerCmdDto, DangerCmd.class);
        //关键命令合法校验
        checkDangerCmd(dangerCmd);
        //校验重复关键命令
        checkDuplicateDangerCmd(dangerCmd);
        CurrentUser currentUser = CurrentUserUtil.getCurrentUser();
        dangerCmd.setUpdatorId(currentUser.getId());
        dangerCmd.setUpdatorName(currentUser.getFullName());
        return dangerCmdMapper.updateDangerCmd(dangerCmd);
    }

    /**
     * 批量删除关键命令(高危命令)
     *
     * @param ids 需要删除的关键命令(高危命令)主键
     * @return 结果
     */
    @Override
    public int deleteDangerCmdByIds(Long[] ids) {
        return dangerCmdMapper.deleteDangerCmdByIds(ids);
    }

    /**
     * 删除关键命令(高危命令)信息
     *
     * @param id 关键命令(高危命令)主键
     * @return 结果
     */
    @Override
    public int deleteDangerCmdById(Long id) {
        return dangerCmdMapper.deleteDangerCmdById(id);
    }

    /**
     * 脚本类型转换
     *
     * @param dangerCmdDto 关键命令实体
     */
    @Override
    public void convertScriptType(DangerCmdDto dangerCmdDto) {
        if (Enums.ScriptType.SHELL.getValue().equalsIgnoreCase(dangerCmdDto.getScriptType())) {
            dangerCmdDto.setScriptType("sh");
        } else if (Enums.ScriptType.PYTHON.getValue().equalsIgnoreCase(dangerCmdDto.getScriptType())) {
            dangerCmdDto.setScriptType("py");
        } else if (Enums.ScriptType.POWER_SHELL.getValue().equalsIgnoreCase(dangerCmdDto.getScriptType())) {
            dangerCmdDto.setScriptType("ps1");
        }
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateDangerCmdByLabel(List<String> deletedLabels) {

        //查找带有删除的标签的数据
        List<String> modifiedDeletedLabels = deletedLabels.stream()
                .map(s -> "%" + s + "%")
                .collect(Collectors.toList());

        List<DangerCmd> dangerCmdList = dangerCmdMapper.selectDangerCmdsByLabels(modifiedDeletedLabels);

        for (DangerCmd dangerCmd : dangerCmdList) {
            // 获取当前记录的标签列表
            List<String> currentLabels = Arrays.asList(dangerCmd.getScriptLabel().split(","));

            // 过滤掉要删除的标签
            List<String> updatedLabels = currentLabels.stream()
                    .filter(label -> !deletedLabels.contains(label))
                    .collect(Collectors.toList());

            // 将更新后的标签列表拼接成字符串
            String updatedLabelString = String.join(",", updatedLabels);


            // 更新数据库中的记录
            dangerCmd.setScriptLabel(updatedLabelString);

            //唯一的标签被删除
            if(updatedLabels.isEmpty()){
                dangerCmd.setScriptLabel("");
            }
            dangerCmdMapper.updateDangerCmd(dangerCmd);
        }

    }
}
