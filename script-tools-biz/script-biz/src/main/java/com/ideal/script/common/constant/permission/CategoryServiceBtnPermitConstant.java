package com.ideal.script.common.constant.permission;

/**
 * 类别维护菜单按钮权限常量类
 *
 * <AUTHOR>
 */
public class CategoryServiceBtnPermitConstant {

    // 权限表达式前缀和连接符
    private static final String PER_PREFIX = "@dp.hasBtnPermission('";
    private static final String PER_SUFFIX = "')";

    /**
     * 新增按钮权限码
     */
    public static final String SAVE_CATEGORY = "saveCategory";

    /**
     * 审核人授权按钮权限码
     */
    public static final String CAT_USER_PERMISSION = "catUserPermission";

    /**
     * 部门授权按钮权限码
     */
    public static final String CAT_ORG_PERMISSION = "catOrgPermission";

    /**
     * 删除按钮权限码
     */
    public static final String REMOVEL_CATEGORY = "removelCategory";

    /**
     * 编辑按钮权限码
     */
    public static final String UPDATE_CATEGORY = "updateCategory";

    /**
     * 分类绑定角色后，授权按钮权限码
     */
    public static final String CATEGORY_ROLE_PERM = "categoryRolePerm";

    /**
     * 新增按钮权限表达式
     */
    public static final String SAVE_CATEGORY_PER = PER_PREFIX + SAVE_CATEGORY + PER_SUFFIX;

    /**
     * 审核人授权按钮权限表达式
     */
    public static final String CAT_USER_PERMISSION_PER = PER_PREFIX + CAT_USER_PERMISSION + PER_SUFFIX;

    /**
     * 部门授权按钮权限表达式
     */
    public static final String CAT_ORG_PERMISSION_PER = PER_PREFIX + CAT_ORG_PERMISSION + PER_SUFFIX;

    /**
     * 删除按钮权限表达式
     */
    public static final String REMOVEL_CATEGORY_PER = PER_PREFIX + REMOVEL_CATEGORY + PER_SUFFIX;

    /**
     * 编辑按钮权限表达式
     */
    public static final String UPDATE_CATEGORY_PER = PER_PREFIX + UPDATE_CATEGORY + PER_SUFFIX;

    /**
     * 脚本分类绑定角色权限表达式
     */
    public static final String CAT_ROLE_PERMISSION_PER = PER_PREFIX + CATEGORY_ROLE_PERM + PER_SUFFIX;
}
