package com.ideal.script.mapper;

import com.ideal.script.model.entity.AgentInfo;
import com.ideal.script.model.entity.TaskIps;

import java.util.List;

/**
 * 常用/克隆任务与agent关系Mapper接口
 * 
 * <AUTHOR>
 */
public interface TaskIpsTemplateMapper
{

    /**
     * 新增任务与agent关系
     * 
     * @param taskIps 任务与agent关系
     * @return 结果
     */
     int insertTaskIps(TaskIps taskIps);

    /**
     * 根据任务id获取ips数据
     * @param taskId 任务id
     * @return ips数据
     */
    List<AgentInfo> selectAgentInfoByTaskId(Long taskId);

    /**
     * 根据任务id删除克隆任务
     * @param taskId 任务id
     * @return 执行结果
     */
    int deleteByTaskId(Long taskId);

}
