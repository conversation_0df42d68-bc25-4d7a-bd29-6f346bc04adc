package com.ideal.script.service.impl;

import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.ideal.common.util.BeanUtils;
import com.ideal.common.util.PageDataUtil;
import com.ideal.sc.constants.StrPool;
import com.ideal.script.dto.CategoryApiDto;
import com.ideal.script.dto.CategoryQueryDto;
import com.ideal.script.dto.ParameterValidationDto;
import com.ideal.script.dto.ScriptCategoryIconDto;
import com.ideal.script.dto.ScriptInfoApiDto;
import com.ideal.script.dto.ScriptInfoDto;
import com.ideal.script.dto.ScriptInfoQueryDto;
import com.ideal.script.mapper.InfoMapper;
import com.ideal.script.mapper.MyScriptMapper;
import com.ideal.script.model.bean.ScriptInfoQueryBean;
import com.ideal.script.model.bean.TaskApplyBean;
import com.ideal.script.model.entity.Category;
import com.ideal.script.model.entity.Info;
import com.ideal.script.service.ICategoryService;
import com.ideal.script.service.IInfoService;
import com.ideal.script.service.IInfoVersionService;
import com.ideal.script.service.IInfoVersionTextService;
import com.ideal.script.service.IParameterService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 脚本信息Service业务层处理
 *
 * <AUTHOR>
 */
@Service
public class InfoServiceImpl implements IInfoService {

    private static final Logger logger = LoggerFactory.getLogger(InfoServiceImpl.class);

    private final InfoMapper infoMapper;
    private final IInfoVersionService infoVersionService;
    private final IParameterService parameterService;
    private final IInfoVersionTextService infoVersionTextService;
    private final ICategoryService categoryService;
    private final MyScriptMapper myScriptMapper;

    public InfoServiceImpl(InfoMapper infoMapper, IInfoVersionService infoVersionService, IParameterService parameterService, IInfoVersionTextService infoVersionTextService, ICategoryService categoryService, MyScriptMapper myScriptMapper) {
        this.infoMapper = infoMapper;
        this.infoVersionService = infoVersionService;
        this.parameterService = parameterService;
        this.infoVersionTextService = infoVersionTextService;
        this.categoryService = categoryService;
        this.myScriptMapper = myScriptMapper;
    }


    /**
     * 查询【脚本基础信息】
     *
     * @param id 【脚本】主键
     * @return 【脚本基础信息Dto】
     */
    @Override
    public ScriptInfoDto selectInfoById(Long id) {
        return BeanUtils.copy(infoMapper.selectInfoById(id), ScriptInfoDto.class);
    }

    /**
     * 查询【脚本基础信息】
     *
     * @param uniqueUuid 脚本uniqueUuid
     * @return 【脚本基础信息Dto】
     */
    @Override
    public ScriptInfoDto selectInfoByUniqueUuid(String uniqueUuid) {
        return BeanUtils.copy(infoMapper.selectInfoByUniqueUuid(uniqueUuid), ScriptInfoDto.class);
    }

    /**
     * 查询脚本基础信息
     *
     * @param infoDto  【脚本基础信息Dto】
     * @param pageNum  【起始页】
     * @param pageSize 【每页大小】
     * @return 脚本基础信息（分页）
     */
    @Override
    public PageInfo<ScriptInfoDto> selectInfoList(ScriptInfoDto infoDto, int pageNum, int pageSize) {
        PageMethod.startPage(pageNum, pageSize);
        List<Info> infoList = null;
        if (null != infoDto) {
            Info info = BeanUtils.copy(infoDto, Info.class);
            infoList = infoMapper.selectInfoList(info);
        }
        return PageDataUtil.toDtoPage(infoList, ScriptInfoDto.class);
    }

    /**
     * 对接巡检查询脚本列表
     *
     * @param scriptId        脚本id
     * @param scriptName      脚本名称
     * @param excludeScriptId 不需要的脚本id
     * @return 脚本基本信息
     */
    @Override
    public List<ScriptInfoDto> selectInfoListApi(Long scriptId, String scriptName, List<Long> excludeScriptId) {
        List<Info> listInfo = infoMapper.selectInfoListApi(scriptId, scriptName, excludeScriptId);
        return BeanUtils.copy(listInfo, ScriptInfoDto.class);
    }

    /**
     * 新增【脚本基础信息】
     *
     * @param infoDto 【脚本基础信息Dto】
     */
    @Override
    public void insertInfo(ScriptInfoDto infoDto) {
        Info info = BeanUtils.copy(infoDto, Info.class);
        infoMapper.insertInfo(info);
        infoDto.setId(info.getId());
        infoDto.setUniqueUuid(info.getUniqueUuid());
    }

    /**
     * 修改【脚本基础信息】
     *
     * @param infoDto 【脚本基础信息Dto】
     */
    @Override
    public void updateInfo(ScriptInfoDto infoDto) {
        Info info = BeanUtils.copy(infoDto, Info.class);
        infoMapper.updateInfo(info);
    }

    /**
     * 批量删除【脚本基础信息】
     *
     * @param ids 需要删除的【脚本】主键
     */
    @Override
    public void deleteInfoByIds(Long[] ids) {
        infoMapper.deleteInfoByIds(ids);
    }

    /**
     * 删除【脚本基础信息】信息
     *
     * @param id 【脚本】主键
     * @return 结果
     */
    @Override
    public int deleteInfoById(Long id) {
        return infoMapper.deleteInfoById(id);
    }

    /**
     * 验证是否存在相同脚本中文名的脚本
     *
     * @param scriptNameZh 脚本中文名
     * @return Boolean
     */
    @Override
    public Boolean validScriptNameZhCountExist(String scriptNameZh) {
        return infoMapper.validScriptNameZhCountExist(scriptNameZh);
    }



    /**
     * 查询分类树
     *
     * @param categoryQueryDto 分类查询条件
     * @return {@link List}<{@link CategoryApiDto}>
     */
    @Override
    public List<CategoryApiDto> getCategoryTreeApi(CategoryQueryDto categoryQueryDto) {
        logger.info("getCategoryTree {}", categoryQueryDto);
        Category category = BeanUtils.copy(categoryQueryDto, Category.class);

        if (category.getLevel() == null) {
            // 没有级别从一级根类查询
            category.setLevel(1);
        }
        List<Category> categoryList = categoryService.getCategoryWithChildren(category, categoryQueryDto.getQueryChildren());
        return BeanUtils.copy(categoryList, CategoryApiDto.class);
    }

    /**
     * 查询脚本基础信息列表
     *
     * @param scriptInfoQueryDto 脚本信息查询条件
     * @return {@link List}<{@link ScriptInfoApiDto}>
     */
    @Override
    public List<ScriptInfoApiDto> getScriptInfoListApi(ScriptInfoQueryDto scriptInfoQueryDto) {
        logger.info("getScriptInfoList {}", scriptInfoQueryDto);
        ScriptInfoQueryBean scriptInfoQueryBean = BeanUtils.copy(scriptInfoQueryDto, ScriptInfoQueryBean.class);
        List<TaskApplyBean> infoVersionList = infoVersionService.getInfoVersionList(scriptInfoQueryBean);
        //拼接分类名称
        for (TaskApplyBean taskApplyBean1 : infoVersionList) {
            if (null == taskApplyBean1.getCategoryId()) {
                taskApplyBean1.setScriptCategoryName(null);
            } else {
                String categoryName = categoryService.getCategoryFullPath(taskApplyBean1.getCategoryId());
                taskApplyBean1.setScriptCategoryName(categoryName);
            }
        }

        List<ScriptInfoApiDto> scriptInfoApiDtoList = BeanUtils.copy(infoVersionList, ScriptInfoApiDto.class);
        for (ScriptInfoApiDto scriptInfoApiDto : scriptInfoApiDtoList) {
            // 组织脚本参数
            scriptInfoApiDto.setScriptParamApiDtoList(BeanUtils.copy(parameterService.getParameterByUuid(scriptInfoApiDto.getSrcScriptUuid()), ParameterValidationDto.class));
        }
        return scriptInfoApiDtoList;
    }

    /**
     * 分页查询脚本基础信息列表
     *
     * @param scriptInfoQueryDto 脚本信息查询条件
     * @return {@link PageInfo}<{@link ScriptInfoApiDto}>
     */
    @Override
    public PageInfo<ScriptInfoApiDto> getScriptInfoPageListApi(ScriptInfoQueryDto scriptInfoQueryDto) {
        logger.info("getScriptInfoPageList {}", scriptInfoQueryDto);
        ScriptInfoQueryBean scriptInfoQueryBean = BeanUtils.copy(scriptInfoQueryDto, ScriptInfoQueryBean.class);
        PageMethod.startPage(scriptInfoQueryDto.getPageNum(), scriptInfoQueryDto.getPageSize());
        List<TaskApplyBean> infoVersionList = infoVersionService.getInfoVersionList(scriptInfoQueryBean);
        for (TaskApplyBean taskApplyBean1 : infoVersionList) {
            if (null == taskApplyBean1.getCategoryId()) {
                taskApplyBean1.setScriptCategoryName(null);
            } else {
                String categoryName = categoryService.getCategoryFullPath(taskApplyBean1.getCategoryId());
                taskApplyBean1.setScriptCategoryName(categoryName);
            }
        }



        PageInfo<ScriptInfoApiDto> infoVersionPage = PageDataUtil.toDtoPage(infoVersionList, ScriptInfoApiDto.class);
        for (ScriptInfoApiDto scriptInfoApiDto : infoVersionPage.getList()) {
            // 组织脚本参数
            scriptInfoApiDto.setScriptParamApiDtoList(BeanUtils.copy(parameterService.getParameterByUuid(scriptInfoApiDto.getSrcScriptUuid()), ParameterValidationDto.class));
        }
        return infoVersionPage ;
    }


    /**
     * 根据脚本uuid获取图标
     *
     * @param srcScriptUuids 脚本srcUuid集合
     * @return 图标和脚本uuid对应关系列表
     */
    @Override
    public List<ScriptCategoryIconDto> getScriptCategoryIconList(List<String> srcScriptUuids) {
        if (CollectionUtils.isEmpty(srcScriptUuids)) {
            logger.warn("srcScriptUuids 参数为空!");
            return new ArrayList<>();
        }
        //根据脚本uuid查询脚本信息
        List<ScriptCategoryIconDto> scriptCategoryIconList = infoMapper.getScriptCategoryIconList(srcScriptUuids);
        if (CollectionUtils.isEmpty(scriptCategoryIconList)) {
            logger.warn("根据脚本uuid:{},未查询到脚本信息!", srcScriptUuids);
            return new ArrayList<>();
        }
        //根据分类全路径categoryPath截取第一个/之前的字符串拿到顶级分类名
        List<String> firstCategoryNameList = scriptCategoryIconList.stream()
                .map(ScriptCategoryIconDto::getCategoryPath)
                .map(categoryPath -> categoryPath.split(StrPool.SLASH)[0])
                .distinct()
                .collect(Collectors.toList());
        //根据所有顶级分类名查询分类表获取图标
        List<Category> categoryList = categoryService.getFirstCategoryByCategoryNameList(firstCategoryNameList);
        //将分类信息封装成map
        Map<String, String> categoryMap = categoryList.stream()
                .filter(category -> StringUtils.isNotBlank(category.getIcon()))
                .collect(Collectors.toMap(Category::getName, Category::getIcon));
        //如果图标map为空，直接返回
        if(MapUtils.isEmpty(categoryMap)){
            return scriptCategoryIconList;
        }
        //设置图标和脚本uuid对应关系
        scriptCategoryIconList.forEach(scriptCategoryIconDto -> scriptCategoryIconDto
                .setIcon(categoryMap.get(scriptCategoryIconDto.getCategoryPath().split(StrPool.SLASH)[0])));
        return scriptCategoryIconList;
    }

}
