package com.ideal.script.service.impl;

import com.ideal.sc.util.HttpClientUtil;
import com.ideal.script.common.constant.Constants;
import com.ideal.script.config.CibProperties;
import com.ideal.script.mapper.TaskRuntimeMapper;
import com.ideal.script.model.dto.cibitsm.FlowAutoTemplateResultDto;
import com.ideal.script.service.IAfterScriptExecAuditing;
import groovy.util.ScriptException;
import groovy.util.logging.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.lang.Nullable;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.*;

@Slf4j
@Service
public class AfterScriptExecAuditing implements IAfterScriptExecAuditing {

    private static final Logger log = LoggerFactory.getLogger(AfterScriptExecAuditing.class);
    private final RedisTemplate<String, String> redisTemplate;
    private final CibProperties cibProperties;
    private final TaskRuntimeMapper taskRuntimeMapper;


    public AfterScriptExecAuditing(RedisTemplate<String, String> redisTemplate, @Nullable CibProperties cibProperties, TaskRuntimeMapper taskRuntimeMapper) {
        this.redisTemplate = redisTemplate;
        this.cibProperties = cibProperties;
        this.taskRuntimeMapper = taskRuntimeMapper;
    }

    /**
     * cib使用http方式调用接口时，任务申请结束推送消息
     * @param result 执行结果
     * @param workOrderNum 工单编号
     */
    @Async
    @Override
    public void cibItsmAuditingResult(Object result, String workOrderNum) {
        //判断任务申请返回值是否为空，不为空获取任务id，然后调用itsm对应接口实现推送
        if(ObjectUtils.notEqual(result,null)){
            List<Long> taskIds = (List<Long>) result;
            if(ObjectUtils.notEqual(taskIds,null) && !taskIds.isEmpty()){
                //调用itsm接口，推送创建任务申请结果
                scriptExecAuditingResultToItsm(taskIds,workOrderNum,null);
            }else{
                log.error("cibItsmAuditingResult error , data is null");
            }
        }else{
            log.error("cibItsmAuditingResult error , result is null");
        }
    }

    /**
     * 脚本任务结束后给cib推送结果
     * @param taskId 任务id
     */
    @Async
    @Override
    public void scriptTaskFinishedToItsm(Long taskId) {
        if(ObjectUtils.notEqual(taskId,null) && taskId > 0){
            //根据任务id获取工单编号
            String workOrderNum = redisTemplate.opsForValue().get(Constants.THIRD_PARTY_CALL_TASK_PREFIX + taskId);
            if(StringUtils.isNotBlank(workOrderNum)){
                //根据任务id查询任务状态
                int i = taskRuntimeMapper.selectErrorRuntimeByTaskId(taskId);
                int state = 1;
                if(i > 0){
                    state = 2;
                }
                //给itsm发送任务结果信息
                scriptExecAuditingResultToItsm(Collections.singletonList(taskId),workOrderNum,state);
            }
        }
    }

    @Override
    public void scriptTaskApplyFinishedToItsm(String code, String user, List<FlowAutoTemplateResultDto> flowAutoTemplateResultList){
        try {
            String url = "";
            if (cibProperties != null) {
                url = cibProperties.getItsmExecAuditingResultUrl();
            }
            if(StringUtils.isBlank(url)){
                throw new ScriptException("ItsmExecAuditingResultUrl is empty");
            }
            //获取token
            String itsmToken = getItsmToken();
            if(StringUtils.isNotBlank(itsmToken)){
                //设置请求头
                Map<String,String> headers = new HashMap<>();
                headers.put("Content-Type","application/json");
                headers.put("Authorization",itsmToken);

                //设置请求数据
                Map<String,Object> dataMap = new HashMap<>();
                dataMap.put("code",code);//单号
                //当前处理人信息notesId
                dataMap.put("flowHandler",user);
                dataMap.put("flowAutoTemplateList",flowAutoTemplateResultList);
                //调用itsm接口，推送结果信息
                HttpClientUtil.postByJson(url, headers, dataMap, Object.class);
            }else{
                throw new ScriptException("cibItsmAuditingResult error , itsmToken is null");
            }
        }catch (Exception e){
            log.error("任务申请结束推送itsm失败：" , e);
        }
    }

    /**
     * 给itsm推送任务申请\执行任务结果
     * @param taskIds 任务id集合
     * @param workOrderNum 单号
     */
    private void scriptExecAuditingResultToItsm(List<Long> taskIds, String workOrderNum, Integer state) {
        try {
            String url = null;
            if (cibProperties != null) {
                url = cibProperties.getItsmExecAuditingResultUrl();
            }
            if(StringUtils.isBlank(url)){
                throw new ScriptException("ItsmExecAuditingResultUrl is empty");
            }
            //获取token
            String itsmToken = getItsmToken();
            if(StringUtils.isNotBlank(itsmToken)){
                //设置请求头
                Map<String,String> headers = new HashMap<>();
                headers.put("Content-Type","application/json");
                headers.put("Authorization",itsmToken);

                //设置请求数据
                Map<String,Object> dataMap = new HashMap<>();
                dataMap.put("code",workOrderNum);//单号
                //定义返回值 当前处理人信息notesId
                String flowHandler = "";
                List<FlowAutoTemplateResultDto> flowAutoTemplateDtoList = new ArrayList<>();
                for(Long taskId : taskIds){
                    if(StringUtils.isBlank(flowHandler)){
                        flowHandler = redisTemplate.opsForValue().get(Constants.THIRD_PARTY_CALL_TASK_IMPLEMENTER + taskId);
                    }
                    if(StringUtils.isNotBlank(flowHandler)){
                        //当前处理人信息notesId
                        dataMap.put("flowHandler",flowHandler);
                    }
                    FlowAutoTemplateResultDto flowAutoTemplateResultDto = new FlowAutoTemplateResultDto();
                    //itsm业务id
                    flowAutoTemplateResultDto.setAutoUid(String.valueOf(taskId));
                    //脚本服务化任务id
                    flowAutoTemplateResultDto.setInstanceCode(redisTemplate.opsForValue().get(Constants.THIRD_PARTY_CALL_TASK_ID + taskId));
                    //执行状态,脚本任务执行结束后赋值，任务申请结束不需要给这个字段赋值
                    if(Objects.nonNull(state)){
                        flowAutoTemplateResultDto.setExecuteStatus(state);
                    }
                    flowAutoTemplateDtoList.add(flowAutoTemplateResultDto);
                }
                dataMap.put("flowAutoTemplateList",flowAutoTemplateDtoList);
                //调用itsm接口，推送结果信息
                HttpClientUtil.postByJson(url, headers, dataMap, Object.class);
            }else{
                throw new ScriptException("cibItsmAuditingResult error , itsmToken is null");
            }
        }catch (Exception e){
            log.error("任务申请结束推送itsm失败：" , e);
        }
    }

    /**
     * 获取itsm平台token
     * @return token
     */
    @Override
    public String getItsmToken(){
        //如果redis中存在token，则不需要调用itsm接口获取
        String token = redisTemplate.opsForValue().get(Constants.ITSM_INTERFACE_TOKEN_TIMEOUT);
        if(StringUtils.isNotBlank(token)){
            return token;
        }

        String id_token = "";
        String userName = null;
        if (cibProperties != null) {
            userName = cibProperties.getItsmExecAuditingTokenUserName();
        }
        String password = null;
        if (cibProperties != null) {
            password = cibProperties.getItsmExecAuditingTokenPassword();
        }
        String url = null;
        if (cibProperties != null) {
            url = cibProperties.getItsmExecAuditingTokenUrl();
        }
        if(StringUtils.isBlank(url) || StringUtils.isBlank(userName) || StringUtils.isBlank(password)){
            log.error("itsm获取token接口url地址、用户名、密码均不能为空，请检查配置");
            return id_token;
        }
        //设置请求头
        Map<String,String> headers = new HashMap<>();
        headers.put("Content-Type","application/json");
        //设置请求数据
        Map<String,Object> dataMap = new HashMap<>();
        dataMap.put("username",userName);
        dataMap.put("password",password);
        Map map = HttpClientUtil.postByJson(url, headers, dataMap, Map.class);
        if(ObjectUtils.notEqual(map,null)
            && ObjectUtils.notEqual(map.get("id_token"),null)){
            id_token = String.valueOf(map.get("id_token"));
            //将token放置在redis中，后续调用所有itsm相关接口都需要传递这个token，如果redis中不存在则再次调用获取token方法，否则直接从redis中获取
            redisTemplate.opsForValue().set(Constants.ITSM_INTERFACE_TOKEN_TIMEOUT,id_token, Duration.ofMinutes(cibProperties.getItsmTokenTimeout()));
        }
        return id_token;
    }
}
