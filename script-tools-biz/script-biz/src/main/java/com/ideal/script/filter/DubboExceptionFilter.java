package com.ideal.script.filter;

import com.ideal.common.util.spring.SpringUtil;
import com.ideal.sc.validation.BeanValidator;
import com.ideal.script.exception.ScriptException;
import org.apache.dubbo.common.constants.CommonConstants;
import org.apache.dubbo.common.extension.Activate;
import org.apache.dubbo.common.logger.ErrorTypeAwareLogger;
import org.apache.dubbo.common.logger.LoggerFactory;
import org.apache.dubbo.rpc.Invocation;
import org.apache.dubbo.rpc.Invoker;
import org.apache.dubbo.rpc.Result;
import org.apache.dubbo.rpc.RpcContext;
import org.apache.dubbo.rpc.filter.ExceptionFilter;
import org.apache.dubbo.rpc.service.GenericService;
import org.apache.dubbo.rpc.support.RpcUtils;

import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import java.util.Set;

import static org.apache.dubbo.common.constants.LoggerCodeConstants.CONFIG_FILTER_VALIDATION_EXCEPTION;

/**
 * dubbo jsr303异常过滤器
 *
 * <AUTHOR>
 **/
@Activate(group = CommonConstants.PROVIDER)
public class DubboExceptionFilter extends ExceptionFilter {
    private final ErrorTypeAwareLogger logger = LoggerFactory.getErrorTypeAwareLogger(DubboExceptionFilter.class);

    @Override
    public void onResponse(Result appResponse, Invoker<?> invoker, Invocation invocation) {
        if (appResponse.hasException() && GenericService.class != invoker.getInterface()) {
            try {
                //自定义jsr303校验异常处理
                Throwable exception = appResponse.getException();
                if (exception instanceof ConstraintViolationException) {
                    Set<ConstraintViolation<?>> constraintViolations = ((ConstraintViolationException) exception).getConstraintViolations();
                    BeanValidator validator = SpringUtil.getBean(BeanValidator.class);
                    //提取校验错误信息
                    String errorMsg = "Failed to validate service: " + invoker.getInterface() + ", method: " + invocation.getMethodName() + ", cause: "
                            + validator.extractMessages(constraintViolations);
                    logger.error(errorMsg, exception);
                    appResponse.setException(new ScriptException(errorMsg));
                    return;
                }
                //其他情况调用父类
                super.onResponse(appResponse, invoker, invocation);
            } catch (Throwable e) {
                logger.warn(CONFIG_FILTER_VALIDATION_EXCEPTION, "", "",
                        "Fail to ExceptionFilter when called by " + RpcContext.getServiceContext().getRemoteHost() +
                                ". service: " + invoker.getInterface().getName() + ", method: " + RpcUtils.getMethodName(invocation) +
                                ", exception: " + e.getClass().getName() + ": " + e.getMessage(), e);
            }
        }
    }

}
