package com.ideal.script.mapper;

import com.ideal.script.model.entity.Attachment;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Mapper接口
 * 
 * <AUTHOR>
 */
public interface AttachmentMapper 
{
    /**
     * 查询
     * 
     * @param id 主键
     * @return 
     */
     Attachment selectAttachmentById(Long id);

    List<Attachment> selectAttachmentByIds(Long[] ids);

    /**
     * 查询列表
     * 
     * @param attachment 
     * @return 集合
     */
     List<Attachment> selectAttachmentList(Attachment attachment);

    /**
     * 新增
     * 
     * @param attachment 
     * @return 结果
     */
     int insertAttachment(Attachment attachment);

    /**
     * 修改
     * 
     * @param attachment 
     * @return 结果
     */
     int updateAttachment(Attachment attachment);

    /**
     * 更改附件功能，有删除有新增的情况
     * @param ids   id
     * @param uuid  uuid
     * @return      结果
     */
     int deleteAttachmentByIdAndUuid(@Param("uuid") String uuid, @Param("ids") List<Long> ids);

    /**
     * 更新
     * @param scriptUuid    脚本uuid
     * @return  结果
     */
     int updateScriptUuid( @Param("scriptUuid") String scriptUuid);

    /**
     * 删除
     * 
     * @param id 主键
     * @return 结果
     */
     int deleteAttachmentById(Long id);

    /**
     * 批量删除
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
     int deleteAttachmentByIds(@Param("ids")Long[] ids);

    /**
     * 删除附件
     * @param scriptUuids   脚本uuid数组
     * @return      结果
     */
     int deleteAttachmentByScriptUuids(String[] scriptUuids);

    /**
     * 拷贝
     * @param ids   id数组
     * @return  结果
     */
     int copyAttachment(@Param("ids")Long[] ids);

    /**
     * 删除
     * @param oldUuid   uuid
     * @return     结果
     */
    int deleteAttachmentByUuid( @Param("oldUuid") String oldUuid);

    /**
     * 查询
     * @param uuid uuid
     * @return     结果
     */
    List<Attachment> getAttachmentByUuid( @Param("uuid") String uuid);

    /**
     * 查询
     * @param uuid uuid
     * @return     结果
     */
    List<Attachment> getAttachmentListForDownload(String uuid);
}
