package com.ideal.script.common.constant.enums;

/**
 * 审核类型枚举
 *
 * <AUTHOR>
 */
public enum AuditTypeEnum {
    /**
     * 脚本发布
     */
    PUBLISH(1,"脚本发布"),
    /**
     * 脚本删除
     */
    DELETE(2,"脚本删除"),
    /**
     * 脚本任务
     */
    TASK (3,"脚本任务");

    private final int type;
    private final String description;
    AuditTypeEnum(int type,String description) {
        this.type = type;
        this.description = description;
    }

    public int getType() {
        return type;
    }

    public String getDescription() {
        return description;
    }
}
