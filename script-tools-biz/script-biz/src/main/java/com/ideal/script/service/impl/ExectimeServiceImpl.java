package com.ideal.script.service.impl;

import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.ideal.common.util.BeanUtils;
import com.ideal.common.util.PageDataUtil;
import com.ideal.script.common.constant.enums.Enums;
import com.ideal.script.mapper.ExectimeMapper;
import com.ideal.script.model.dto.ExectimeDto;
import com.ideal.script.model.entity.Exectime;
import com.ideal.script.service.IExectimeService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 脚本执行次数统计Service业务层处理
 *
 * <AUTHOR>
 */
@Service
public class ExectimeServiceImpl implements IExectimeService {

    private final ExectimeMapper exectimeMapper;

    public ExectimeServiceImpl(ExectimeMapper exectimeMapper) {
        this.exectimeMapper = exectimeMapper;
    }

    /**
     * 查询脚本执行次数统计
     *
     * @param id 脚本执行次数统计主键
     * @return 脚本执行次数统计
     */
    @Override
    public ExectimeDto selectExectimeById(Long id) {
        return BeanUtils.copy(exectimeMapper.selectExectimeById(id), ExectimeDto.class);
    }

    /**
     * 查询脚本执行次数统计列表
     *
     * @param exectimeDto 脚本执行次数统计
     * @return 脚本执行次数统计
     */
    @Override
    public PageInfo<ExectimeDto> selectExectimeList(ExectimeDto exectimeDto, int pageNum, int pageSize) {
        PageMethod.startPage(pageNum, pageSize);
        List<Exectime> exectimeList = exectimeMapper.selectExectimeList(BeanUtils.copy(exectimeDto, Exectime.class));
        return PageDataUtil.toDtoPage(exectimeList, ExectimeDto.class);
    }


    /**
     * 新增脚本执行次数统计
     *
     * @param exectimeDto 脚本执行次数统计
     * @return 结果
     */
    @Override
    public int insertExectime(ExectimeDto exectimeDto) {
        Exectime exectime = BeanUtils.copy(exectimeDto, Exectime.class);
        return exectimeMapper.insertExectime(exectime);
    }

    /**
     * 修改脚本执行次数统计
     *
     * @param exectimeDto 脚本执行次数统计
     * @return 结果
     */
    @Override
    public int updateExectime(ExectimeDto exectimeDto) {
        Exectime exectime = BeanUtils.copy(exectimeDto, Exectime.class);
        return exectimeMapper.updateExectime(exectime);
    }

    /**
     * 批量删除脚本执行次数统计
     *
     * @param ids 需要删除的脚本执行次数统计主键
     * @return 结果
     */
    @Override
    public int deleteExectimeByIds(Long[] ids) {
        return exectimeMapper.deleteExectimeByIds(ids);
    }

    /**
     * 删除脚本执行次数统计信息
     *
     * @param id 脚本执行次数统计主键
     * @return 结果
     */
    @Override
    public int deleteExectimeById(Long id) {
        return exectimeMapper.deleteExectimeById(id);
    }

    /**
     * 更新执行次数
     *
     * @param status        脚本执行状态
     * @param srcScriptUuid ieai_script_info_version表的isrc_script_uuid
     */
    @Override
    @Async
    public void updateScriptExecTime(Integer status, String srcScriptUuid, Integer type) {
        if (null != status) {
            if (status == Enums.TaskRuntimeState.COMPLETED.getValue()) {
                // 成功状态更新总次数和成功次数
                type = 1;
            } else {
                // 其他状态只更新总次数
                type = 2;
            }
        }
        if (StringUtils.isNotBlank(srcScriptUuid)) {
            exectimeMapper.updateScriptExectime(type, srcScriptUuid);
        }
    }

    /**
     * 查询脚本使用次数、成功数、总数、成功率
     *
     * @param srcScriptUuid 脚本版本Id
     * @return ExectimeDto
     */
    @Override
    public ExectimeDto getTotalAndSuccessRate(String srcScriptUuid) {
        return BeanUtils.copy(exectimeMapper.getTotalAndSuccessRate(srcScriptUuid), ExectimeDto.class);
    }

}
