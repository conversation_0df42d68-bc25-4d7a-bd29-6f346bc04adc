package com.ideal.script.mapper;

import com.ideal.script.model.entity.ParameterManager;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Mapper接口
 * 
 * <AUTHOR>
 */
public interface ParameterManagerMapper 
{
    /**
     * 查询
     * 
     * @param id 主键
     * @return 
     */
     ParameterManager selectParameterManagerById(Long id);

    /**
     * 查询列表
     * 
     * @param parameterManager 
     * @return 集合
     */
     List<ParameterManager> selectParameterManagerList(ParameterManager parameterManager);
    /**
     * 校验参数是否重复
     *
     * @param parameterManager 查询参数
     * @return 参数数据
     */
    List<ParameterManager> selectSaveNameList(ParameterManager parameterManager);

    /**
     * 新增
     * 
     * @param parameterManager 
     * @return 结果
     */
     int insertParameterManager(ParameterManager parameterManager);

    /**
     * 修改
     * 
     * @param parameterManager 
     * @return 结果
     */
     int updateParameterManager(ParameterManager parameterManager);

    /**
     * 删除
     * 
     * @param id 主键
     * @return 结果
     */
     int deleteParameterManagerById(Long id);

    /**
     * 批量删除
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
     int deleteParameterManagerByIds(@Param("ids")Long[] ids);

    /**
     * 查询
     * @return 结果
     */
    List<ParameterManager> selectParameterManagerForScriptEdit();

    Boolean validParamterCheckExist(String paramName);

    List<ParameterManager> selectParameterManagerByName(String paramName);

    /**
     * 检查ID是否存在
     *
     * @param ids ID数组
     * @return 存在的ID列表
     */
    List<Long> checkIdsExist(Long[] ids);
}
