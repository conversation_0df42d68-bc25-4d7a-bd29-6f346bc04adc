package com.ideal.script.mapper;

import com.ideal.script.model.entity.AuditRelation;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 双人复核与脚本服务化关系Mapper接口
 * 
 * <AUTHOR>
 */
public interface AuditRelationMapper 
{
    /**
     * 查询双人复核与脚本服务化关系
     * 
     * @param id 双人复核与脚本服务化关系主键
     * @return 双人复核与脚本服务化关系
     */
     AuditRelation selectAuditRelationById(Long id);

    /**
     * 查询双人复核与脚本服务化关系
     *
     * @param id 双人复核与脚本服务化关系主键
     * @return 双人复核与脚本服务化关系
     */
    AuditRelation selectAuditRelationByTaskId(Long takId);

    /**
     * 查询双人复核与脚本服务化关系(通过双人复核主键)
     * @param workItemId    双人复核表主键id
     * @return  双人复核与脚本服务化关系
     */
     AuditRelation selectAuditRelationByWorkItemId(Long workItemId);

    /**
     * 根据双人复核id查询脚本id以及版本uuid
     *
     * @param id 双人复核与脚本服务化关系主键
     * @return {@link AuditRelation }
     */
    AuditRelation selectInfoIdAndSrcScriptUuidByAuditRelationId(Long id);

    /**
     * 查询审核关系表
     * @param auditRelation 审核关系表对象
     * @return 返回审核关系表对象
     */
    AuditRelation selectAuditRelationForAudit(AuditRelation auditRelation);

    /**
     * 查询双人复核与脚本服务化关系列表
     * 
     * @param auditRelation 双人复核与脚本服务化关系
     * @return 双人复核与脚本服务化关系集合
     */
     List<AuditRelation> selectAuditRelationList(AuditRelation auditRelation);

    /**
     * 新增双人复核与脚本服务化关系
     * 
     * @param auditRelation 双人复核与脚本服务化关系
     * @return 结果
     */
     int insertAuditRelation(AuditRelation auditRelation);

    /**
     * 修改双人复核与脚本服务化关系
     * 
     * @param auditRelation 双人复核与脚本服务化关系
     * @return 结果
     */
     int updateAuditRelation(AuditRelation auditRelation);

    /**
     * 修改双人复核与脚本服务化关系通过workItemId
     *
     * @param auditRelation 双人复核与脚本服务化关系
     * @return 结果
     */
     int updateAuditRelationByWorkItemId(AuditRelation auditRelation);

    /**
     * 根据脚本的uuid更新关系数据
     * @param auditRelation 关系对象
     * @return 关系数据
     */
    int updateAuditRelationForAudit(AuditRelation auditRelation);

    /**
     * 删除双人复核与脚本服务化关系
     * 
     * @param id 双人复核与脚本服务化关系主键
     * @return 结果
     */
     int deleteAuditRelationById(Long id);

    /**
     * 批量删除双人复核与脚本服务化关系
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
     int deleteAuditRelationByIds(@Param("ids")Long[] ids);

    /**
     * 根据脚本的info_version_id获取审核数据
     * @param ids 脚本的info_version_id
     * @return 脚本审核结果数据集合
     */
     List<AuditRelation> selectAuditRelationByScriptInfoVersionId(@Param("ids")Long[] ids);
}
