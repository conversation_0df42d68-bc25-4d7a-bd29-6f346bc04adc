package com.ideal.script.mapper;

import java.util.List;
import com.ideal.script.model.entity.Functionpublish;
import com.ideal.script.model.entity.VarAndFuncForEdit;
import org.apache.ibatis.annotations.Param;

/**
 * 函数库基础发布Mapper接口
 * 
 * <AUTHOR>
 */
public interface FunctionpublishMapper 
{
    /**
     * 查询函数库基础发布
     * 
     * @param id 函数库基础发布主键
     * @return 函数库基础发布
     */
     Functionpublish selectFunctionpublishById(Long id);

    /**
     * 查询函数库基础发布列表
     * 
     * @param functionpublish 函数库基础发布
     * @return 函数库基础发布集合
     */
     List<Functionpublish> selectFunctionpublishList(Functionpublish functionpublish);

    /**
     * 新增函数库基础发布
     * 
     * @param functionpublish 函数库基础发布
     * @return 结果
     */
     int insertFunctionpublish(Functionpublish functionpublish);

    /**
     * 修改函数库基础发布
     * 
     * @param functionpublish 函数库基础发布
     * @return 结果
     */
     int updateFunctionpublish(Functionpublish functionpublish);

    /**
     * 删除函数库基础发布
     * 
     * @param id 函数库基础发布主键
     * @return 结果
     */
     int deleteFunctionpublishById(Long id);

    /**
     * 批量删除函数库基础发布
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
     int deleteFunctionpublishByIds(@Param("ids")Long[] ids);

    /**
     * 查询函数库基础发布列表
     *
     * @param varAndFuncForEdit 函数库基础发布
     * @return 函数库基础发布集合
     */
    List<Functionpublish> selectFunctionpublishListForScriptEdit(VarAndFuncForEdit varAndFuncForEdit);
}
