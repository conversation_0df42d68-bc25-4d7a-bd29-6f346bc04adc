package com.ideal.script.mapper;

import com.ideal.script.model.entity.InfoVersion;
import com.ideal.script.model.entity.Stale;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface StaleScriptMapper {
    /**
     * 获取默认版本的脚本
     * @return 所有默认版本的脚本
     */
    List<InfoVersion> getData();

    /**
     * 根据infoId查询180天未修改报表数据
     * @param infoId infoId
     * @return 180天未修改报表数据实体类
     */
    Stale getStaleData(@Param("id") Long infoId);

    /**
     * 根据infoId删除180天未修改报表数据
     * @param infoId infoId
     * @return sql执行生效行数
     */
    int deleteStaleByInfoId(@Param("id") Long infoId);

    /**
     * 更新未修改天数
     * @param day 未修改天数
     * @param id 主键
     * @return sql执行生效行数
     */
    int updateStaleData(@Param("day") int day, @Param("id") Long id);

    /**
     * 插入180天未修改数据
     * @param stale 180天报表实体类
     * @return sql执行生效行数
     */
    int insertStaleData(Stale stale);
}
