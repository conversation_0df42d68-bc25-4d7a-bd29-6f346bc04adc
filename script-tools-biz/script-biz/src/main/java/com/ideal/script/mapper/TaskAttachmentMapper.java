package com.ideal.script.mapper;

import java.util.List;
import com.ideal.script.model.entity.TaskAttachment;
import org.apache.ibatis.annotations.Param;

/**
 * 脚本任务附件附Mapper接口
 * 
 * <AUTHOR>
 */
public interface TaskAttachmentMapper 
{
    /**
     * 查询脚本任务附件附
     * 
     * @param id 脚本任务附件附主键
     * @return 脚本任务附件附
     */
     TaskAttachment selectTaskAttachmentById(Long id);

    /**
     * 查询脚本任务附件附列表
     * 
     * @param taskAttachment 脚本任务附件附
     * @return 脚本任务附件附集合
     */
     List<TaskAttachment> selectTaskAttachmentList(TaskAttachment taskAttachment);

    /**
     * 新增脚本任务附件附
     * 
     * @param taskAttachment 脚本任务附件附
     * @return 结果
     */
     int insertTaskAttachment(TaskAttachment taskAttachment);

    /**
     * 修改脚本任务附件附
     * 
     * @param taskAttachment 脚本任务附件附
     * @return 结果
     */
     int updateTaskAttachment(TaskAttachment taskAttachment);

    /**
     * 删除脚本任务附件附
     * 
     * @param id 脚本任务附件附主键
     * @return 结果
     */
     int deleteTaskAttachmentById(Long id);

    /**
     * 批量删除脚本任务附件附
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
     int deleteTaskAttachmentByIds(@Param("ids")Long[] ids);

     /**
      * 双人复核业务详情页面-查询任务绑定的附件
      * @param serviceId 业务主键
      * @return List<TaskAttachment>
      */

    List<TaskAttachment> selectTaskAttachmentByServiceId(Long serviceId,Long taskId);

    /**
     * 双人复核业务详情页面-查询任务绑定的附件 不包含文件内容
     * @param serviceId 业务主键
     * @return List<TaskAttachment>
     */

    List<TaskAttachment> selectTaskAttachmentNoContentByServiceId(Long serviceId,Long taskId);

    /**
     * 根据任务ID 获取对应临时附件id
     * @param taskId 任务id
     * @return 附件id
     */
    List<Long> getIdsByTaskId(Long taskId);

    /**
     * 根据任务id更新为0值
     * @param taskId
     * @return
     */
    int updateTaskIdEmptyByTaskId(Long taskId);
}
