package com.ideal.script.filter;

import com.google.common.io.ByteStreams;

import javax.servlet.ReadListener;
import javax.servlet.ServletInputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletRequestWrapper;
import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;

/**
 * HttpServletRequest包装类，缓存body
 * <AUTHOR>
 */
public class CachedRequestWrapper extends HttpServletRequestWrapper {
    /**
     * 存储body数据的容器
     */
    private  byte[] body;

    public CachedRequestWrapper(HttpServletRequest request) throws IOException {
        super(request);
        body = ByteStreams.toByteArray(request.getInputStream());
    }

    /**
     * 获取请求Body
     *
     * @return String
     */
    public String getBodyString() {
        return new String(body, StandardCharsets.UTF_8);
    }

    @Override
    public BufferedReader getReader() {
        return new BufferedReader(new InputStreamReader(getInputStream()));
    }

    @Override
    public ServletInputStream getInputStream()   {

        final ByteArrayInputStream inputStream = new ByteArrayInputStream(body);

        return new ServletInputStream() {
            @Override
            public int read() {
                return inputStream.read();
            }

            @Override
            public boolean isFinished() {
                return false;
            }

            @Override
            public boolean isReady() {
                return false;
            }
            @Override
            public void setReadListener(ReadListener listener) {
                // 空
            }

        };
    }

    public void modifyBody(String bodyReq){
        this.body = bodyReq.getBytes(StandardCharsets.UTF_8);
    }
}
