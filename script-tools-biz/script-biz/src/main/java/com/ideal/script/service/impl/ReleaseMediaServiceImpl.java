package com.ideal.script.service.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ideal.common.util.BeanUtils;
import com.ideal.common.util.FilePathValidator;
import com.ideal.sc.util.CurrentUserUtil;
import com.ideal.sc.util.FileUtils;
import com.ideal.sc.util.ZipUtil;
import com.ideal.script.common.constant.Constants;
import com.ideal.script.common.constant.enums.Enums;
import com.ideal.script.common.constant.enums.ExceptionMessage;
import com.ideal.script.common.constant.enums.LogMessage;
import com.ideal.script.dto.AttachmentDto;
import com.ideal.script.dto.CategoryApiDto;
import com.ideal.script.dto.CategoryDto;
import com.ideal.script.dto.ScriptFileImportExportApiDto;
import com.ideal.script.dto.ScriptInfoDto;
import com.ideal.script.dto.ScriptVersionDto;
import com.ideal.script.exception.ScriptException;
import com.ideal.script.mapper.CategoryMapper;
import com.ideal.script.mapper.InfoMapper;
import com.ideal.script.mapper.InfoVersionMapper;
import com.ideal.script.model.bean.ReleaseMediaBean;
import com.ideal.script.model.dto.InfoVersionTextDto;
import com.ideal.script.model.dto.ParameterCheckDto;
import com.ideal.script.model.dto.ParameterDto;
import com.ideal.script.model.dto.ParameterManagerDto;
import com.ideal.script.model.dto.ToProductDto;
import com.ideal.script.model.dto.ToProductQueryDto;
import com.ideal.script.model.dto.ToProductRelationDto;
import com.ideal.script.model.entity.Attachment;
import com.ideal.script.model.entity.Category;
import com.ideal.script.model.entity.Info;
import com.ideal.script.model.entity.InfoVersion;
import com.ideal.script.model.entity.InfoVersionText;
import com.ideal.script.model.entity.Parameter;
import com.ideal.script.model.entity.ParameterCheck;
import com.ideal.script.model.entity.ParameterManager;
import com.ideal.script.service.IAttachmentService;
import com.ideal.script.service.ICategoryService;
import com.ideal.script.service.IInfoService;
import com.ideal.script.service.IInfoVersionService;
import com.ideal.script.service.IInfoVersionTextService;
import com.ideal.script.service.IMyScriptService;
import com.ideal.script.service.IParameterCheckService;
import com.ideal.script.service.IParameterManagerService;
import com.ideal.script.service.IParameterService;
import com.ideal.script.service.IReleaseMediaService;
import com.ideal.script.service.IToProductRelationService;
import com.ideal.script.service.IToProductService;
import com.ideal.system.api.IUserInfo;
import com.ideal.system.common.component.model.CurrentUser;
import com.ideal.system.dto.UserInfoApiDto;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static org.apache.commons.lang3.time.DateFormatUtils.format;

/**
 * 服务投产业务类
 *
 * <AUTHOR>
 */
@Service
public class ReleaseMediaServiceImpl implements IReleaseMediaService {
    private static final Logger logger = LoggerFactory.getLogger(ReleaseMediaServiceImpl.class);
    private final IMyScriptService myScriptService;

    private final IParameterService parameterService;

    private final IParameterCheckService parameterCheckService;

    private final IParameterManagerService parameterManagerService;

    private final IAttachmentService attachmentService;
    private final IInfoVersionService infoVersionService;
    private final IInfoVersionTextService infoVersionTextService;

    private final IInfoService infoService;

    private final ICategoryService categoryService;
    private final InfoMapper infoMapper;

    private final IToProductService toProductService;
    private final IToProductRelationService toProductRelationService;

    private final CategoryMapper categoryMapper;
    private final InfoVersionMapper infoVersionMapper;
    private final IUserInfo userInfoApi;
    private final ObjectMapper objectMapper;


    public ReleaseMediaServiceImpl(IMyScriptService myScriptService, IParameterService parameterService, IParameterCheckService parameterCheckService, IParameterManagerService parameterManagerService, IAttachmentService attachmentService, IInfoVersionService infoVersionService, IInfoVersionTextService infoVersionTextService, IInfoService infoService, ICategoryService categoryService, InfoMapper infoMapper, @Lazy IToProductService toProductService, IToProductRelationService toProductRelationService, CategoryMapper categoryMapper, InfoVersionMapper infoVersionMapper, IUserInfo userInfoApi, ObjectMapper objectMapper) {
        this.myScriptService = myScriptService;
        this.parameterService = parameterService;
        this.parameterCheckService = parameterCheckService;
        this.parameterManagerService = parameterManagerService;
        this.attachmentService = attachmentService;
        this.infoVersionService = infoVersionService;
        this.infoVersionTextService = infoVersionTextService;
        this.infoService = infoService;
        this.categoryService = categoryService;
        this.infoMapper = infoMapper;
        this.toProductService = toProductService;
        this.toProductRelationService = toProductRelationService;
        this.categoryMapper = categoryMapper;
        this.infoVersionMapper = infoVersionMapper;
        this.userInfoApi = userInfoApi;
        this.objectMapper = objectMapper;
    }

    /**
     * 生成压缩文件，并返回文件数据
     * @param ids   版本id
     * @return   Map
     * @throws ScriptException  脚本自定义异常
     */
    @Override
    public Map<String,Object> scriptZipFile(Long[] ids) throws ScriptException{
        Map<String,Object> res = new HashMap<>(3);
        File sourceFolder = null;
        File zipFile = null;
        try {
            // 创建一个文件夹来存放各个版本的导出投产介质（脚本json文件，json文件存放脚本参数、参数校验规则、基础信息、分类等，附件生成附件文件）
            String currentDir = System.getProperty("user.dir");
            String targetPath = currentDir + "/work/";
            String path = targetPath + "temp_exportReleaseMedia_" + format(new Date(), "yyyyMMddHHmmssms");
            // 创建一个文件夹用来放置脚本文件及附件
            sourceFolder = FileUtils.createFile(path);
            // 创建投产介质导出文件所需基础信息
            createReleaseMediaFile(ids, path);
            String zipName = "投产介质_" + format(new Date(), "yyyyMMddHHmmssms") + ".zip";
            zipFile = FileUtils.createZipFile(sourceFolder);
            res.put(Enums.ProductionFile.FILENAME.getValue(),zipFile);
            res.put(Enums.ProductionFile.SOURCEFOLDER.getValue(), sourceFolder);
            res.put(Enums.ProductionFile.ZIPNAME.getValue(), zipName);
        }catch (Exception e){
            logger.error("scriptZipFile fail", e);
            throw new ScriptException("error.clean.tempFile");
        }
        return res;
    }

    /**
     * 投产介质导出
     *
     * @param ids      版本id
     * @param response 响应
     */
    @Override
    public void exportReleaseMedia(Long[] ids, HttpServletResponse response) throws ScriptException {
        File sourceFolder = null;
        File zipFile = null;
        try {
            Map<String,Object> map = scriptZipFile(ids);
            sourceFolder = (File) map.get("sourceFolder");
            zipFile = (File) map.get("zipFile");
            String zipName = (String) map.get("zipName");
            //将文件输出到浏览器
            FileUtils.downloadFileToBrowser(zipFile, response, zipName);
        } catch (Exception e) {
            logger.error("exportReleaseMedia exception:", e);
            throw new ScriptException("exportReleaseMedia.error");
        } finally {
            // 删除临时文件夹和压缩文件
            cleanupFiles(zipFile, sourceFolder);
        }
    }

    public boolean isFormalReleaseMediaFile(File zipFile) {
        return zipFile.getName().startsWith(Enums.PrefixEnum.PRODUCTION_PREFIX.getPrefix()) && zipFile.getName().endsWith(Enums.FileExtensionEnum.ZIP.getValue());
    }

    /**
     * 导入投产介质
     *
     * @param file              投产介质文件
     * @param user              用户
     * @param toProductQueryDto 包含所属人、投产描述
     * @throws ScriptException 抛出自定义通知异常
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void importReleaseMedia(MultipartFile file, CurrentUser user, ToProductQueryDto toProductQueryDto) throws ScriptException {
        File zipFile = null;

        if (file == null || file.getOriginalFilename() == null) {
            throw new ScriptException("upload.file.empty");
        }
        checkFileName(file);

        try {
            // 临时目录
            String path = getProductFilePath();
            // 上载的文件落地到E:\2023\workspace\ieai-script-service\script-starter\target\classes\importReleaseMedia目录下，生成一个投产介质.zip文件。
            zipFile = FileUtils.convertMultipartFileToFile(file, path);
            saveFileProductData(zipFile,toProductQueryDto,user);
        }   catch (Exception e) {
            logger.error("importReleaseMedia is error:", e);
            if("import.release.media.same.script".equals(e.getMessage())){
                throw new ScriptException(e.getMessage());
            }
            throw new ScriptException(ExceptionMessage.IMPORT_RELEASE_MEDIA_ERROR.getValue());
        }
    }

    /**
     * 获取临时文件路径
     * @return  文件路径
     */
    @Override
    public String getProductFilePath(){
        String currentDir = System.getProperty("user.dir");

        String targetPath = currentDir + "/work/";
        // 临时目录
        return targetPath + "importReleaseMedia";
    }

    private void checkFileName(MultipartFile file) throws ScriptException {
        String fileName = file.getOriginalFilename();
        if (null == fileName) {
            logger.error("File name is null");
            return;
        }
        if (!(fileName.startsWith(Enums.PrefixEnum.PRODUCTION_PREFIX.getPrefix()) && fileName.endsWith(Enums.FileExtensionEnum.ZIP.getValue()))) {
            throw new ScriptException("file.format.error");
        }
    }

    /**
     * zip文件解压、入库
     * @param zipFile   压缩文件
     * @param toProductQueryDto ieai_script_to_product对应dto
     * @param user  用户
     * @throws ScriptException  脚本自定义异常
     */
    private void saveFileProductData(File zipFile,ToProductQueryDto toProductQueryDto,CurrentUser user) throws ScriptException{
        File outputDirectory = null;
        try {
            if (isFormalReleaseMediaFile(zipFile)) {
                String zipOutputDirectory = zipFile.getAbsolutePath().replace(".zip", "");
                // 将zip压缩文件解压到 同名的文件夹下（d:/aa/d.zip 解压到d:/aa/d/a.json 和附件文件夹）
                ZipUtil.upZipFile(zipFile.getAbsolutePath(), zipOutputDirectory);
                outputDirectory = new File(zipOutputDirectory);
                // 预存版本uuid
                List<String> srcScriptUuidList = new ArrayList<>();
                toProductQueryDto.setSrcScriptUuidList(srcScriptUuidList);
                handlerImportMedia(outputDirectory, toProductQueryDto);
            }
            // 设置文件名
            toProductQueryDto.setFileName(zipFile.getName());
            // 设置投产人
            toProductQueryDto.setUserId(user.getId());
            toProductQueryDto.setUserName(user.getFullName());
            // 记录投产记录，服务投产的数据默认是审批完成状态
            toProductQueryDto.setProductState(1);
            saveProductInfo(toProductQueryDto);
        } catch (Exception e) {
            logger.error("importReleaseMedia is error:", e);
            throw new ScriptException(e.getMessage());
        } finally {
            // 删除临时文件夹和压缩文件
            cleanupFiles(zipFile, outputDirectory);
        }

    }

    /**
     * 接口，服务投产入口
     * @param scriptFileImportExportApiDto 脚本的dto参数
     * @return  Map
     */
    @Override
    public Map<String,String> importScriptProduction(ScriptFileImportExportApiDto scriptFileImportExportApiDto) {
        //创建返回map，并赋默认值
        Map<String,String> res = new HashMap<>(2);
        res.put("state","fail");
        res.put("message","import file fail");
        //获取文件参数
        byte[] fileByte = (byte[]) scriptFileImportExportApiDto.getFileContentByte();
        String fileName = scriptFileImportExportApiDto.getFileName();
        String contentType = scriptFileImportExportApiDto.getFileSuffix();
        //定义文件
        File zipFile = null;
        //定义需要的参数
        CurrentUser user = CurrentUserUtil.getCurrentUser();
        ToProductQueryDto toProductQueryDto = new ToProductQueryDto();
        try {
            toProductQueryDto.setScriptUserId(user.getId());
            toProductQueryDto.setScriptUserName(user.getLoginName());
            toProductQueryDto.setDescription("接口服务投产");
            // 创建文件到临时目录
            String path = getProductFilePath();
            //针对目录文件不存在的情况首先创建文件
            FileUtils.createFile(path);
            //在上一步成功创建了目录的情况下创建文件
            zipFile = byteArrayToFile(fileByte,path + "/" + fileName + "." + contentType);
            if(null == zipFile){
                return res;
            }
            //校验文件名字，必须是服务投产开头，zip结尾
            checkFileName(zipFile);
            // 上载的文件落地到E:\2023\workspace\ieai-script-service\script-starter\target\classes\importReleaseMedia目录下，生成一个投产介质.zip文件。
            saveFileProductData(zipFile,toProductQueryDto,user);
            res.put("state","success");
            res.put("message","import file success");
        } catch (ScriptException e) {
            res.put("message",e.getMessage());
            logger.error("importScriptProduction is error:", e);
        }
        return res;
    }

    /**
     * byte数组转换成file文件
     * @param data  数据
     * @param path  路径
     * @return  文件
     */
    private File byteArrayToFile(byte[] data , String path) throws ScriptException {
        if (FilePathValidator.apply(path)) {
            File file = new File(path);
            try (FileOutputStream fos = new FileOutputStream(file)){
                fos.write(data);
                fos.flush();
            } catch (IOException e) {
                logger.error("byteArrayToFile byteArrayToFile is error : ",e);
                return null;
            }
            return file;
        }else{
            throw new ScriptException("Invalid path : " + path);
        }
    }

    /**
     * 校验File类型服务投产文件的名称是否复核规范
     * @param file  文件
     * @throws ScriptException  脚本服务化自定义异常
     */
    private void checkFileName(File file) throws ScriptException {
        String fileName = file.getName();
        if (null == fileName) {
            return;
        }
        if (!(fileName.startsWith(Enums.PrefixEnum.PRODUCTION_PREFIX.getPrefix()) && fileName.endsWith(Enums.FileExtensionEnum.ZIP.getValue()))) {
            throw new ScriptException("file.format.error");
        }
    }

    /**
     * 投产记录表入库
     *
     * @param toProductQueryDto 投产导入、包含投产描述
     * <AUTHOR>
     */
    @Override
    public void saveProductInfo(ToProductQueryDto toProductQueryDto) {
        if (null == toProductQueryDto) {
            logger.error("ToProductQueryDto is null");
            return;
        }
        if (null == toProductQueryDto.getSrcScriptUuidList()) {
            logger.error("ToProductQueryDto.getSrcScriptUuidList is null");
            return;
        }
        if (toProductQueryDto.getSrcScriptUuidList().isEmpty()) {
            logger.error("toProductQueryDto.getSrcScriptUuidList().isEmpty() is true");
            return;
        }
        // 存主表
        ToProductDto toProductDto = BeanUtils.copy(toProductQueryDto, ToProductDto.class);
        toProductService.insertToProduct(toProductDto);
        toProductQueryDto.setId(toProductDto.getId());
        // 存关系
        List<String> srcScriptUuidList = toProductQueryDto.getSrcScriptUuidList();
        for (String srcScriptUuid : srcScriptUuidList) {
            ToProductRelationDto toProductRelationDto = new ToProductRelationDto();
            toProductRelationDto.setScriptToproductId(toProductDto.getId());
            toProductRelationDto.setSrcScriptUuid(srcScriptUuid);
            toProductRelationDto.setCreateTime(new Timestamp(LocalDateTime.now().toInstant(ZoneOffset.of("+8")).toEpochMilli()));
            toProductRelationService.insertToProductRelation(toProductRelationDto);
        }

    }

    /**
     * 处理解压缩后的文件目录
     *
     * @param toProductQueryDto 包含所属人、投产描述
     * @param outputDirectory   加压缩后的文件
     */
    @Override
    public void handlerImportMedia(File outputDirectory, ToProductQueryDto toProductQueryDto) throws ScriptException {
        if (!outputDirectory.isDirectory()) {
            logger.error("outputDirectory is not a directory");
            return;
        }
        File[] tempFiles = outputDirectory.listFiles();
        if (tempFiles == null || tempFiles.length == 0) {
            logger.error("tempFiles is empty");
            return;
        }
        // 取得temp_exportReleaseMedia_开头的文件夹
        File tempFolder = tempFiles[0];
        // 取得temp_exportReleaseMedia_ 文件夹下的脚本文件夹目录集
        File[] scriptFiles = tempFolder.listFiles();
        if (null == scriptFiles) {
            logger.error("scriptFiles is null");
            return;
        }
        // 处理temp_exportReleaseMedia_下的每个脚本目录
        // 导入过程中，如果出现版本号重复的脚本，则暂停插入，待版本号没重复的脚本插入后，剩余的脚本基于脚本的最大版本号升级插入
        List<File> versionRepeatFiles = new ArrayList<>();
        for (File scriptFile : scriptFiles) {
            // 判断是否是脚本目录
            if (scriptFile.isDirectory()) {
                // 处理脚本目录
                if(!Constants.SCRIPT_FILE_DIR_NAME.equals(scriptFile.getName())){
                    handleMainFolder(scriptFile, toProductQueryDto);
                    //标识，导入的版本号如果重复，则记录该脚本
                    if(toProductQueryDto.isScriptVersionRepeat()){
                        //保存已存在版本号的脚本
                        versionRepeatFiles.add(scriptFile);
                        //重置版本号重复标识
                        toProductQueryDto.setScriptVersionRepeat(false);
                    }
                }
            }
        }

        //list中的脚本都是校验出的重复版本号脚本，此时重新执行服务投产
        if(!versionRepeatFiles.isEmpty()){
            toProductQueryDto.setScriptVersionCheckFlag(false);
            for (File scriptFile : versionRepeatFiles) {
                if(!Constants.SCRIPT_FILE_DIR_NAME.equals(scriptFile.getName())){
                    handleMainFolder(scriptFile, toProductQueryDto);
                }
            }
        }
    }

    /**
     * 处理脚本目录
     *
     * @param toProductQueryDto 包含所属人、投产描述
     * @param folder            脚本目录
     */
    public void handleMainFolder(File folder, ToProductQueryDto toProductQueryDto) throws ScriptException {
        if (!folder.isDirectory()) {
            return;
        }
        // 处理每个脚本的文件夹
        handleScriptFolder(folder, toProductQueryDto);
    }

    /**
     * 处理子目录中的json文件以及附件文件夹
     *
     * @param toProductQueryDto 包含所属人、投产描述
     * @param subFolder         目录文件夹
     */
    private void handleScriptFolder(File subFolder, ToProductQueryDto toProductQueryDto) throws ScriptException {
        // 获取子目录中的json文件
        File[] jsonFiles = subFolder.listFiles((dir, name) -> name.toLowerCase().endsWith(".json"));
        if (null == jsonFiles) {
            return;
        }

        // 处理json文件内容
        ReleaseMediaBean releaseMediaBean = handleJsonFile(jsonFiles[0], toProductQueryDto);
        //判断，如果导入的脚本版本号重复，则不继续向下执行
        if(toProductQueryDto.isScriptVersionRepeat()){
            return;
        }

        if (null == releaseMediaBean) {
            return;
        }

        if (Boolean.TRUE.equals(releaseMediaBean.getSrcScriptUuidExist())) {
            //数据库中有直接跳过
            return;
        }
        // 将版本uuid放入集合
        toProductQueryDto.getSrcScriptUuidList().add(releaseMediaBean.getInfoVersion().getSrcScriptUuid());

        // 获取子目录中以 "_attachment" 结尾的文件夹
        File[] attachmentFolders = new File[0];
        try {
            attachmentFolders = subFolder.listFiles(file -> file.isDirectory() && file.getName().endsWith("_attachment"));
        } catch (Exception e) {
            logger.error("handleScriptFolder is error:", e);
        }
        if (null == attachmentFolders) {
            return;
        }
        if (attachmentFolders.length == 0) {
            return;
        }
        // 处理附件
        handleAttachmentsFolder(attachmentFolders[0], releaseMediaBean,true);
    }


    /**
     * 解析并处理脚本json文件
     *
     * @param toProductQueryDto 包含所属人、投产描述
     * @param jsonFile          脚本json文件
     */
    @Override
    public ReleaseMediaBean handleJsonFile(File jsonFile, ToProductQueryDto toProductQueryDto) throws ScriptException {
        // 解析并处理 JSON 文件
        logger.info("begin handel jsonFile，fileName:{}", jsonFile.getName());
        ReleaseMediaBean releaseMediaBean;
        // 从 JSON 文件中读取内容，并将其转换为 ReleaseMediaBean 对象
        try (InputStream inputStream = Files.newInputStream(jsonFile.toPath());

             InputStreamReader reader = new InputStreamReader(inputStream, StandardCharsets.UTF_8)) {

            releaseMediaBean = objectMapper.readValue(reader, ReleaseMediaBean.class);

            // 所属人设置(选择了所属人会有值)
            releaseMediaBean.setCreatorName(toProductQueryDto.getScriptUserName());
            releaseMediaBean.setCreatorId(toProductQueryDto.getScriptUserId());
            ToProductQueryDto tempToProductQueryDto = BeanUtils.copy(toProductQueryDto, ToProductQueryDto.class);
            //如果没有选择归属人那就只用json中的info中的创建用户名称和创建用户id(version ,version_text可以用这一个)
            if(tempToProductQueryDto.getScriptUserId() == null || tempToProductQueryDto.getScriptUserId() == 0){
                releaseMediaBean.setCreatorName(releaseMediaBean.getInfoDto().getCreatorName());
                releaseMediaBean.setCreatorId(releaseMediaBean.getInfoDto().getCreatorId());
            }

            logger.info("投产介质srcScriptUuid:{}", releaseMediaBean.getInfoVersion().getSrcScriptUuid());
            Boolean srcScriptUuidExist = infoVersionService.validSrcScriptUuidExist(releaseMediaBean.getInfoVersion().getSrcScriptUuid());
            // 赋值版本是否存在的信息
            releaseMediaBean.setSrcScriptUuidExist(srcScriptUuidExist);
            if (Boolean.TRUE.equals(srcScriptUuidExist)) {
//                throw new ScriptException("import.release.media.same.script");
                logger.error("相同uuid的脚本不再导入，uuid为：{}", releaseMediaBean.getInfoVersion().getSrcScriptUuid());
                return releaseMediaBean;
            }
            // 存储投产介质
            storeReleaseMedia(releaseMediaBean,tempToProductQueryDto);
            //版本好相关参数赋值
            toProductQueryDto.setScriptVersionRepeat(tempToProductQueryDto.isScriptVersionRepeat());
            toProductQueryDto.setScriptVersionCheckFlag(tempToProductQueryDto.isScriptVersionCheckFlag());
            toProductQueryDto.setVersionMap(tempToProductQueryDto.getVersionMap());
        }catch(ScriptException e){
            throw new ScriptException(e.getMessage());
        }catch (Exception e) {
            logger.error("handleJsonFile error:", e);
            throw new ScriptException("handle.json.file.error");
        }
        logger.info("Successfully read and parsed JSON file");

        return releaseMediaBean;
    }

    /**
     * 存储投产介质，将ReleaseMediaBean对象存储到特定的介质中
     *
     * @param releaseMediaBean 投产介质
     */

    public void storeReleaseMedia(ReleaseMediaBean releaseMediaBean,ToProductQueryDto toProductQueryDto) throws ScriptException {
        try {
            if (null == releaseMediaBean) {
                logger.info("releaseMediaBean is null!");
                return;
            }
            if (null == releaseMediaBean.getInfoDto()) {
                logger.info("releaseMediaBean.getInfo() is null!");
                return;
            }


            String scriptNameZh = releaseMediaBean.getInfoDto().getScriptNameZh();
            // 根据脚本中文名判断是否有，有的话就用库里的uniqueUuid，没有的话用json文件里面的uniqueUuid
            Boolean scriptNameZhExist = infoService.validScriptNameZhCountExist(scriptNameZh);

            //服务投产设置脚本部门使用归属人的部门id
            List<String> nameList = new ArrayList<>();
            nameList.add(releaseMediaBean.getCreatorName());
            //这里是根据登陆名校验(loginName)
            List<UserInfoApiDto> userInfoList = userInfoApi.queryUserInfoListByUserName(nameList);
            //如果用户不存在不能进行导入
            if(userInfoList == null || userInfoList.isEmpty()){
                throw new ScriptException("createUser.not.exist");
            }
            toProductQueryDto.setScriptUserId(userInfoList.get(0).getId());
            toProductQueryDto.setScriptUserName(userInfoList.get(0).getFullName());
            //构建创建用户信息
            buildReleaseMediaBean(releaseMediaBean,userInfoList);


            if (Boolean.TRUE.equals(scriptNameZhExist)) {
                Info info = new Info();
                info.setScriptNameZh(scriptNameZh);
                List<Info> infoList = infoMapper.selectInfoList(info);

                if (null != infoList.get(0)) {
                    updateInfoCreateUser(releaseMediaBean, infoList.get(0));
                    releaseMediaBean.getInfoDto().setId(infoList.get(0).getId());
                    releaseMediaBean.getInfoDto().setUniqueUuid(infoList.get(0).getUniqueUuid());
                    releaseMediaBean.getInfoVersion().setInfoUniqueUuid(infoList.get(0).getUniqueUuid());
                }
            } else {
                // 存储基础信息
                saveInfo(releaseMediaBean);
            }
            // 处理脚本分类
            Long categoryId = processCategory(releaseMediaBean.getInfoDto().getCategoryDto(), null,userInfoList);

            //计算版本号
            if(calculateScriptVersion(releaseMediaBean,toProductQueryDto)){
                return;
            }

            // 更新基础信息绑定的分类Id
            updateCategoryId(releaseMediaBean, categoryId);

            // 存储版本信息
            saveInfoVersion(releaseMediaBean);

            // 存储版本内容
            saveInfoVersionText(releaseMediaBean);

            // 存储参数校验规则
            Map<Long, Long> parametersCheckMap = saveParametersCheck(releaseMediaBean);
            //存储枚举参数
            Map<Long,Long> enumParamMap = saveParameterManager(releaseMediaBean);

            // 存储参数信息
            saveParameters(releaseMediaBean, parametersCheckMap,enumParamMap);
        }catch(ScriptException e){
            logger.error("storeReleaseMedia found error:",e);
            throw new ScriptException(e.getMessage());
        }catch (Exception e) {
            logger.error("storeReleaseMedia error:", e);
            throw new ScriptException(ExceptionMessage.IMPORT_RELEASE_MEDIA_ERROR.getValue());
        }

    }

    /**
     * 计算脚本版本号
     * @param releaseMediaBean 参数bean
     * @param toProductQueryDto 参数dto
     * @return 是否版本号重复标识
     */
    private boolean calculateScriptVersion(ReleaseMediaBean releaseMediaBean,ToProductQueryDto toProductQueryDto){
        //统一单位，v8导入初始版本为1，改为1.0
        if("1".equals(releaseMediaBean.getInfoVersion().getVersion())){
            releaseMediaBean.getInfoVersion().setVersion("1.0");
        }
        //根据uniqueUuid获取脚本所有的版本号
        //如果库中没有此版本号则正常导入此版本
        //如果库中已经存在此版本号，则待不重复版本号的脚本导入后，基于最大版本升级
        //例如，根据uniqueUuid查询到了1.0-1.5的脚本，此时导入的版本号如果是1.2-1.6，那么此时不导入脚本，待版本号不重复的脚本都导入后再导入重复的，版本号赋值为1.7
        //定义版本存储map，key为脚本的uniqueUuid，value是脚本各个版本的版本号List集合
        Map<String,List<String>> versionMap = toProductQueryDto.getVersionMap();
        //如果当前map中不存在此脚本的任何版本号，那么就去数据库里查询，将查询到的结果保存到map中
        if(Objects.isNull(versionMap.get(releaseMediaBean.getInfoDto().getUniqueUuid()))){
            List<String> allVersionByUniqueUuidList = infoVersionMapper.getAllVersionByUniqueUuid(releaseMediaBean.getInfoDto().getUniqueUuid());
            if(ObjectUtils.notEqual(allVersionByUniqueUuidList,null)){
                versionMap.put(releaseMediaBean.getInfoDto().getUniqueUuid(),allVersionByUniqueUuidList);
            }
        }
        //获取当前脚本的所有版本号
        List<String> scriptVersionList = versionMap.get(releaseMediaBean.getInfoDto().getUniqueUuid());
        //校验，如果版本号不为空，比对当前版本号是否与已存在的版本号冲突
        if(!Objects.isNull(scriptVersionList) && !scriptVersionList.isEmpty()){
            //如果走到（!toProductQueryDto.isScriptVersionCheckFlag()）的判断里，说明执行的是重复版本号脚本导入的操作
            //此时版本号不重复的脚本已经全部导入，重复版本的脚本的版本号都不能再用，要基于最大版本号升级
            if(!toProductQueryDto.isScriptVersionCheckFlag()){
                //获取脚本当前的最大版本号
                String maxVersion = scriptVersionList.stream()
                        .max(Comparator.comparingDouble(Double::parseDouble))
                        .orElse(null);
                BigDecimal value;
                //基于最大版本号升级
                if (StringUtils.isNotBlank(maxVersion)) {
                    value = new BigDecimal(maxVersion);
                    value = value.add(new BigDecimal("0.1"));
                }else{
                    value = new BigDecimal("1.0");
                }
                //赋值
                releaseMediaBean.getInfoVersion().setVersion(value.toString());
                //保存新生成的版本号
                scriptVersionList.add(value.toString());
                versionMap.put(releaseMediaBean.getInfoDto().getUniqueUuid(),scriptVersionList);
                toProductQueryDto.setVersionMap(versionMap);
            //如果脚本已经存在当前导入的版本号，则基于当前版本号升级，否则使用导入的版本号
            }else if(scriptVersionList.contains(releaseMediaBean.getInfoVersion().getVersion())){
                //如果在已有版本号数据中找到了要导入的版本号，则此时不插入脚本直接返回
                toProductQueryDto.setScriptVersionRepeat(true);
                return true;
            }else{
                //保存新生成的版本号
                scriptVersionList.add(releaseMediaBean.getInfoVersion().getVersion());
                versionMap.put(releaseMediaBean.getInfoDto().getUniqueUuid(),scriptVersionList);
                toProductQueryDto.setVersionMap(versionMap);
            }
        }else{
            //如果版本号为空，说明数据库中没有这个脚本的任何版本，同时其它版本也还没有导入,使用本身的版本号
            //保存新生成的版本号
            scriptVersionList = new ArrayList<>();
            scriptVersionList.add(releaseMediaBean.getInfoVersion().getVersion());
            versionMap.put(releaseMediaBean.getInfoDto().getUniqueUuid(),scriptVersionList);
            toProductQueryDto.setVersionMap(versionMap);
        }
        return false;
    }

    /**
     * 修改主表修改人为投产导入时选择的所属人
     *
     * @param releaseMediaBean 包含所属人姓名、所属人Id
     * @param info             主表对象
     */
    private void updateInfoCreateUser(ReleaseMediaBean releaseMediaBean, Info info) {
        Info info1 = new Info();
        info1.setId(info.getId());
        info1.setCreatorId(releaseMediaBean.getCreatorId());
        info1.setCreatorName(releaseMediaBean.getCreatorName());
        infoMapper.updateInfo(info1);
    }

    /**
     * 脚本绑定分类Id
     *
     * @param releaseMediaBean 投产介质
     * @param categoryId       分类Id
     * <AUTHOR>
     */
    public void updateCategoryId(ReleaseMediaBean releaseMediaBean, Long categoryId) {
        ScriptInfoDto infoDto = releaseMediaBean.getInfoDto();
        infoDto.setCategoryId(categoryId);
        infoService.updateInfo(infoDto);
    }


    /**
     * 递归遍历并处理分类信息
     *
     * @param categoryDto 分类
     * @param parentId 父分类Id
     * @return {@link Long } 返回最后一层分类Id
     * <AUTHOR>
     */
    public Long processCategory(CategoryDto categoryDto, Long parentId,List<UserInfoApiDto> userInfoList) throws ScriptException {
        // 查询是否存在当前分类
        CategoryDto existingCategory = categoryService.findByLevelAndNameAndParentId(categoryDto.getLevel(), categoryDto.getName(), parentId);
        Long categoryId;
        if (null == existingCategory.getId()) {
            // 不存在则创建新分类
            // 设置父类别ID
            categoryDto.setParentId(parentId);
            // 重置Id，不然保存的时候就用传过来的了
            categoryDto.setId(null);
            categoryDto.setCreatorId(userInfoList.get(0).getId());
            categoryDto.setCreatorName(userInfoList.get(0).getFullName());
            categoryDto.setUpdatorId(userInfoList.get(0).getId());
            categoryDto.setUpdatorName(userInfoList.get(0).getFullName());
            Category category = BeanUtils.copy(categoryDto, Category.class);
            categoryMapper.insertCategory(category);
            // 获取新插入分类的ID
            categoryId = category.getId();
        } else {
            // 如果分类已存在
            categoryId = existingCategory.getId();
        }
        // 递归处理子分类，将当前分类的ID作为父类别ID传入
        if (categoryDto.getChildren() != null) {
            List<Long> childIds = new ArrayList<>();
            for (CategoryApiDto child : categoryDto.getChildren()) {
                Long childId = processCategory(BeanUtils.copy(child, CategoryDto.class), categoryId,userInfoList);
                childIds.add(childId);
            }
            // 返回最后一个层级子分类的ID
            return childIds.get(childIds.size() - 1);
        }
        return categoryId;
    }


    /**
     * 存储版本内容信息
     *
     * @param releaseMediaBean 投产介质
     */
    public void saveInfoVersionText(ReleaseMediaBean releaseMediaBean) {
        releaseMediaBean.getInfoVersionText().setId(null);
        infoVersionTextService.insertInfoVersionText(BeanUtils.copy(releaseMediaBean.getInfoVersionText(), InfoVersionTextDto.class));
    }

    /**
     * 存储参数，同时记录旧、新id.
     *
     * @param releaseMediaBean 投产介质
     * @return Map<Long>
     */
    public Map<Long, Long> saveParametersCheck(ReleaseMediaBean releaseMediaBean) throws ScriptException {
        Map<Long, Long> map = new HashMap<>(releaseMediaBean.getParameterCheckList().size() * 2);
        List<ParameterCheck> parameterCheckList = releaseMediaBean.getParameterCheckList();
        for (ParameterCheck parameterCheck : parameterCheckList) {

            // 判断校验规则是否存在
            Boolean parameterCheckExist = parameterCheckService.validParamterCheckExist(parameterCheck.getRuleName());
            ParameterCheckDto parameterCheckDto;
            if (Boolean.TRUE.equals(parameterCheckExist)) {
                parameterCheckDto = parameterCheckService.selectParameterCheckByName(parameterCheck.getRuleName());
            } else {
                parameterCheckDto = BeanUtils.copy(parameterCheck, ParameterCheckDto.class);
                parameterCheckDto.setId(null);
                parameterCheckService.insertParameterCheck(parameterCheckDto);
            }
            map.put(parameterCheck.getId(), parameterCheckDto.getId());
        }
        return map;
    }

    /**
     * 存储枚举参数
     * @param releaseMediaBean  服务投产信息
     * @return  map
     * @throws ScriptException  脚本异常
     */
    public Map<Long,Long> saveParameterManager(ReleaseMediaBean releaseMediaBean) throws ScriptException {
        Map<Long, Long> map = new HashMap<>(releaseMediaBean.getParameterCheckList().size() * 2);
        List<ParameterManager> parameterManagerList = releaseMediaBean.getParameterManagerList();
        if(parameterManagerList == null || parameterManagerList.isEmpty()){
            return map;
        }
        for (ParameterManager parameterManager : parameterManagerList) {

            // 判断校验规则是否存在
            Boolean parameterCheckExist = parameterManagerService.validParamterCheckExist(parameterManager.getParamName());
            ParameterManagerDto parameterManagerDto;
            if (Boolean.TRUE.equals(parameterCheckExist)) {
                parameterManagerDto = parameterManagerService.selectParameterManagerByName(parameterManager.getParamName());
            } else {
                parameterManagerDto = BeanUtils.copy(parameterManager, ParameterManagerDto.class);
                parameterManagerDto.setId(null);
                parameterManagerService.insertParameterManager(parameterManagerDto);
            }
            map.put(parameterManager.getId(), parameterManagerDto.getId());
        }
        return map;
    }

    /**
     * 存储参数信息
     *
     * @param releaseMediaBean 投产介质
     * @param map              存储旧、新校验规则id的map
     * <AUTHOR>
     */
    public void saveParameters(ReleaseMediaBean releaseMediaBean, Map<Long, Long> map,Map<Long, Long> enumParamMap) {
        List<Parameter> parameterList = releaseMediaBean.getParameters();
        // 遍历参数列表，替换旧的ID为新的ID
        for (Parameter parameter : parameterList) {
            //替换校验规则id
            Long oldParamCheckIid = parameter.getParamCheckIid();
            Long newParamCheckId = map.get(oldParamCheckIid);
            //设置参数校验规则id
            if (newParamCheckId != null) {
                parameter.setParamCheckIid(newParamCheckId);
            }
            //替换枚举参数id
            Long oldEnumId = parameter.getScriptParameterManagerId();
            Long newEnumId = enumParamMap.get(oldEnumId);
            //设置枚举参数id
            if (newEnumId != null) {
                parameter.setScriptParameterManagerId(newEnumId);
            }
            parameter.setId(null);
            // 存储参数
            parameterService.insertParameter(BeanUtils.copy(parameter, ParameterDto.class));
        }
    }

    /**
     * 存储版本信息
     *
     * @param releaseMediaBean 投产介质
     */
    public void saveInfoVersion(ReleaseMediaBean releaseMediaBean) {

        if (null == releaseMediaBean.getInfoVersion()) {
            logger.info("没有版本数据！");
            return;
        }

        if (null != releaseMediaBean.getInfoVersion()) {

            // 重置Id
            releaseMediaBean.getInfoVersion().setId(null);


            //根据uniqueUuid查询默认的脚本
            InfoVersion infoVersion = infoVersionService.getInfoDefaultVersionByUniqueUuid(releaseMediaBean.getInfoDto().getUniqueUuid());
            //判断当前脚本是不是默认脚本，是则清除之前的脚本默认属性，不是则看库中有没有默认脚本。库中有，则不动，库中没有，当前这个设置为默认脚本

            if(releaseMediaBean.getInfoVersion().getIsDefault() != 1){
                //当前不是默认，库中不存在默认，将当前设置为默认
                if(infoVersion == null){
                    //默认版本
                    releaseMediaBean.getInfoVersion().setIsDefault(1);
                }else{
                    //当前不是默认，库中存在默认，取当前
                    releaseMediaBean.getInfoVersion().setIsDefault(releaseMediaBean.getInfoVersion().getIsDefault());

                }
            }else{
                //当前的是默认，库中有默认，不用动
                if(infoVersion != null){
                    // 版本直接发布，设置成默认版本，其它版本上的以前版本清空
                    InfoVersion infoVersionForUpdate = new InfoVersion();
                    infoVersionForUpdate.setInfoUniqueUuid(releaseMediaBean.getInfoDto().getUniqueUuid());
                    infoVersionForUpdate.setIsDefault(0);
                    infoVersionService.updateInfoVersionDefaultValue(infoVersionForUpdate);

                    //默认版本
                    releaseMediaBean.getInfoVersion().setIsDefault(1);
                }else{
                    //当前是默认，库中没有默认，取当前
                    releaseMediaBean.getInfoVersion().setIsDefault(releaseMediaBean.getInfoVersion().getIsDefault());
                }
            }

            releaseMediaBean.getInfoVersion().setEditState(1);
            releaseMediaBean.getInfoVersion().setUseState(releaseMediaBean.getInfoVersion().getUseState());
            ScriptVersionDto infoVersionDto = BeanUtils.copy(releaseMediaBean.getInfoVersion(), ScriptVersionDto.class);
            infoVersionService.insertInfoVersion(infoVersionDto);
            releaseMediaBean.getInfoVersion().setId(infoVersionDto.getId());
            releaseMediaBean.getInfoVersionText().setSrcScriptUuid(infoVersionDto.getSrcScriptUuid());


        }
    }

    /**
     * 存储脚本主表信息
     *
     * @param releaseMediaBean 投产介质
     */
    public void saveInfo(ReleaseMediaBean releaseMediaBean) {
        try {
            // 重置id为空
            releaseMediaBean.getInfoDto().setId(null);
            ScriptInfoDto infoDto = BeanUtils.copy(releaseMediaBean.getInfoDto(), ScriptInfoDto.class);
            infoService.insertInfo(infoDto);
            releaseMediaBean.getInfoDto().setId(infoDto.getId());
            releaseMediaBean.getInfoDto().setUniqueUuid(infoDto.getUniqueUuid());
            releaseMediaBean.getInfoVersion().setInfoUniqueUuid(infoDto.getUniqueUuid());
        } catch (Exception e) {
            logger.error("saveInfo error:", e);
        }
    }

    /**
     * 服务投产导入构建创建用户信息
     * @param releaseMediaBean  导入信息
     * @param userInfoList  用户列表
     */
    private void buildReleaseMediaBean(ReleaseMediaBean releaseMediaBean,List<UserInfoApiDto> userInfoList){
        releaseMediaBean.setCreatorId(userInfoList.get(0).getId());
        //这里还是用loginName,下面涉及到更新表用fullName
        releaseMediaBean.setCreatorName(userInfoList.get(0).getLoginName());
        //info
        if(releaseMediaBean.getInfoDto() != null) {
            releaseMediaBean.getInfoDto().setCreatorId(userInfoList.get(0).getId());
            releaseMediaBean.getInfoDto().setCreatorName(userInfoList.get(0).getFullName());
            releaseMediaBean.getInfoDto().setUpdatorId(userInfoList.get(0).getId());
            releaseMediaBean.getInfoDto().setUpdatorName(userInfoList.get(0).getFullName());
            releaseMediaBean.getInfoDto().setOrgCode(userInfoList.get(0).getOrgCode());
        }
        //version
        if(releaseMediaBean.getInfoVersion() != null) {
            releaseMediaBean.getInfoVersion().setCreatorId(userInfoList.get(0).getId());
            releaseMediaBean.getInfoVersion().setCreatorName(userInfoList.get(0).getFullName());
            releaseMediaBean.getInfoVersion().setUpdatorId(userInfoList.get(0).getId());
            releaseMediaBean.getInfoVersion().setUpdatorName(userInfoList.get(0).getFullName());
        }
        //version_text
        if(releaseMediaBean.getInfoVersionText() != null) {
            releaseMediaBean.getInfoVersionText().setCreatorId(userInfoList.get(0).getId());
            releaseMediaBean.getInfoVersionText().setCreatorName(userInfoList.get(0).getFullName());
        }
        //参数表
        if(releaseMediaBean.getParameters() != null){
            for (Parameter parameter : releaseMediaBean.getParameters()) {
                parameter.setCreatorName(userInfoList.get(0).getFullName());
                parameter.setCreatorId(userInfoList.get(0).getId());
            }
        }
        //参数验证表
        if(releaseMediaBean.getParameterCheckList() != null){
            for (ParameterCheck parameterCheck : releaseMediaBean.getParameterCheckList()) {
                parameterCheck.setCreatorId(userInfoList.get(0).getId());
                parameterCheck.setCreatorName(userInfoList.get(0).getFullName());
            }
        }
        //枚举参数表
        if(releaseMediaBean.getParameterManagerList() != null){
            for (ParameterManager parameterManager : releaseMediaBean.getParameterManagerList()) {
                parameterManager.setCreatorId(userInfoList.get(0).getId());
                parameterManager.setCreatorName(userInfoList.get(0).getFullName());
                parameterManager.setUpdatorId(userInfoList.get(0).getId());
                parameterManager.setUpdatorName(userInfoList.get(0).getFullName());
            }
        }
    }

    /**
     * 遍历附件文件夹内文件
     *
     * @param attachmentsFolder 附件文件夹
     */
    @Override
    public void handleAttachmentsFolder(File attachmentsFolder, ReleaseMediaBean releaseMediaBean,boolean saveAttachmentFalg) throws
            ScriptException {
        // 遍历附件文件夹内文件
        File[] attachmentFiles = attachmentsFolder.listFiles();
        List<Attachment> attachmentList = new ArrayList<>();
        String srcScriptUuid = releaseMediaBean.getInfoVersion().getSrcScriptUuid();
        if (attachmentFiles != null) {
            for (File attachmentFile : attachmentFiles) {
                // 处理附件文件
                Attachment attachment = handleAttachmentFile(attachmentFile, srcScriptUuid);
                attachmentList.add(attachment);
            }
            if (!attachmentList.isEmpty()) {
                releaseMediaBean.setAttachmentList(attachmentList);
                // 将文件内容存储到附件表中
                if(saveAttachmentFalg){
                    saveAttachments(attachmentList);
                }
            }
        }
    }

    /**
     * 存储附件
     *
     * @param attachmentList 附件列表
     */
    public void saveAttachments(List<Attachment> attachmentList) {
        if (null == attachmentList) {
            return;
        }
        for (Attachment attachment : attachmentList) {
            attachment.setId(null);
            attachmentService.insertAttachment(BeanUtils.copy(attachment, AttachmentDto.class));
        }
    }

    /**
     * 处理附件文件
     *
     * @param attachmentFile 附件
     */
    public Attachment handleAttachmentFile(File attachmentFile, String srcScriptUuid) throws ScriptException {
        Attachment attachment;
        try {
            attachment = new Attachment();
            // 读取文件内容
            byte[] fileContent = Files.readAllBytes(attachmentFile.toPath());
            attachment.setName(attachmentFile.getName());
            attachment.setContents(fileContent);
            attachment.setSize(attachmentFile.length());
            attachment.setSrcScriptUuid(srcScriptUuid);
        } catch (Exception e) {
            logger.error("Failed to process attachment files during production media import!", e);
            // 处理读取文件异常
            throw new ScriptException("error.import.attachment");
        }
        return attachment;
    }


    /**
     * 生成投产介质文件
     *
     * @param path 文件路径
     * @param ids  版本Id
     */
    private void createReleaseMediaFile(Long[] ids, String path) throws
            JsonProcessingException, ScriptException {
        // 获取投产介质所需的信息
        List<ReleaseMediaBean> releaseMediaBeanList = getReleaseMediaBeans(ids);

        for (ReleaseMediaBean releaseMediaBean : releaseMediaBeanList) {
            String scriptName = releaseMediaBean.getInfoDto().getScriptName();
            List<Attachment> attachment = releaseMediaBean.getAttachmentList();

            String json = objectMapper.writeValueAsString(releaseMediaBean);

            String srcScriptUuid = releaseMediaBean.getInfoVersion().getSrcScriptUuid();

            String fileName = scriptName + ".json";
            String filePath = path + File.separator + srcScriptUuid + "_" + scriptName;
            myScriptService.createFile(fileName, json, attachment, filePath, false);

            String scriptFileName = scriptName + "." + releaseMediaBean.getInfoDto().getScriptType();
            String scriptFilePath = path + File.separator + Constants.SCRIPT_FILE_DIR_NAME;
            myScriptService.createFile(scriptFileName, releaseMediaBean.getInfoVersionText().getContent(), new ArrayList<>(), scriptFilePath, true);
        }
    }


    /**
     * 组装投产介质所需的信息
     *
     * @param ids 版本id
     * @return List<ReleaseMediaBean>
     */
    private List<ReleaseMediaBean> getReleaseMediaBeans(Long[] ids) {
        //获取要下载的列表
        List<ReleaseMediaBean> releaseMediaBeanList = new ArrayList<>();
        for (Long infoVersionId : ids) {

            ReleaseMediaBean releaseMediaBean = new ReleaseMediaBean();

            // 获取脚本版本信息
            ScriptVersionDto infoVersionDto = infoVersionService.selectInfoVersionById(infoVersionId);
            releaseMediaBean.setInfoVersion(BeanUtils.copy(infoVersionDto, InfoVersion.class));

            // 获取文件内容
            InfoVersionTextDto infoVersionTextDto = infoVersionTextService.selectInfoVersionTextByScriptUuid(infoVersionDto.getSrcScriptUuid());
            releaseMediaBean.setInfoVersionText(BeanUtils.copy(infoVersionTextDto, InfoVersionText.class));

            // 获取脚本基础信息
            ScriptInfoDto infoDto = infoService.selectInfoByUniqueUuid(infoVersionDto.getInfoUniqueUuid());
            if (StringUtils.isNotEmpty(infoDto.getPlatform())) {
                infoDto.setPlatforms(Arrays.asList(infoDto.getPlatform().split(",")));
            }
            //根据查到的用户获取登录名称
            List<UserInfoApiDto> userInfoList = userInfoApi.getUserInfoList(Collections.singletonList(infoDto.getCreatorId()));
            if(Objects.nonNull(userInfoList) && !userInfoList.isEmpty()) {
                infoDto.setCreatorName(userInfoList.get(0).getLoginName());
                infoDto.setCreatorFullName(userInfoList.get(0).getFullName());
            }
            // 获取脚本的当前分类
            Category category = categoryMapper.selectCategoryById(infoDto.getCategoryId());
            Category sonCategory = categoryService.setChildrenForCategoryAndParent(category);
            infoDto.setCategoryDto(BeanUtils.copy(sonCategory,CategoryDto.class));

            String categoryName = categoryService.getCategoryFullPath(infoDto.getCategoryId());
            infoDto.setCategoryPath(categoryName);

            releaseMediaBean.setInfoDto(infoDto);

            // 获取当前版本脚本绑定的参数集合
            List<Parameter> parameterList = parameterService.getParameterByUuid(infoVersionDto.getSrcScriptUuid());
            releaseMediaBean.setParameters(parameterList);

            // 定义参数校验规则列表
            List<ParameterCheck> parameterCheckList = new ArrayList<>();
            // 定义枚举参数列表
            List<ParameterManager> parameterManagerList = new ArrayList<>();

            // 组装参数校验规则集合以及枚举参数列表集合
            for (Parameter parameter : parameterList) {
                if (null != parameter.getParamCheckIid() && -1 != parameter.getParamCheckIid()) {
                    ParameterCheckDto parameterCheckDto = parameterCheckService.selectParameterCheckById(parameter.getParamCheckIid());
                    parameterCheckList.add(BeanUtils.copy(parameterCheckDto, ParameterCheck.class));
                }
                if (null != parameter.getScriptParameterManagerId() && -1 != parameter.getScriptParameterManagerId()) {
                    ParameterManagerDto parameterManagerDto = parameterManagerService.selectParameterManagerById(parameter.getScriptParameterManagerId());
                    parameterManagerList.add(BeanUtils.copy(parameterManagerDto, ParameterManager.class));

                }

            }
            releaseMediaBean.setParameterCheckList(parameterCheckList);
            releaseMediaBean.setParameterManagerList(parameterManagerList);

            // 获取附件
            List<AttachmentDto> attachmentDtoList = attachmentService.getAttachmentByUuid(infoVersionDto.getSrcScriptUuid());
            releaseMediaBean.setAttachmentList(BeanUtils.copy(attachmentDtoList, Attachment.class));

            releaseMediaBeanList.add(releaseMediaBean);

        }
        return releaseMediaBeanList;
    }


    /**
     * 删除临时文件和压缩包
     *
     * @param zipFile      压缩文件
     * @param sourceFolder 临时文件
     */
    @Override
    public void cleanupFiles(File zipFile, File sourceFolder) throws ScriptException {
        try {

            // 验证路径有效性
            if (zipFile != null && !FilePathValidator.apply(zipFile.getAbsolutePath())) {
                throw new ScriptException("Invalid path for zip file: " + zipFile.getAbsolutePath());
            }

            if (sourceFolder != null && !FilePathValidator.apply(sourceFolder.getAbsolutePath())) {
                throw new ScriptException("Invalid path for source folder: " + sourceFolder.getAbsolutePath());
            }

            // 删除压缩文件
            if (null != zipFile) {
                org.apache.commons.io.FileUtils.deleteQuietly(zipFile);
            }
            // 清空原始文件夹
            if (null != sourceFolder) {
                org.apache.commons.io.FileUtils.deleteDirectory(sourceFolder);
            }

        } catch (Exception e) {
            logger.error(LogMessage.CLEANUPFILES_FAIL.getValue(), e);
            throw new ScriptException("error.clean.tempFile");
        }
    }

    /**
     * 接口，脚本导出，入口
     * @param srcScriptUuid 脚本的uuid
     * @return  脚本介质导入导入dto
     */
    @Override
    public ScriptFileImportExportApiDto exportScriptProductionApi(List<String> srcScriptUuid){
        //根据uuid查询脚本的iid
        List<Long> ids =  infoVersionMapper.getScriptIdsByUuids(srcScriptUuid);
        //查询脚本文件并返回结果
        ScriptFileImportExportApiDto scriptFileImportExportApiDto = new ScriptFileImportExportApiDto();
        File zipFile = null;
        File sourceFolder = null;
        try {
            Map<String, Object> map = scriptZipFile(ids.toArray(new Long[0]));
            String zipName = String.valueOf(map.get("zipName"));
            zipFile = (File) map.get("zipFile");
            sourceFolder = (File) map.get("sourceFolder");
            scriptFileImportExportApiDto.setFileName(zipName.substring(0,zipName.length()-4));
            scriptFileImportExportApiDto.setFileSuffix("zip");
            scriptFileImportExportApiDto.setFileContentByte(ZipUtil.readFileByte(zipFile));
        } catch (Exception e) {
            logger.error("exportScriptProductionApi is error : ",e);
        } finally {
            // 删除临时文件夹和压缩文件
            try {
                cleanupFiles(zipFile, sourceFolder);
            } catch (Exception e) {
                logger.error(LogMessage.CLEANUPFILES_FAIL.getValue(), e);
            }
        }
        return scriptFileImportExportApiDto;
    }

}
