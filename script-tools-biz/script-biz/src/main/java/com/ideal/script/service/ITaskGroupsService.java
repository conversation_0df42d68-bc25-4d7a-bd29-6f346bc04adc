package com.ideal.script.service;

import com.github.pagehelper.PageInfo;
import com.ideal.common.dto.TableQueryDto;
import com.ideal.script.dto.ItsmGetScriptAgentQueryDto;
import com.ideal.script.exception.ScriptException;
import com.ideal.script.model.dto.*;
import com.ideal.script.model.dto.interaction.agent.SystemComputerGroupDto;
import com.ideal.script.model.dto.interaction.agent.SystemComputerGroupQueryDto;
import com.ideal.system.dto.SystemAgentInfoApiDto;
import org.apache.ibatis.session.SqlSession;

import java.util.List;

/**
 * 任务与资源组关系 平台资源组名称变更，此处选择的资源组名称名称不变 选择资源组后，将资源组下涉Service接口
 * 
 * <AUTHOR>
 */
 public interface ITaskGroupsService
{
    /**
     * 查询任务与资源组关系 平台资源组名称变更，此处选择的资源组名称名称不变 选择资源组后，将资源组下涉
     * 
     * @param id 任务与资源组关系 平台资源组名称变更，此处选择的资源组名称名称不变 选择资源组后，将资源组下涉主键
     * @return 任务与资源组关系 平台资源组名称变更，此处选择的资源组名称名称不变 选择资源组后，将资源组下涉
     */
     TaskGroupsDto selectTaskGroupsById(Long id);

    /**
     * 查询任务与资源组关系 平台资源组名称变更，此处选择的资源组名称名称不变 选择资源组后，将资源组下涉列表
     * @param pageNum 当前页
     * @param pageSize 每页条数
     * @param taskGroupsDto 任务与资源组关系 平台资源组名称变更，此处选择的资源组名称名称不变 选择资源组后，将资源组下涉
     * @return 任务与资源组关系 平台资源组名称变更，此处选择的资源组名称名称不变 选择资源组后，将资源组下涉集合
     */
     PageInfo<TaskGroupsDto> selectTaskGroupsList(TaskGroupsDto taskGroupsDto, int pageNum, int pageSize);

    /**
     * 新增任务与资源组关系 平台资源组名称变更，此处选择的资源组名称名称不变 选择资源组后，将资源组下涉
     * 
     * @param taskGroupsDto 任务与资源组关系 平台资源组名称变更，此处选择的资源组名称名称不变 选择资源组后，将资源组下涉
     * @return 结果
     */
     int insertTaskGroups(TaskGroupsDto taskGroupsDto);

    /**
     * 修改任务与资源组关系 平台资源组名称变更，此处选择的资源组名称名称不变 选择资源组后，将资源组下涉
     * 
     * @param taskGroupsDto 任务与资源组关系 平台资源组名称变更，此处选择的资源组名称名称不变 选择资源组后，将资源组下涉
     * @return 结果
     */
     int updateTaskGroups(TaskGroupsDto taskGroupsDto);

    /**
     * 批量删除任务与资源组关系 平台资源组名称变更，此处选择的资源组名称名称不变 选择资源组后，将资源组下涉
     * 
     * @param ids 需要删除的任务与资源组关系 平台资源组名称变更，此处选择的资源组名称名称不变 选择资源组后，将资源组下涉主键集合
     * @return 结果
     */
     int deleteTaskGroupsByIds(Long[] ids);

    /**
     * 删除任务与资源组关系 平台资源组名称变更，此处选择的资源组名称名称不变 选择资源组后，将资源组下涉信息
     * 
     * @param id 任务与资源组关系 平台资源组名称变更，此处选择的资源组名称名称不变 选择资源组后，将资源组下涉主键
     * @return 结果
     */
     int deleteTaskGroupsById(Long id);

    /**
     * 删除任务与资源组关系
     *
     * @param taskId
     * @return 结果
     */
    int deleteTaskGroupsByTaskId(Long taskId);

     /**
      * 查询任务绑定资源组信息-双人复核业务详情界面使用
      * @param serviceId 业务主键
      * @return List<TaskGroupsDto>
      */
    List<TaskGroupsDto> selectTaskGroupsByServiceId(Long serviceId,Long taskId);

    /**
     * 查询某个agent分组下绑定的agent信息
     *
     * @param pageNum                     当前页
     * @param pageSize                    每页页数
     * @param systemComputerGroupQueryDto 组id
     * @return PageInfo<SystemComputerGroupDto>
     */
     PageInfo<AgentInfoDto> queryAgentPageListByGroupId(SystemComputerGroupQueryDto systemComputerGroupQueryDto, Integer pageNum, Integer pageSize);

    /**
     * 存储任务与资源组关系
     *
     * @param scriptExecAuditDto 任务申请提交审核Dto
     * @param taskInfo           任务申请提交任务时的脚本任务对象
     * @param sqlSession         ​SqlSession​ 对象
     * @throws ScriptException 抛出自定义异常
     */
    void saveTaskGroups(ScriptExecAuditDto scriptExecAuditDto, TaskDto taskInfo, SqlSession sqlSession) throws ScriptException;

    /**
     * 功能描述：获取选定资源组绑定的agent信息
     *
     * @param taskGroupsDtoList 资源组集合
     * @return {@link List }<{@link AgentInfoDto }>
     */
    List<AgentInfoDto> retrieveUniqueAgentInfoList(List<TaskGroupsDto> taskGroupsDtoList);

    /**
     * 获取用户所属角色绑定的设备组下的所有Agent
     * @param agentInfoQueryDto agent查询条件封装Dto
     * @param pageNum 当前页
     * @param pageSize 每页页数
     * @return PageInfo<AgentInfoDto>
     */

    PageInfo<AgentInfoDto> queryAgentInfoGroupRole(AgentInfoQueryDto agentInfoQueryDto, Integer pageNum, Integer pageSize);

    /**
     * itsm获取设备李彪
     * @param tableQueryDTO 请求参数
     * @return 返回设备分页信息
     */
    PageInfo<ItsmAgentInfoDto> queryAgentInfoItsmGroupRole(TableQueryDto<ItsmGetScriptAgentQueryDto> tableQueryDTO);

    /**
     * 获取agent分组列表数据
     * @param systemComputerGroupQueryDtoDto agent组条件封装
     * @param pageNum 当前页
     * @param pageSize 每页页数
     * @return PageInfo<SystemComputerGroupDto>
     */
    PageInfo<SystemComputerGroupDto> queryAgentGroupPageList(SystemComputerGroupQueryDto systemComputerGroupQueryDtoDto, Integer pageNum, Integer pageSize);

    /**
     * 任务申请获取agent 转换
     * @param systemAgentInfoApiDto
     * @return
     */
    AgentInfoDto convertToAgentInfoDto(SystemAgentInfoApiDto systemAgentInfoApiDto);
}
