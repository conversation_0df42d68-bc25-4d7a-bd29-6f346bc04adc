package com.ideal.script.service.impl;

import com.ideal.common.util.BeanUtils;
import com.ideal.script.mapper.PlatformMapper;
import com.ideal.script.model.dto.PlatformDto;
import com.ideal.script.model.entity.Platform;
import com.ideal.script.service.IPlatformService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class PlatformServiceImpl implements IPlatformService {

    private final PlatformMapper platformMapper;

    public PlatformServiceImpl(PlatformMapper platformMapper) {
        this.platformMapper = platformMapper;
    }


    @Override
    public List<PlatformDto> getScriptPlatformCode() {
        List<Platform> platforms = platformMapper.selectPlatformList();
        return BeanUtils.copy(platforms, PlatformDto.class);
    }


}
