package com.ideal.script.config;

/**
 * psbc个性化配置
 *
 * <AUTHOR>
 **/
public class PsbcProperties {
    /**
     * itsm单号校验地址
     */
    private String itsmCheckOrderUrl;
    /**
     * itsm单号创建地址
     */
    private String itsmCreateOrderUrl;
    /**
     * itsm接口鉴权key
     */
    private String itsmAuthenticationKey;
    /**
     * 是否推送itsm开关，默认false
     */
    private boolean enablePushItsm = false;
    /**
     * 任务执行结束，给itsm推送消息url
     */
    private String pushTaskMessageToItsmUrl;

    public String getPushTaskMessageToItsmUrl() {
        return pushTaskMessageToItsmUrl;
    }

    public void setPushTaskMessageToItsmUrl(String pushTaskMessageToItsmUrl) {
        this.pushTaskMessageToItsmUrl = pushTaskMessageToItsmUrl;
    }

    private Integer staleLimit = 180;

    public Integer getStaleLimit() {
        return staleLimit;
    }

    public void setStaleLimit(Integer staleLimit) {
        this.staleLimit = staleLimit;
    }

    public boolean isEnablePushItsm() {
        return enablePushItsm;
    }

    public void setEnablePushItsm(boolean enablePushItsm) {
        this.enablePushItsm = enablePushItsm;
    }

    public String getItsmCheckOrderUrl() {
        return itsmCheckOrderUrl;
    }

    public void setItsmCheckOrderUrl(String itsmCheckOrderUrl) {
        this.itsmCheckOrderUrl = itsmCheckOrderUrl;
    }

    public String getItsmCreateOrderUrl() {
        return itsmCreateOrderUrl;
    }

    public void setItsmCreateOrderUrl(String itsmCreateOrderUrl) {
        this.itsmCreateOrderUrl = itsmCreateOrderUrl;
    }

    public String getItsmAuthenticationKey() {
        return itsmAuthenticationKey;
    }

    public void setItsmAuthenticationKey(String itsmAuthenticationKey) {
        this.itsmAuthenticationKey = itsmAuthenticationKey;
    }
}
