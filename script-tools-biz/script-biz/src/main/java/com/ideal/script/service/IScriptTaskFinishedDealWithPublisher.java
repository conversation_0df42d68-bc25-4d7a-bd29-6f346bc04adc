package com.ideal.script.service;

import com.ideal.script.common.constant.enums.Enums;
import com.ideal.script.model.dto.TaskStartDto;

/**
 * 脚本任务结束后各种操作接口
 */
public interface IScriptTaskFinishedDealWithPublisher {

    /**
     * psbc，推送agent执行信息数量给itsm
     * @param taskRuntimeId agent实例id
     * @param message 提示信息
     * @param taskIdFlag 任务id标识
     */
    void pushAgentStateCountToItsm(Long taskRuntimeId,String message,boolean taskIdFlag);

    /**
     * psbc，任务完成后推送统计短信
     * @param taskRuntimeId agent实例id
     */
    void pushAgentStateCountToItsm(Long taskRuntimeId);

    /**
     * citic,中信作业中心监控，任务完成消息推送mq
     * @param taskStartDto 任务信息对象
     * @param state 状态
     */
    void scriptResultToOperationCenterMonitorMq(TaskStartDto taskStartDto, Enums.TaskInstanceStatus state);

    /**
     * cib,脚本任务完成后调用itsm接口
     * @param taskId 任务id
     */
    void scriptTaskFinishedToItsm(Long taskId);

}
