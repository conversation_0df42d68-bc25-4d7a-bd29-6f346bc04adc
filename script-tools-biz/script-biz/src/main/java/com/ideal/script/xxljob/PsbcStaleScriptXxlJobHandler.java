package com.ideal.script.xxljob;

import com.ideal.sc.annotation.CustomerIdentity;
import com.ideal.sc.constants.CustomerConstants;
import com.ideal.script.config.PsbcProperties;
import com.ideal.script.mapper.ExectimeMapper;
import com.ideal.script.mapper.InfoMapper;
import com.ideal.script.mapper.InfoVersionMapper;
import com.ideal.script.mapper.StaleScriptMapper;
import com.ideal.script.model.entity.Exectime;
import com.ideal.script.model.entity.Info;
import com.ideal.script.model.entity.InfoVersion;
import com.ideal.script.model.entity.Stale;
import com.ideal.script.service.impl.MyScriptServiceImpl;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

/**
 * psbc长时间未修改报表JobHandler
 *
 * <AUTHOR>
 **/
@Component
@CustomerIdentity(CustomerConstants.PSBC)
@SuppressWarnings("unused")
public class PsbcStaleScriptXxlJobHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(PsbcStaleScriptXxlJobHandler.class);
    private final StaleScriptMapper staleScriptMapper;
    private final InfoVersionMapper infoVersionMapper;
    private final MyScriptServiceImpl myScriptService;
    private final InfoMapper infoMapper;
    private final ExectimeMapper exectimeMapper;
    private final PsbcProperties psbcProperties;

    public PsbcStaleScriptXxlJobHandler(StaleScriptMapper staleScriptMapper, InfoVersionMapper infoVersionMapper, MyScriptServiceImpl myScriptService, InfoMapper infoMapper, ExectimeMapper exectimeMapper, PsbcProperties psbcProperties) {
        this.staleScriptMapper = staleScriptMapper;
        this.infoVersionMapper = infoVersionMapper;
        this.myScriptService = myScriptService;
        this.infoMapper = infoMapper;
        this.exectimeMapper = exectimeMapper;
        this.psbcProperties = psbcProperties;
    }

    @XxlJob("staleScriptHandler")
    public void staleScriptHandler() {
        try {
            // 获取180天之前时间点
            Timestamp limit = new Timestamp(System.currentTimeMillis() - 1000L * 60 * 60 * 24 * psbcProperties.getStaleLimit());
            List<InfoVersion> notifyList = new ArrayList<>();

            // 查询所有180天前最高版本的脚本基本信息
            List<InfoVersion> data = staleScriptMapper.getData();
            for (InfoVersion infoVersion : data) {
                // 小于180天
                if (infoVersion.getUpdateTime().after(limit)) {
                    // infoId
                    staleScriptMapper.deleteStaleByInfoId(infoVersion.getId());
                } else {
                    Stale staleData = staleScriptMapper.getStaleData(infoVersion.getId());
                    int day = Math.toIntExact((System.currentTimeMillis() - infoVersion.getUpdateTime().getTime()) / (1000L * 60 * 60 * 24));
                    if (null == staleData) {
                        InfoVersion infoVersion1 = infoVersionMapper.selectInfoVersionBysrcScriptUuid(infoVersion.getSrcScriptUuid());
                        Info info = infoMapper.selectInfoByUniqueUuid(infoVersion1.getInfoUniqueUuid());
                        myScriptService.buildCategoryPath(info);
                        Exectime totalAndSuccessRate = exectimeMapper.getTotalAndSuccessRate(infoVersion.getSrcScriptUuid());
                        // 插入数据
                        Stale stale = new Stale();
                        stale.setInfoId(info.getId());
                        stale.setScriptName(info.getScriptName());
                        stale.setScriptNameZh(info.getScriptNameZh());
                        if (null == totalAndSuccessRate) {
                            stale.setSuccessRate("0%");
                            stale.setTaskCount(0);
                        } else {
                            stale.setSuccessRate(totalAndSuccessRate.getSuccessRate());
                            stale.setTaskCount(Math.toIntExact(totalAndSuccessRate.getTotalTimes()));
                        }
                        stale.setCategoryPath(info.getCategoryPath());
                        stale.setInfoVersionId(infoVersion1.getId());
                        stale.setDefaultVersion(infoVersion1.getVersion());
                        stale.setUnmodifyDay(day);
                        stale.setInfoUpdatetime(infoVersion1.getUpdateTime());
                        stale.setCreatorId(infoVersion1.getCreatorId());
                        stale.setCreatorName(infoVersion1.getCreatorName());
                        staleScriptMapper.insertStaleData(stale);

                        notifyList.add(infoVersion1);
                    } else {
                        staleScriptMapper.updateStaleData(day, staleData.getId());
                        if (staleData.getConfirmState() == 1) {
                            // 已确认数据不处理 1-已确认状态
                        } else {
                            InfoVersion infoVersion1 = infoVersionMapper.selectInfoVersionBysrcScriptUuid(infoVersion.getSrcScriptUuid());
                            // 更新数据
                            notifyList.add(infoVersion1);
                        }
                    }
                }
            }

            // 调用推送方法
            for (InfoVersion infoVersion : notifyList) {
                myScriptService.toAlarm(infoVersion.getSrcScriptUuid(), infoVersion.getCreatorId());
                LOGGER.info("xxl-job scan 180day sendAlarm, srcScriptUuid: {}, createUserId: {}", infoVersion.getSrcScriptUuid(), infoVersion.getCreatorId());
            }
            XxlJobHelper.handleSuccess("stale script report generated successfully!");
        } catch (Exception e) {
            LOGGER.error("stale script report generated error:", e);
            //返回给调度中心执行结果异常，执行备注信息
            XxlJobHelper.handleFail(e.getMessage());
        }
    }
}
