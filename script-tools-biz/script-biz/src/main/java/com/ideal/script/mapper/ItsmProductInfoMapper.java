package com.ideal.script.mapper;

import com.ideal.script.model.entity.ItsmProductInfo;
import org.springframework.data.repository.query.Param;

/**
 * 【itsm工单与脚本信息表】Mapper接口
 *
 * <AUTHOR>
 */
public interface ItsmProductInfoMapper {
    int deleteByPublishInfoId(@Param("publishInfoId") Long publishInfoId);

    ItsmProductInfo getItsmProductInfoByProductId(@Param("productId") Long productId);

    ItsmProductInfo getItsmProductInfoById(@Param("id") Long id);

    int insertProductInfo(ItsmProductInfo itsmProductInfo);

    int updateScriptInfoJsonById(ItsmProductInfo itsmProductInfo);
}
