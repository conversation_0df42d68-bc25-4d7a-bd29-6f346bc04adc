package com.ideal.script.mapper;

import com.ideal.script.model.entity.ItsmProductAttachment;
import com.ideal.script.model.entity.ItsmProductInfo;
import org.springframework.data.repository.query.Param;

/**
 * 【itsm工单与脚本信息表】Mapper接口
 *
 * <AUTHOR>
 */
public interface ItsmProductAttachmentMapper {
    int deleteByPublishInfoId(@Param("publishInfoId") Long publishInfoId);

    int insert(ItsmProductAttachment itsmProductAttachment);

    ItsmProductAttachment selectAttachmentById(Long attachmentId);

}
