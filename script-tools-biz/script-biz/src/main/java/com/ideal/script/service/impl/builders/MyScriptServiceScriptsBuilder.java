package com.ideal.script.service.impl.builders;

import com.ideal.message.center.IPublisher;
import com.ideal.notification.api.IWarn;
import com.ideal.script.common.util.BatchDataUtil;
import com.ideal.script.config.ScriptBusinessConfig;
import com.ideal.script.mapper.AttachmentMapper;
import com.ideal.script.mapper.AuditRelationMapper;
import com.ideal.script.mapper.BindFuncVarMapper;
import com.ideal.script.mapper.CategoryMapper;
import com.ideal.script.mapper.DangerCmdMapper;
import com.ideal.script.mapper.InfoMapper;
import com.ideal.script.mapper.InfoVersionMapper;
import com.ideal.script.mapper.InfoVersionTextMapper;
import com.ideal.script.mapper.MyScriptMapper;
import com.ideal.script.mapper.ParameterMapper;
import com.ideal.script.remotecall.RemoteCall;
import com.ideal.script.service.IDangerCmdService;
import com.ideal.script.service.IScriptStatementService;
import com.ideal.script.service.impl.AttachmentServiceImpl;
import com.ideal.script.service.impl.AuditRelationServiceImpl;
import com.ideal.script.service.impl.BindFuncVarServiceImpl;
import com.ideal.script.service.impl.CategoryServiceImpl;
import com.ideal.script.service.impl.InfoVersionServiceImpl;
import com.ideal.script.service.impl.InfoVersionTextServiceImpl;
import com.ideal.script.service.impl.ParameterServiceImpl;
import com.ideal.script.service.impl.TaskApplyServiceImpl;
import com.ideal.system.api.IRole;
import com.ideal.system.api.IUserInfo;
import org.apache.ibatis.session.SqlSessionFactory;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
public class MyScriptServiceScriptsBuilder {
    InfoMapper infoMapper;
    InfoVersionMapper infoVersionMapper;
    ParameterMapper parameterMapper;
    InfoVersionTextMapper infoVersionTextMapper;
    AttachmentMapper attachmentMapper;
    MyScriptMapper myScriptMapper;
    CategoryMapper categoryMapper;
    BindFuncVarMapper bindFuncVarMapper;
    ParameterServiceImpl parameterService;
    InfoVersionTextServiceImpl infoVersionTextService;
    AttachmentServiceImpl attachmentService;
    BindFuncVarServiceImpl bindFuncVarService;
    CategoryServiceImpl categoryService;

    RemoteCall remoteCall;
    SqlSessionFactory factory;
    BatchDataUtil batchDataUtil;

    AuditRelationMapper auditRelationMapper;
    TaskApplyServiceImpl taskApplyService;
    AuditRelationServiceImpl auditRelationService;
    DangerCmdMapper dangerCmdMapper;
    IUserInfo iUserInfo;
    IRole iRole;
    RedissonClient redissonClient;

    ScriptBusinessConfig scriptBusinessConfig;

    InfoVersionServiceImpl infoVersionService;

    IScriptStatementService scriptStatementService;

    IWarn warn;

    IDangerCmdService dangerCmdService;
    IPublisher iPublisher;


    public MyScriptServiceScriptsBuilder(InfoMapper infoMapper, InfoVersionMapper infoVersionMapper, ParameterMapper parameterMapper, InfoVersionTextMapper infoVersionTextMapper, AttachmentMapper attachmentMapper, MyScriptMapper myScriptMapper, CategoryMapper categoryMapper, BindFuncVarMapper bindFuncVarMapper, ParameterServiceImpl parameterService, InfoVersionTextServiceImpl infoVersionTextService, AttachmentServiceImpl attachmentService, BindFuncVarServiceImpl bindFuncVarService, CategoryServiceImpl categoryService, RemoteCall remoteCall, SqlSessionFactory factory, BatchDataUtil batchDataUtil, AuditRelationMapper auditRelationMapper, TaskApplyServiceImpl taskApplyService, AuditRelationServiceImpl auditRelationService, DangerCmdMapper dangerCmdMapper, IUserInfo iUserInfo, RedissonClient redissonClient, ScriptBusinessConfig scriptBusinessConfig, InfoVersionServiceImpl infoVersionService, IScriptStatementService scriptStatementService,IWarn warn,IDangerCmdService dangerCmdService,IPublisher iPublisher,IRole iRole) {
        this.infoMapper = infoMapper;
        this.infoVersionMapper = infoVersionMapper;
        this.parameterMapper = parameterMapper;
        this.infoVersionTextMapper = infoVersionTextMapper;
        this.attachmentMapper = attachmentMapper;
        this.myScriptMapper = myScriptMapper;
        this.categoryMapper = categoryMapper;
        this.bindFuncVarMapper = bindFuncVarMapper;
        this.parameterService = parameterService;
        this.infoVersionTextService = infoVersionTextService;
        this.attachmentService = attachmentService;
        this.bindFuncVarService = bindFuncVarService;
        this.categoryService = categoryService;
        this.remoteCall = remoteCall;
        this.factory = factory;
        this.batchDataUtil = batchDataUtil;
        this.auditRelationMapper = auditRelationMapper;
        this.taskApplyService = taskApplyService;
        this.auditRelationService = auditRelationService;
        this.dangerCmdMapper = dangerCmdMapper;
        this.iUserInfo = iUserInfo;
        this.iRole = iRole;
        this.redissonClient = redissonClient;
        this.scriptBusinessConfig = scriptBusinessConfig;
        this.infoVersionService = infoVersionService;
        this.scriptStatementService = scriptStatementService;
        this.warn = warn;
        this.dangerCmdService = dangerCmdService;
        this.iPublisher = iPublisher;

    }

    public MyScriptServiceScripts build() {
        return new MyScriptServiceScripts(this);
    }

}
