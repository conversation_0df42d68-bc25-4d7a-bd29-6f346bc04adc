package com.ideal.script.service.impl;

import com.ideal.sc.CustomerProperty;
import com.ideal.sc.constants.CustomerConstants;
import com.ideal.script.common.constant.enums.Enums;
import com.ideal.script.model.dto.TaskStartDto;
import com.ideal.script.observer.itsm.ItsmScriptTaskResultPush;
import com.ideal.script.observer.numerical.ScheduledTaskNumericalResultPush;
import com.ideal.script.service.IAfterScriptExecAuditing;
import com.ideal.script.service.IScriptTaskFinishedDealWithPublisher;
import com.ideal.script.service.IScriptTaskStatePublisher;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * 脚本任务完成后所有操作实现类
 */
@Service
public class ScriptTaskFinishedDealWithPublisher implements IScriptTaskFinishedDealWithPublisher {

    private final ItsmScriptTaskResultPush itsmScriptTaskResultPush;
    private final ScheduledTaskNumericalResultPush scheduledTaskNumericalResultPush;
    private final IScriptTaskStatePublisher scriptTaskStatePublisher;
    private final CustomerProperty customerProperty;
    private final IAfterScriptExecAuditing afterScriptExecAuditing;

    public ScriptTaskFinishedDealWithPublisher(@Lazy ItsmScriptTaskResultPush itsmScriptTaskResultPush, ScheduledTaskNumericalResultPush scheduledTaskNumericalResultPush, IScriptTaskStatePublisher scriptTaskStatePublisher, CustomerProperty customerProperty, IAfterScriptExecAuditing afterScriptExecAuditing){
        this.itsmScriptTaskResultPush = itsmScriptTaskResultPush;
        this.scheduledTaskNumericalResultPush = scheduledTaskNumericalResultPush;
        this.scriptTaskStatePublisher = scriptTaskStatePublisher;
        this.customerProperty = customerProperty;
        this.afterScriptExecAuditing = afterScriptExecAuditing;
    }

    /**
     * psbc，推送agent执行信息数量给itsm
     * @param taskRuntimeId agent实例id
     * @param message 提示信息
     * @param taskIdFlag 任务id标识
     */
    @Override
    public void pushAgentStateCountToItsm(Long taskRuntimeId, String message, boolean taskIdFlag) {
        //任务完成推送itsm
        itsmScriptTaskResultPush.pushMessage(taskRuntimeId,"【finish】任务执行完毕",false);
    }

    /**
     * psbc，任务完成后推送统计短信
     * @param taskRuntimeId agent实例id
     */
    @Override
    public void pushAgentStateCountToItsm(Long taskRuntimeId) {
        //任务完成发送统计短信
        scheduledTaskNumericalResultPush.pushMessage(taskRuntimeId);
    }

    /**
     * citic,中信作业中心监控，任务完成消息推送mq
     * @param taskStartDto 任务信息对象
     * @param state 状态
     */
    @Override
    public void scriptResultToOperationCenterMonitorMq(TaskStartDto taskStartDto, Enums.TaskInstanceStatus state) {
        //中信作业中心监控，任务完成消息推送
        if(Objects.equals(customerProperty.getName(), CustomerConstants.CITIC)){
            scriptTaskStatePublisher.publish(taskStartDto, Enums.TaskInstanceStatus.COMPLETED);
        }
    }

    /**
     * cib,脚本任务完成后调用itsm接口
     * @param taskId 任务id
     */
    @Override
    public void scriptTaskFinishedToItsm(Long taskId) {
        afterScriptExecAuditing.scriptTaskFinishedToItsm(taskId);
    }
}
