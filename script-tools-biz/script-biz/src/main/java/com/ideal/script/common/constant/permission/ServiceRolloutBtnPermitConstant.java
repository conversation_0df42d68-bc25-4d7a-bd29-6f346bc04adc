package com.ideal.script.common.constant.permission;

/**
 * 服务投产菜单按钮权限常量类
 *
 * <AUTHOR>
 */
public class ServiceRolloutBtnPermitConstant {

    // 权限表达式前缀和连接符
    private static final String PER_PREFIX = "@dp.hasBtnPermission('";
    private static final String PER_SUFFIX = "')";

    /**
     * 投产导入按钮权限码
     */
    public static final String IMPORT_RELEASE_MEDIA = "importReleaseMedia";

    /**
     * 投产导入按钮权限表达式
     */
    public static final String IMPORT_RELEASE_MEDIA_PER = PER_PREFIX + IMPORT_RELEASE_MEDIA + PER_SUFFIX;
}
