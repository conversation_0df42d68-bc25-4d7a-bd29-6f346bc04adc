package com.ideal.script.common.constant.permission;

/**
 * 任务申请菜单按钮权限常量类
 *
 * <AUTHOR>
 */
public class TaskApplyBtnPermitConstant {

    // 权限表达式前缀和连接符
    private static final String PER_PREFIX = "@dp.hasBtnPermission('";
    private static final String PER_SUFFIX = "')";

    /**
     * 常用任务、克隆任务按钮权限码
     */
    public static final String CLONE_TASK = "cloneTask";

    /**
     * 任务申请按钮权限码
     */
    public static final String SCRIPT_EXEC_AUDITING = "scriptExecAuditing";


    /**
     * 任务申请-常用任务 任务监控-执行历史-克隆任务 按钮权限表达式
     */
    public static final String CLONE_TASK_PER = PER_PREFIX + CLONE_TASK + PER_SUFFIX;


    /**
     * 任务申请按钮、删除临时附件权限表达式
     */
    public static final String SCRIPT_EXEC_AUDITING_PER = PER_PREFIX + SCRIPT_EXEC_AUDITING + PER_SUFFIX;


}
