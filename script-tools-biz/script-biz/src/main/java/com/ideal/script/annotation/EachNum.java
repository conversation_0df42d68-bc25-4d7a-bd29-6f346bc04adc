package com.ideal.script.annotation;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 验证脚本服务化任务agent并发数
 * <AUTHOR>
 */
@Documented
@Constraint(validatedBy = {EachNumValidator.class})
@Target({ElementType.FIELD, ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
public @interface EachNum {
    //最大允许并发数上限300
    int MAX = 300;
    int DEFAULT_VALUE = 100;
    String message() default "不能超过";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}
