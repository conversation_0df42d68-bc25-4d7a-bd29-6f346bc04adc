package com.ideal.script.common.validation;

import com.ideal.script.annotation.ValidDataSize;
import org.springframework.util.unit.DataSize;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
/**
 * 容量验证注解{@link ValidDataSize}使用的验证器
 * @see com.ideal.script.config.ScriptBusinessConfig
 * <AUTHOR>
 */
public class DataSizeValidator implements ConstraintValidator<ValidDataSize, DataSize> {
    private long maxSize;

    @Override
    public void initialize(ValidDataSize constraintAnnotation) {
        maxSize = DataSize.parse(constraintAnnotation.max()).toBytes();
    }

    @Override
    public boolean isValid(DataSize value, ConstraintValidatorContext context) {
        return value == null || value.toBytes() <= maxSize;
    }
}