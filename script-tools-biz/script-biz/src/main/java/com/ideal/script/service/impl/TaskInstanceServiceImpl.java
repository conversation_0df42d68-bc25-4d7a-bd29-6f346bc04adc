package com.ideal.script.service.impl;

import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.ideal.common.util.BeanUtils;
import com.ideal.common.util.PageDataUtil;
import com.ideal.script.common.constant.Constants;
import com.ideal.script.common.constant.enums.Enums;
import com.ideal.script.mapper.TaskInstanceMapper;
import com.ideal.script.model.dto.TaskInstanceDto;
import com.ideal.script.model.entity.TaskInstance;
import com.ideal.script.service.ITaskInstanceService;
import com.ideal.script.service.interact.ToolsApiAct;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

/**
 * 脚本任务运行实例Service业务层处理
 *
 * <AUTHOR>
 */
@Service
public class TaskInstanceServiceImpl implements ITaskInstanceService {
    private static final Logger LOGGER = LoggerFactory.getLogger(TaskInstanceServiceImpl.class);

    private final TaskInstanceMapper taskInstanceMapper;

    private final ToolsApiAct toolsApiAct;
    private final RedissonClient redissonClient;
    private final ITaskInstanceService taskInstanceService;
    public TaskInstanceServiceImpl(TaskInstanceMapper taskInstanceMapper, @Lazy ToolsApiAct toolsApiAct, RedissonClient redissonClient, @Lazy ITaskInstanceService taskInstanceService) {
        this.taskInstanceMapper = taskInstanceMapper;
        this.toolsApiAct = toolsApiAct;
        this.redissonClient = redissonClient;
        this.taskInstanceService = taskInstanceService;
    }

    /**
     * 查询脚本任务运行实例
     *
     * @param id 脚本任务运行实例主键
     * @return 脚本任务运行实例
     */
    @Override
    public TaskInstanceDto selectTaskInstanceById(Long id) {
        return BeanUtils.copy(taskInstanceMapper.selectTaskInstanceById(id), TaskInstanceDto.class);
    }

    /**
     * 查询任务实例的总数
     * @param id instanceId
     * @return 查询任务实例的总数
     */
    @Override
    public Long selectIpsCount(Long id) {
        return taskInstanceMapper.selectIpsCount(id);
    }

    /**
     * 根据id查询instance实例
     * @param ids instanceId集合
     * @return instanceId集合
     */
    @Override
    public List<Long> selectTaskInstanceIdsById(Long [] ids) {
        return taskInstanceMapper.selectTaskInstanceIdsById(ids);
    }

    /**
     * 查询脚本任务运行实例
     *
     * @param id 脚本任务运行实例主键
     * @return 脚本任务运行实例
     */
    @Override
    public TaskInstanceDto selectTaskInstanceByTaskId(Long id) {
        return BeanUtils.copy(taskInstanceMapper.selectTaskInstanceByTaskId(id), TaskInstanceDto.class);
    }


    /**
     * 通过taskInfoId查询instance信息
     * @param taskInfoId    任务id
     * @return  脚本任务运行实例对象
     */
    @Override
    public TaskInstanceDto getTaskInstanceByTaskInfoId(Long taskInfoId) {
        return BeanUtils.copy(taskInstanceMapper.getTaskInstanceByTaskInfoId(taskInfoId), TaskInstanceDto.class);
    }

    /**
     * 获取任务实例数据
     * @param runtimeId agent实例id
     * @return 任务实例dto
     */
    @Override
    public TaskInstanceDto getTaskInstanceByRuntimeId(Long runtimeId){
        return BeanUtils.copy(taskInstanceMapper.getTaskInstanceByRuntimeId(runtimeId), TaskInstanceDto.class);
    }

    /**
     * 查询脚本任务运行实例列表
     *
     * @param taskInstanceDto 脚本任务运行实例
     * @return 脚本任务运行实例
     */
    @Override
    public PageInfo<TaskInstanceDto> selectTaskInstanceList(TaskInstanceDto taskInstanceDto, int pageNum, int pageSize) {
        PageMethod.startPage(pageNum, pageSize);
        List<TaskInstance> taskInstanceList = taskInstanceMapper.selectTaskInstanceList(BeanUtils.copy(taskInstanceDto, TaskInstance.class));
        return PageDataUtil.toDtoPage(taskInstanceList, TaskInstanceDto.class);
    }


    /**
     * 新增脚本任务运行实例
     *
     * @param taskInstanceDto 脚本任务运行实例
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertTaskInstance(TaskInstanceDto taskInstanceDto) {
        TaskInstance taskInstance = BeanUtils.copy(taskInstanceDto, TaskInstance.class);
        int result = taskInstanceMapper.insertTaskInstance(taskInstance);
        taskInstanceDto.setId(taskInstance.getId());
        return result;

    }

    /**
     * 修改脚本任务运行实例
     *
     * @param taskInstanceDto 脚本任务运行实例
     * @return 结果
     */
    @Override
    public int updateTaskInstance(TaskInstanceDto taskInstanceDto) {
        toolsApiAct.updateTaskStateToTool(taskInstanceDto.getId(),taskInstanceDto.getStatus());
        TaskInstance taskInstance = BeanUtils.copy(taskInstanceDto, TaskInstance.class);
        return taskInstanceMapper.updateTaskInstance(taskInstance);
    }

    /**
     * 批量删除脚本任务运行实例
     *
     * @param ids 需要删除的脚本任务运行实例主键
     * @return 结果
     */
    @Override
    public int deleteTaskInstanceByIds(Long[] ids) {
        return taskInstanceMapper.deleteTaskInstanceByIds(ids);
    }

    /**
     * 删除脚本任务运行实例信息
     *
     * @param id 脚本任务运行实例主键
     * @return 结果
     */
    @Override
    public int deleteTaskInstanceById(Long id) {
        return taskInstanceMapper.deleteTaskInstanceById(id);
    }

    @Override
    public void updateServerNum(Long taskInstanceId, List<Integer> notInStates) {
        taskInstanceMapper.updateServerNum(taskInstanceId,notInStates);
    }

    @Override
    public List<Map<String,Object>> getStatusSummary(Long taskInstanceId) {
        return taskInstanceMapper.getStatusSummary(taskInstanceId);
    }
    /**
     * 更新脚本任务运行实例表的任务状态，当前只有在agent异常状态时和驱动完成时调用
     *
     * @param state 状态
     * @param taskInstanceId 任务实例Id
     * @param excludeStates 排除状态
     * @param updateEndTime 是否更新结束时间
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateState(int state, Long taskInstanceId, int[] excludeStates,boolean updateEndTime) {
        toolsApiAct.updateTaskStateToTool(taskInstanceId,state);
        return taskInstanceMapper.updateState(state,taskInstanceId,excludeStates,updateEndTime);
    }
    /**
     * 开启新事务，根据统计状态，更新任务状态，任务驱动完成后，重试回来的结果是完成状态下会调用
     *
     * @param taskInstanceId 任务实例id
     */
    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRES_NEW)
    public void updateTaskInstanceStateNewTransaction(Long taskInstanceId) {
        taskInstanceService.updateTaskInstanceState(taskInstanceId);
    }


    @Override
    public void updateRunNum(Long taskInstanceId) {
        taskInstanceMapper.updateRunNum(taskInstanceId);
    }

    /**
     * 功能描述：更新脚本任务实例表结束时间
     *
     * @param taskInstanceId 脚本实例表主键
     * @param notInStates    不包含分批执行的状态
     * <AUTHOR>
     */
    @Override
    public void updateEndTime(Long taskInstanceId, List<Integer> notInStates) {
        taskInstanceMapper.updateEndTime(taskInstanceId,notInStates);
    }

    /**
     * 更新任务实例状态(终止/忽略/驱动计数器判断为0时调用、计数器为0调用)
     *
     * @param taskInstanceId  任务实例id
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateTaskInstanceState(Long taskInstanceId) {

        TaskInstanceDto taskInstanceDto = selectTaskInstanceById(taskInstanceId);
        if (null == taskInstanceDto) {
            LOGGER.error("taskInstanceDto is null,taskInstanceId:{}",taskInstanceId);
            return;
        }
        int state;
        //按照agent实例状态统计查询
        List<Map<String, Object>> statusSummary = getStatusSummary(taskInstanceId);
        long failCount = 0;
        long killCount = 0;
        long skipCount = 0;
        long runCount  = 0;
        long completeCount  = 0;
        long notRunCount = 0;
        for (Map<String, Object> row : statusSummary) {
            int queryState = (int) row.get("istate");
            Long cnt = (Long) row.get("counts");
            if (queryState == Enums.TaskRuntimeState.NOT_RUN.getValue()) {
                notRunCount = cnt;
                continue;
            }
            if (queryState == Enums.TaskRuntimeState.RUNNING.getValue()) {
                runCount = cnt;
                continue;
            }
            if (queryState == Enums.TaskRuntimeState.SCRIPT_FAIL_STATE.getValue()) {
                failCount = cnt;
                continue;
            }
            if (queryState == Enums.TaskRuntimeState.TERMINATED.getValue()) {
                killCount = cnt;
                continue;
            }
            if (queryState == Enums.TaskRuntimeState.SKIP.getValue()) {
                skipCount = cnt;
                continue;
            }
            if (queryState == Enums.TaskRuntimeState.COMPLETED.getValue()) {
                completeCount = cnt;
            }
        }
        LOGGER.debug("updateTaskInstanceState -> taskInstanceId:{},runCount:{},failCount:{},killCount:{},skipCount:{},completeCount:{}",taskInstanceId,runCount,failCount,killCount,skipCount,completeCount);
        //只要有异常，任务就是异常
        if (failCount > 0) {
            state = Enums.TaskInstanceStatus.EXCEPTION.getValue();
        } else if (runCount > 0 || notRunCount > 0) { // 没异常但是还有运行的，或者存在未运行的agent实例，展示运行
            state = Enums.TaskInstanceStatus.RUNNING.getValue();
        } else if (skipCount > 0 || killCount > 0) { // 运行、异常都没有，但是有忽略和终止的的，展示红色完成
            state = Enums.TaskInstanceStatus.COMPLETED_RED.getValue();
        } else if ( completeCount > 0 ){
            state = Enums.TaskInstanceStatus.COMPLETED.getValue();
        } else{
            //上面的情况都没数据，说明任务正在驱动执行过程中点击了某些数据的终止，runtime表还没插入数据，不用更新任务实例表
            return ;
        }

        //更新任务实例状态，排除完成状态
        taskInstanceMapper.updateState(state, taskInstanceDto.getId(), Constants.SCRIPT_FINISH_SET,true);
        //删除redis中的异常任务标识
        redissonClient.getBucket(String.format(Constants.SCRIPT_TASK_EXCEPTION_REDIS_KEY, taskInstanceId)).deleteAsync();
    }
}
