package com.ideal.script.mapper;


import com.ideal.script.model.entity.ParameterCheck;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Mapper接口
 *
 * <AUTHOR>
 */
public interface ParameterCheckMapper
{

    /**
     * 新增
     * @param parameterCheck    参数验证实体
     * @return  结果
     */
    int insertParameterCheck(ParameterCheck parameterCheck);

    /**
     * 修改
     * @param parameterCheck    参数验证实体
     * @return  结果
     */
    int updateParameterCheck(ParameterCheck parameterCheck);

    /**
     * 分页查询
     * @param parameterCheck    参数验证实体
     * @return  结果
     */
    List<ParameterCheck> selectParameterCheckList(ParameterCheck parameterCheck);

    /**
     * 删除
     * @param ids   id数组
     * @return  结果
     */
    int deleteParameterCheckByIds(@Param("ids")Long[] ids);

    /**
     * 查询参数校验规则
     * @param id 规则Id
     * @return ParameterCheck
     */

    ParameterCheck selectParameterCheckById(Long id);

    /**
     * 验证规则是否已存在
     *
     * @param ruleName 规则名
     * @return {@link Boolean }
     */
    Boolean validParamterCheckExist(String ruleName);

    /**
     * 查询参数校验规则信息
     *
     * @param ruleName 规则名
     * @return {@link List }<{@link ParameterCheck }>
     */
    List<ParameterCheck> selectParameterCheckByName(String ruleName);
}
