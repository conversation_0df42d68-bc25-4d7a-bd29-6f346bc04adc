package com.ideal.script.mapper;

import java.util.List;

import com.ideal.script.model.entity.TaskGroups;
import org.apache.ibatis.annotations.Param;

/**
 * 任务与资源组关系 平台资源组名称变更，此处选择的资源组名称名称不变 选择资源组后，将资源组下涉Mapper接口
 * 
 * <AUTHOR>
 */
public interface TaskGroupsMapper 
{
    /**
     * 查询任务与资源组关系 平台资源组名称变更，此处选择的资源组名称名称不变 选择资源组后，将资源组下涉
     * 
     * @param id 任务与资源组关系 平台资源组名称变更，此处选择的资源组名称名称不变 选择资源组后，将资源组下涉主键
     * @return 任务与资源组关系 平台资源组名称变更，此处选择的资源组名称名称不变 选择资源组后，将资源组下涉
     */
     TaskGroups selectTaskGroupsById(Long id);

    /**
     * 查询任务与资源组关系 平台资源组名称变更，此处选择的资源组名称名称不变 选择资源组后，将资源组下涉列表
     * 
     * @param taskGroups 任务与资源组关系 平台资源组名称变更，此处选择的资源组名称名称不变 选择资源组后，将资源组下涉
     * @return 任务与资源组关系 平台资源组名称变更，此处选择的资源组名称名称不变 选择资源组后，将资源组下涉集合
     */
     List<TaskGroups> selectTaskGroupsList(TaskGroups taskGroups);

    /**
     * 新增任务与资源组关系 平台资源组名称变更，此处选择的资源组名称名称不变 选择资源组后，将资源组下涉
     * 
     * @param taskGroups 任务与资源组关系 平台资源组名称变更，此处选择的资源组名称名称不变 选择资源组后，将资源组下涉
     * @return 结果
     */
     int insertTaskGroups(TaskGroups taskGroups);

    /**
     * 修改任务与资源组关系 平台资源组名称变更，此处选择的资源组名称名称不变 选择资源组后，将资源组下涉
     * 
     * @param taskGroups 任务与资源组关系 平台资源组名称变更，此处选择的资源组名称名称不变 选择资源组后，将资源组下涉
     * @return 结果
     */
     int updateTaskGroups(TaskGroups taskGroups);

    /**
     * 删除任务与资源组关系 平台资源组名称变更，此处选择的资源组名称名称不变 选择资源组后，将资源组下涉
     * 
     * @param id 任务与资源组关系 平台资源组名称变更，此处选择的资源组名称名称不变 选择资源组后，将资源组下涉主键
     * @return 结果
     */
     int deleteTaskGroupsById(Long id);

    /**
     * 删除任务与资源组关系 平台资源组名称变更，此处选择的资源组名称名称不变 选择资源组后，将资源组下涉
     *
     * @param taskId 任务id
     * @return 结果
     */
    int deleteTaskGroupsByTaskId(Long taskId);

    /**
     * 批量删除任务与资源组关系 平台资源组名称变更，此处选择的资源组名称名称不变 选择资源组后，将资源组下涉
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
     int deleteTaskGroupsByIds(@Param("ids")Long[] ids);

     /**
      * 查询任务绑定的资源组信息-双人复核详情页面使用
      * @param serviceId 业务主键
      * @return List<TaskGroupsDto>
      */
    List<TaskGroups> selectTaskGroupsByServiceId(Long serviceId,Long taskId);
}
