package com.ideal.script.mapper;

import com.ideal.script.model.entity.TaskGroups;

import java.util.List;

/**
 * 常用/克隆任务资源组表mapper
 * 
 * <AUTHOR>
 */
public interface TaskGroupsTemplateMapper
{

    /**
     * 新增任务
     * 
     * @param taskGroups 资源组
     * @return 结果
     */
     int insertTaskGroups(TaskGroups taskGroups);

    /**
     * 根据任务id查询资源组
     * @param taskId 任务id
     * @return 资源组信息
     */
    List<TaskGroups> selectTaskGroupsByTaskId(Long taskId);

    /**
     * 根据任务id删除克隆任务
     * @param taskId 任务id
     * @return 执行结果
     */
    int deleteByTaskId(Long taskId);
}
