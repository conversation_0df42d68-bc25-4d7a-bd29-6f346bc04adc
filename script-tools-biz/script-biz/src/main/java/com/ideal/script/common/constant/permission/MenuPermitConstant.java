package com.ideal.script.common.constant.permission;

/**
 * 菜单常量类
 * 包含系统中所有菜单的码值和权限表达式
 *
 * <AUTHOR>
 */
public class MenuPermitConstant {

    // 菜单码值常量
    /**
     * 我的脚本
     */
    public static final String MY_SCRIPT = "myScript";

    /**
     * 参数管理
     */
    public static final String PARAM_MANAGE = "param-manage";

    /**
     * 类别维护
     */
    public static final String CATEGORY_SERVICE = "category-service";

    /**
     * 任务申请
     */
    public static final String TASK_APPLY = "task-apply";

    /**
     * 值班任务申请
     */
    public static final String TASK_APPLY_DUTY = "task-apply-duty";

    /**
     * 常用任务
     */
    public static final String TEMPLATE_TASK = "template-task";

    /**
     * 关键命令
     */
    public static final String KEY_COMMANDS = "key-commands";

    /**
     * 参数验证
     */
    public static final String PARAM_VALIDATION = "param-validation";

    /**
     * 任务监控
     */
    public static final String EXECUTION_TASK = "execution-task";

    /**
     * 服务投产
     */
    public static final String SERVICE_ROLLOUT = "service-rollout";

    /**
     * 双人复核
     */
    public static final String DOUBLE_CHECK = "double-check";

    /**
     * 定时周期维护
     */
    public static final String TIME_TASK_MANAGER = "time-task-manager";

    /**
     * 长期未修改脚本报表
     */
    public static final String SCRIPT_STATEMENT = "script-statement";

    /**
     * 历史任务报表
     */
    public static final String HISTORY_TASK_STATEMENT = "history-task-statement";

    /**
     * 定时任务监控
     */
    public static final String SCHEDULED_TASK = "scheduled-task";

    /**
     * 全部脚本
     */
    public static final String ALL_SCRIPT = "all-script";

    // 权限表达式前缀和连接符
    private static final String PER_PREFIX = "@dp.hasBtnPermission('";
    private static final String PER_SUFFIX = "')";
    public static final String OR = " or ";

    // 单个权限表达式常量，用于注解中使用
    /**
     * 我的脚本菜单权限
     */
    public static final String MY_SCRIPT_PER = PER_PREFIX + MY_SCRIPT + PER_SUFFIX;

    /**
     * 参数管理菜单权限
     */
    public static final String PARAM_MANAGE_PER = PER_PREFIX + PARAM_MANAGE + PER_SUFFIX;

    /**
     * 类别维护菜单权限
     */
    public static final String CATEGORY_SERVICE_PER = PER_PREFIX + CATEGORY_SERVICE + PER_SUFFIX;

    /**
     * 任务申请菜单权限
     */
    public static final String TASK_APPLY_PER = PER_PREFIX + TASK_APPLY + PER_SUFFIX;

    /**
     * 值班任务申请菜单权限
     */
    public static final String TASK_APPLY_DUTY_PER = PER_PREFIX + TASK_APPLY_DUTY + PER_SUFFIX;

    /**
     * 常用任务菜单权限
     */
    public static final String TEMPLATE_TASK_PER = PER_PREFIX + TEMPLATE_TASK + PER_SUFFIX;

    /**
     * 关键命令菜单权限
     */
    public static final String KEY_COMMANDS_PER = PER_PREFIX + KEY_COMMANDS + PER_SUFFIX;

    /**
     * 参数验证菜单权限
     */
    public static final String PARAM_VALIDATION_PER = PER_PREFIX + PARAM_VALIDATION + PER_SUFFIX;

    /**
     * 任务监控菜单权限
     */
    public static final String EXECUTION_TASK_PER = PER_PREFIX + EXECUTION_TASK + PER_SUFFIX;

    /**
     * 服务投产菜单权限
     */
    public static final String SERVICE_ROLLOUT_PER = PER_PREFIX + SERVICE_ROLLOUT + PER_SUFFIX;

    /**
     * 双人复核菜单权限
     */
    public static final String DOUBLE_CHECK_PER = PER_PREFIX + DOUBLE_CHECK + PER_SUFFIX;

    /**
     * 定时周期维护菜单权限
     */
    public static final String TIME_TASK_MANAGER_PER = PER_PREFIX + TIME_TASK_MANAGER + PER_SUFFIX;

    /**
     * 长期未修改脚本报表菜单权限
     */
    public static final String SCRIPT_STATEMENT_PER = PER_PREFIX + SCRIPT_STATEMENT + PER_SUFFIX;

    /**
     * 历史任务报表菜单权限
     */
    public static final String HISTORY_TASK_STATEMENT_PER = PER_PREFIX + HISTORY_TASK_STATEMENT + PER_SUFFIX;

    /**
     * 定时任务监控菜单权限
     */
    public static final String SCHEDULED_TASK_PER = PER_PREFIX + SCHEDULED_TASK + PER_SUFFIX;

    /**
     * 全部脚本菜单权限
     */
    public static final String ALL_SCRIPT_PER = PER_PREFIX + ALL_SCRIPT + PER_SUFFIX;

    /**
     * 组合权限：我的脚本 或 任务申请
     */
    public static final String MY_SCRIPT_OR_TASK_APPLY_PER = MY_SCRIPT_PER + OR + TASK_APPLY_PER;

    /**
     * 组合权限：我的脚本 或 双人复核
     */
    public static final String MY_SCRIPT_OR_DOUBLE_CHECK_PER = MY_SCRIPT_PER + OR + DOUBLE_CHECK_PER;

    /**
     * 组合权限：我的脚本 或 定时任务监控
     */
    public static final String MY_SCRIPT_OR_SCHEDULED_TASK_PER = MY_SCRIPT_PER + OR + SCHEDULED_TASK_PER;

    /**
     * 组合权限：任务申请 或 值班任务申请
     */
    public static final String TASK_APPLY_OR_TASK_APPLY_DUTY_PER = TASK_APPLY_PER + OR + TASK_APPLY_DUTY_PER;

    /**
     * 组合权限：任务申请 或 双人复核
     */
    public static final String TASK_APPLY_OR_DOUBLE_CHECK_PER = TASK_APPLY_PER + OR + DOUBLE_CHECK_PER;

    /**
     * 组合权限：任务申请 或 常用任务
     */
    public static final String TASK_APPLY_OR_TEMPLATE_TASK_PER = TASK_APPLY_PER + OR + TEMPLATE_TASK_PER;

    /**
     * 组合权限：类别维护 或 我的脚本
     */
    public static final String CATEGORY_SERVICE_OR_MY_SCRIPT_PER = CATEGORY_SERVICE_PER + OR + MY_SCRIPT_PER;

    /**
     * 组合权限：任务申请 或 任务监控 或 双人复核
     */
    public static final String TASK_APPLY_OR_EXECUTION_TASK_OR_DOUBLE_CHECK_PER =
            TASK_APPLY_PER + OR + EXECUTION_TASK_PER + OR + DOUBLE_CHECK_PER;

    /**
     * 组合权限：类别维护 或 常用任务 或 任务申请
     */
    public static final String CATEGORY_SERVICE_OR_TEMPLATE_TASK_OR_TASK_APPLY_PER =
            CATEGORY_SERVICE_PER + OR + TEMPLATE_TASK_PER + OR + TASK_APPLY_PER;

    /**
     * 组合权限：任务监控 或 定时任务监控 或 我的脚本
     */
    public static final String EXECUTION_TASK_OR_SCHEDULED_TASK_OR_MY_SCRIPT_PER =
            EXECUTION_TASK_PER + OR + SCHEDULED_TASK_PER + OR + MY_SCRIPT_PER;

    /**
     * 组合权限：任务申请 或 我的脚本 或 双人复核
     */
    public static final String TASK_APPLY_OR_MY_SCRIPT_OR_DOUBLE_CHECK_PER =
            TASK_APPLY_PER + OR + MY_SCRIPT_PER + OR + DOUBLE_CHECK_PER;

    /**
     * 组合权限：我的脚本 或 任务申请 或 值班任务申请
     */
    public static final String MY_SCRIPT_OR_TASK_APPLY_OR_TASK_APPLY_DUTY_PER =
            MY_SCRIPT_PER + OR + TASK_APPLY_PER + OR + TASK_APPLY_DUTY_PER;

    /**
     * 组合权限：任务监控 或 定时任务监控 或 我的脚本 或 任务申请 或 常用任务
     */
    public static final String EXECUTION_TASK_OR_SCHEDULED_TASK_OR_MY_SCRIPT_OR_TASK_APPLY_OR_TEMPLATE_TASK_PER =
            EXECUTION_TASK_PER + OR + SCHEDULED_TASK_PER + OR +
            MY_SCRIPT_PER + OR + TASK_APPLY_PER + OR + TEMPLATE_TASK_PER;

    /**
     * 组合权限：任务监控 或 任务申请 或 常用任务
     */
    public static final String EXECUTION_TASK_OR_TASK_APPLY_OR_TEMPLATE_TASK_PER =
            EXECUTION_TASK_PER + OR + TASK_APPLY_PER + OR + TEMPLATE_TASK_PER;

    /**
     * 组合权限：我的脚本 或 任务申请 或 值班任务申请 或 常用任务 或 双人复核
     */
    public static final String MY_SCRIPT_OR_TASK_APPLY_OR_TASK_APPLY_DUTY_OR_TEMPLATE_TASK_OR_DOUBLE_CHECK_PER =
            MY_SCRIPT_PER + OR + TASK_APPLY_PER + OR +
            TASK_APPLY_DUTY_PER + OR + TEMPLATE_TASK_PER + OR + DOUBLE_CHECK_PER;

    /**
     * 组合权限：任务申请 或 值班任务申请 或 我的脚本 或 服务投产 或 双人复核 或 历史任务报表
     */
    public static final String TASK_APPLY_OR_TASK_APPLY_DUTY_OR_MY_SCRIPT_OR_SERVICE_ROLLOUT_OR_DOUBLE_CHECK_OR_HISTORY_TASK_STATEMENT_PER =
            TASK_APPLY_PER + OR + TASK_APPLY_DUTY_PER + OR +
            MY_SCRIPT_PER + OR + SERVICE_ROLLOUT_PER + OR +
            DOUBLE_CHECK_PER + OR + HISTORY_TASK_STATEMENT_PER;

    /**
     * 组合权限：任务监控 或 定时周期维护 或 定时任务监控
     */
    public static final String EXECUTION_TASK_OR_TIME_TASK_MANAGER_OR_SCHEDULED_TASK_PER =
            EXECUTION_TASK_PER + OR + TIME_TASK_MANAGER_PER + OR + SCHEDULED_TASK_PER;

    /**
     * 组合权限：常用任务 或 我的脚本
     */
    public static final String TEMPLATE_TASK_OR_MY_SCRIPT_PER =
            TEMPLATE_TASK_PER + OR + MY_SCRIPT_PER;

    /**
     * 组合权限：常用任务 或 任务申请
     */
    public static final String TEMPLATE_TASK_OR_TASK_APPLY_PER =
            TEMPLATE_TASK_PER + OR + TASK_APPLY_PER;
    /**
     * 组合权限：任务监控 或 我的脚本 或 定时任务监控
     */
    public static final String EXECUTION_TASK_OR_MY_SCRIPT_OR_SCHEDULED_TASK_PER =
            EXECUTION_TASK_PER + OR + MY_SCRIPT_PER + OR + SCHEDULED_TASK_PER;

    /**
     * 组合权限：任务监控 或 定时任务监控
     */
    public static final String EXECUTION_TASK_OR_SCHEDULED_TASK_PER =
            EXECUTION_TASK_PER + OR + SCHEDULED_TASK_PER;

    /**
     * 复杂组合权限：类别维护 或 我的脚本 或 关键命令 或 长期未修改脚本报表 或
     * 定时周期维护 或 定时任务监控 或 全部脚本 或 历史任务报表 或 任务监控
     */
    public static final String CATEGORY_SERVICE_DFS_PER =
            CATEGORY_SERVICE_PER + OR + MY_SCRIPT_PER + OR +
            KEY_COMMANDS_PER + OR + SCRIPT_STATEMENT_PER + OR +
            TIME_TASK_MANAGER_PER + OR + SCHEDULED_TASK_PER + OR +
            ALL_SCRIPT_PER + OR + HISTORY_TASK_STATEMENT_PER + OR +
            EXECUTION_TASK_PER;

}
