package com.ideal.script.common.constant.enums;

/**
 * 函数、变量枚举类
 *
 * <AUTHOR>
 */
public enum BindFuncVarTypeEnum {

    /**
     * 变量
     */
    VARIABLE(1, "变量"),
    /**
     * 函数
     */
    FUNCTION(2, "函数");

    private final Integer type;

    private final String description;

    BindFuncVarTypeEnum(Integer type, String description) {
        this.type = type;
        this.description = description;
    }

    public Integer getType() {
        return type;
    }

    public String getDescription() {
        return description;
    }

}
