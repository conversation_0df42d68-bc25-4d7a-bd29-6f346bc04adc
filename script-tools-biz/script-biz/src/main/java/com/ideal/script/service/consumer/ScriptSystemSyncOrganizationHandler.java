package com.ideal.script.service.consumer;

import com.ideal.message.center.ISubscriber;
import com.ideal.script.service.resulthandler.IScriptSystemSyncOrganizationHandlerService;
import com.ideal.system.dto.OrgManagementApiDto;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * 基础数据服务组织机构变更处理脚本服务化相关表的orgCode
 * <AUTHOR>
 **/
@Component
public class ScriptSystemSyncOrganizationHandler implements ISubscriber {
    private final Logger logger = LoggerFactory.getLogger(ScriptSystemSyncOrganizationHandler.class);

    private final IScriptSystemSyncOrganizationHandlerService iScriptSystemSyncOrganizationHandlerService;
    public ScriptSystemSyncOrganizationHandler(IScriptSystemSyncOrganizationHandlerService iScriptSystemSyncOrganizationHandlerService) {
        this.iScriptSystemSyncOrganizationHandlerService = iScriptSystemSyncOrganizationHandlerService;
    }

    @Override
    public void notice(Object message) {
        if (message instanceof OrgManagementApiDto) {
            OrgManagementApiDto orgManagementApiDto = (OrgManagementApiDto) message;
            logger.info("orgManagementApiDto code:{},orgManagementApiDto operateType:{},orgManagementApiDto originalCode:{}", orgManagementApiDto.getCode(),orgManagementApiDto.getOperateType(),orgManagementApiDto.getOriginalCode());
            //只有修改的情况才处理，新增无需关注，删除情况无法处理，删除的情况只有部门下无用户才能删除，这种情况下只能业务上控制再做删除前，将老部门的脚本做创建者转移
            if(StringUtils.equals(orgManagementApiDto.getOperateType(),"update")){
                iScriptSystemSyncOrganizationHandlerService.handler(orgManagementApiDto);
            }
        }else {
            logger.error("The received message is not instance of OrgManagementApiDto :{}",message);
        }
    }
}
