package com.ideal.script.mapper;

import com.ideal.script.model.entity.ToProductEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Mapper接口
 *
 * <AUTHOR>
 * @date 2024-04-02
 */
public interface ToProductMapper {
    /**
     * 查询
     *
     * @param id 主键
     * @return 
     */
    ToProductEntity selectToProductById(Long id);

    /**
     * 查询列表
     *
     * @param toProduct 
     * @return 集合
     */
    List<ToProductEntity> selectToProductList(ToProductEntity toProduct);

    /**
     * 新增
     *
     * @param toProduct 
     * @return 结果
     */
    int insertToProduct(ToProductEntity toProduct);

    /**
     * 修改
     *
     * @param toProduct 
     * @return 结果
     */
    int updateToProduct(ToProductEntity toProduct);

    /**
     * 根据单号更新投产信息
     * @param toProduct 对象数据
     * @return 结果
     */
    int updateToProductByOrderNumber(ToProductEntity toProduct);

    /**
     * 删除
     *
     * @param id 主键
     * @return 结果
     */
    int deleteToProductById(Long id);

    /**
     * 批量删除
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteToProductByIds(@Param("ids")Long[] ids);
}
