package com.ideal.script.xxljob;

import com.ideal.script.service.ITaskAttachmentService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

@Component("attachmentCleanJobHandler")
public class AttachmentCleanJobHandler {
    private final Logger logger = LoggerFactory.getLogger(AttachmentCleanJobHandler.class);
    private final ITaskAttachmentService taskAttachmentService;
    public AttachmentCleanJobHandler(ITaskAttachmentService taskAttachmentService) {
        this.taskAttachmentService = taskAttachmentService;
    }

    @XxlJob("attachmentClean")
    public void scheduleJobHandler() {
        logger.info("xxl-job attachment clean");
        int i = taskAttachmentService.cleanAttachment();
        logger.info("xxl-job attachment clean end, clean size: {}", i);
        // 返回给调度中心执行结果正常，执行备注信息
        XxlJobHelper.handleSuccess("task start is success!");
    }
}
