package com.ideal.script.common.constant.permission;

/**
 * 常用任务菜单按钮权限常量类
 *
 * <AUTHOR>
 */
public class TemplateTaskBtnPermitConstant {

    // 权限表达式前缀和连接符
    private static final String PER_PREFIX = "@dp.hasBtnPermission('";
    private static final String PER_SUFFIX = "')";

    /**
     * 常用克隆任务提交按钮权限码
     */
    public static final String PUBLISH_TEMPLATE = "publishTemplate";

    /**
     * 常用克隆任务保存按钮权限码
     */
    public static final String SAVE_TEMPLATE = "saveTemplate";

    /**
     * 常用克隆任务删除按钮权限码
     */
    public static final String REMOVE_TEMPLATE_TASK = "removeTemplateTask";

    /**
     * 常用克隆任务提交按钮权限表达式
     */
    public static final String PUBLISH_TEMPLATE_PER = PER_PREFIX + PUBLISH_TEMPLATE + PER_SUFFIX;

    /**
     * 常用克隆任务保存按钮、上传附件、删除附件权限表达式
     */
    public static final String SAVE_TEMPLATE_PER = PER_PREFIX + SAVE_TEMPLATE + PER_SUFFIX;

    /**
     * 常用克隆任务删除按钮权限表达式
     */
    public static final String REMOVE_TEMPLATE_TASK_PER = PER_PREFIX + REMOVE_TEMPLATE_TASK + PER_SUFFIX;
}
