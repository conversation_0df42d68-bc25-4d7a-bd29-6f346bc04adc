package com.ideal.script.mapper;

import com.ideal.script.model.bean.TaskExecuteBean;
import com.ideal.script.model.dto.TaskHisAgentExcelDto;
import com.ideal.script.model.entity.Task;
import com.ideal.system.common.component.model.CurrentUser;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 脚本任务Mapper接口
 *
 * <AUTHOR>
 */
public interface TaskMapper {
    /**
     * 查询脚本任务
     *
     * @param id 脚本任务主键
     * @return 脚本任务
     */
    Task selectTaskById(Long id);

    /**
     * 根据agent实例id查询脚本任务
     *
     * @param runtimeId agent实例id
     * @return 脚本任务
     */
    Task selectTaskByRuntimeId(Long runtimeId);

    /**
     * 查询脚本任务
     *
     * @param ids 脚本任务主键
     * @return 脚本任务
     */
    List<TaskExecuteBean> selectTaskByIds(List<Long> ids, CurrentUser currentUser);

    /**
     * 查询脚本任务信息
     *
     * @param serviceId 双人复核业务主键
     * @return 脚本任务
     */
    Task selectTaskByServiceId(Long serviceId, Long taskId);

    /**
     * 查询脚本任务列表
     *
     * @param task 脚本任务
     * @return 脚本任务集合
     */
    List<Task> selectTaskList(Task task);

    /**
     * 新增脚本任务
     *
     * @param task 脚本任务
     * @return 结果
     */
    int insertTask(Task task);

    /**
     * 修改脚本任务
     *
     * @param task 脚本任务
     * @return 结果
     */
    int updateTask(Task task);

    /**
     * 删除脚本任务
     *
     * @param id 脚本任务主键
     * @return 结果
     */
    int deleteTaskById(Long id);

    /**
     * 批量删除脚本任务
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteTaskByIds(@Param("ids")Long[] ids);


    /**
     * 根据instanceId获取任务来源
     * @param taskInstanceId
     * @return
     */
    Integer getStartTypeByTaskInstanceId(@Param("taskInstanceId")Long taskInstanceId);

    /**
     * 查询任务申请发起的 各种状态的数据
     * @param taskExecuteBean
     * @param currentUser
     * @param mode pend - 未运行 running - 运行中-任务申请 running-test - 任务申请-测试 timetask - 定时任务维护 finish-test - 执行历史-测试 finish - 执行历史-完成
     * @return
     */
    List<TaskExecuteBean> selectTask(@Param("taskExecuteBean") TaskExecuteBean taskExecuteBean, @Param("currentUser") CurrentUser currentUser, @Param("mode") String mode);

    /**
     * 根据runtimeIds 查询agent excel导出数据
     * @param ids
     */
    List<TaskHisAgentExcelDto> getTaskHisExcelExport(@Param("taskInstanceIds") List<Long> ids);

    /**
     * 根据taskInstanceId查询任务名称和发布描述
     * @param taskInstanceId 任务实例表id
     * @return 任务名称和发布描述
     */
    Task selectTaskNameAndPublishDescByTaskInstanceId(@Param("taskInstanceId")Long taskInstanceId);


}
