package com.ideal.script.mapper;

import java.util.List;

import com.ideal.script.model.bean.TaskBindAgentInfoBean;
import com.ideal.script.model.dto.AgentInfoDto;
import com.ideal.script.model.entity.AgentInfo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

/**
 * Agent基本信息-冗余平台管理Mapper接口
 *
 * <AUTHOR>
 */
@Repository("scriptAgentInfoMapper")
public interface AgentInfoMapper {
    /**
     * 查询Agent基本信息-冗余平台管理
     *
     * @param id Agent基本信息-冗余平台管理主键
     * @return Agent基本信息-冗余平台管理
     */
    AgentInfo selectAgentInfoById(Long id);

    /**
     * 查询Agent基本信息-冗余平台管理列表
     *
     * @param agentInfo Agent基本信息-冗余平台管理
     * @return Agent基本信息-冗余平台管理集合
     */
    List<AgentInfo> selectAgentInfoList(AgentInfo agentInfo);

    /**
     * 新增Agent基本信息-冗余平台管理
     *
     * @param agentInfo Agent基本信息-冗余平台管理
     * @return 结果
     */
    int insertAgentInfo(AgentInfo agentInfo);

    /**
     * 修改Agent基本信息-冗余平台管理
     *
     * @param agentInfo Agent基本信息-冗余平台管理
     * @return 结果
     */
    int updateAgentInfo(AgentInfo agentInfo);

    /**
     * 删除Agent基本信息-冗余平台管理
     *
     * @param id Agent基本信息-冗余平台管理主键
     * @return 结果
     */
    int deleteAgentInfoById(Long id);

    /**
     * 批量删除Agent基本信息-冗余平台管理
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteAgentInfoByIds(@Param("ids") Long[] ids);


    /**
     * 根据任务taskId查询任务绑定的agent信息
     *
     * @param agentInfo    Agent信息
     * @param scriptTaskId 脚本任务主键
     * @param queryType 全部all  未运行null
     * @return List<TaskBindAgentInfoBean>
     * <AUTHOR>
     */
    List<TaskBindAgentInfoBean> getTaskBindAgentInfo(@Param("agentInfo") AgentInfo agentInfo, @Param("scriptTaskId") Long scriptTaskId, @Param("queryType") String queryType);

    /**
     * 查询runtime未运行的数据
     * @param agentInfo agent信息
     * @param scriptTaskId 任务id
     * @param queryType 查询条件
     * @return agent信息
     */
    List<TaskBindAgentInfoBean> getTaskRuntimeAgentInfo(@Param("agentInfo") AgentInfo agentInfo, @Param("scriptTaskId") Long scriptTaskId, @Param("queryType") String queryType);

    /**
     * 功能描述： 检查ieai_script_agent_info表中是否已存在相同的记录(IP+端口)
     *
     * @param agentIp   agentIp
     * @param agentPort agent端口
     * @return boolean
     * <AUTHOR>
     */
    boolean checkAgentInfoExists(@Param("agentIp") String agentIp, @Param("agentPort") Long agentPort);

    /**
     * 双人复核详情页面-查询服务器信息
     *
     * @param serviceId 业务主键
     * @return List<AgentInfo>
     */

    List<AgentInfo> selectAgentInfoByServiceId(Long serviceId,Long taskId);

    /**
     * 根据ip、端口批量查询agent数据
     * @param agentInfoDtoList 查询条件
     * @return agent结果集
     */
    List<AgentInfo> selectAgentInfoByIpAndPort(List<AgentInfoDto> agentInfoDtoList);
}
