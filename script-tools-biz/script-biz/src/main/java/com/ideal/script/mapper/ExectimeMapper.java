package com.ideal.script.mapper;

import com.ideal.script.model.entity.Exectime;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 脚本执行次数统计Mapper接口
 * 
 * <AUTHOR>
 */
public interface ExectimeMapper 
{
    /**
     * 查询脚本执行次数统计
     * 
     * @param id 脚本执行次数统计主键
     * @return 脚本执行次数统计
     */
     Exectime selectExectimeById(Long id);

    /**
     * 查询脚本执行次数统计列表
     * 
     * @param exectime 脚本执行次数统计
     * @return 脚本执行次数统计集合
     */
     List<Exectime> selectExectimeList(Exectime exectime);

    /**
     * 新增脚本执行次数统计
     * 
     * @param exectime 脚本执行次数统计
     * @return 结果
     */
     int insertExectime(Exectime exectime);

    /**
     * 修改脚本执行次数统计
     * 
     * @param exectime 脚本执行次数统计
     * @return 结果
     */
     int updateExectime(Exectime exectime);

    /**
     * 删除脚本执行次数统计
     * 
     * @param id 脚本执行次数统计主键
     * @return 结果
     */
     int deleteExectimeById(Long id);

    /**
     * 批量删除脚本执行次数统计
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
     int deleteExectimeByIds(@Param("ids")Long[] ids);

    /**
     * 更新执行次数
     * @param type 类型
     *  1：agent实例执行成功 总次数和成功次数都更新+1
     *  2：agent执行结果异常或者agent未连接上，那么只更新总次数加1
     *  3：点击任务执行时候只更新taskcount字段
     * @param srcScriptUuid 脚本UUID
     */
    void updateScriptExectime( @Param("type") Integer type, @Param("srcScriptUuid") String srcScriptUuid);

    /**
     * 查询脚本使用次数、成功数、总数、成功率
     *
     * @param srcScriptUuid 脚本版本Id
     * @return Exectime
     */
    Exectime getTotalAndSuccessRate(String srcScriptUuid);
}
