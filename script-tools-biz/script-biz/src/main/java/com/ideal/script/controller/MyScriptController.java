package com.ideal.script.controller;

import com.github.pagehelper.PageInfo;
import com.ideal.audit.producer.annotation.Auditable;
import com.ideal.common.dto.R;
import com.ideal.common.dto.TableQueryDto;
import com.ideal.common.util.BeanUtils;
import com.ideal.sc.util.CurrentUserUtil;
import com.ideal.sc.util.ValidationUtils;
import com.ideal.script.common.constant.Constants;
import com.ideal.script.common.constant.enums.ValidationMessages;
import com.ideal.script.common.constant.permission.MenuPermitConstant;
import com.ideal.script.common.constant.permission.MyScriptBtnPermitConstant;
import com.ideal.script.common.validation.Create;
import com.ideal.script.common.validation.Update;
import com.ideal.script.dto.*;
import com.ideal.script.exception.ScriptException;
import com.ideal.script.model.bean.ExecutorValidation;
import com.ideal.script.model.dto.CreatorTransferDto;
import com.ideal.script.model.dto.ScriptDeleteDto;
import com.ideal.script.model.dto.ScriptReferrerInfoDto;
import com.ideal.script.model.dto.ScriptValidationResultDto;
import com.ideal.script.model.dto.ScriptVersionInfoDto;
import com.ideal.script.model.dto.ValidationResultDto;
import com.ideal.script.model.dto.*;
import com.ideal.script.model.dto.client.ArtifactoryResponse;
import com.ideal.script.model.dto.client.BatchDownloadDto;
import com.ideal.script.service.IMyScriptService;
import com.ideal.script.service.IReleaseMediaService;
import com.ideal.script.service.IScriptReferrerInfoService;
import com.ideal.system.common.component.aop.MethodPermission;
import com.ideal.system.common.component.model.CurrentUser;
import com.ideal.system.dto.UserInfoApiDto;
import com.ideal.system.dto.UserInfoQueryDto;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RScoredSortedSet;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.*;

import static com.ideal.script.common.constant.Constants.LIST_SUCCESS;

/**
 * 我的脚本
 * <AUTHOR>
 */
@RestController
@RequestMapping("${app.script-tools-url:}/myScript")
@MethodPermission(MenuPermitConstant.MY_SCRIPT_PER)
public class MyScriptController {

    private static final Logger logger = LoggerFactory.getLogger(MyScriptController.class);

    private final IMyScriptService iMyScriptService;

    private final IReleaseMediaService releaseMediaService;

    private final RedissonClient redissonClient;

    private final IScriptReferrerInfoService scriptReferrerInfoService;

    public MyScriptController(IScriptReferrerInfoService scriptReferrerInfoService,IMyScriptService iMyScriptService, IReleaseMediaService releaseMediaService, RedissonClient redissonClient) {
        this.scriptReferrerInfoService = scriptReferrerInfoService;
        this.iMyScriptService = iMyScriptService;
        this.releaseMediaService = releaseMediaService;
        this.redissonClient = redissonClient;
    }

    private R<Object> handleValidationResult( ValidationResultDto res) {
        if (res != null && res.getParameterValidationResultDto() != null && res.getParameterValidationResultDto().getLine() != null) {
            logger.error("参数验证不通过");
            return R.fail(Constants.REPONSE_STATUS_VALIDATA_CODE, res.getParameterValidationResultDto(), ValidationMessages.PARAMETER_VALIDATION_FAILED_MESSAGE.getMessage());
        }
        if (res != null && null != res.getScriptValidationResultDtoList() && !res.getScriptValidationResultDtoList().isEmpty()) {
            for (ScriptValidationResultDto scriptValidationResultDto : res.getScriptValidationResultDtoList()) {
                if (scriptValidationResultDto.getType() == 1) {
                    logger.info("脚本中存在屏蔽关键命令，无法保存");
                    return R.fail(Constants.REPONSE_STATUS_VALIDATA_CODE, res.getScriptValidationResultDtoList(), ValidationMessages.SCRIPT_BLOCKED_MESSAGE.getMessage());
                }
            }
            logger.info("脚本中存在提醒关键命令，是否继续保存");
            return R.fail(Constants.REPONSE_STATUS_VALIDATA_CODE, res.getScriptValidationResultDtoList(), ValidationMessages.CONTAINS_KEYWORD_MESSAGE.getMessage());
        }
        return null;
    }

    @PostMapping("/saveMyScript")
    @Auditable("我的脚本|新增")
    @MethodPermission(MyScriptBtnPermitConstant.SAVE_SCRIPT_PER)
    public R<Object> saveMyScript(@RequestBody @Validated(Create.class) ScriptInfoDto scriptInfoDto) {
        try {
            //校验参数名称是否重复
            boolean scriptNameExistFlag = iMyScriptService.paramsNameExistCheck(scriptInfoDto);
            if(scriptNameExistFlag){
                logger.error("参数名称不能重复");
                return R.fail(Constants.REPONSE_STATUS_VALIDATA_CODE, "", ValidationMessages.PARAMETER_NAME_EXIST_MESSAGE.getMessage());
            }
            //根据
            ValidationResultDto res;
            //当存在id并且不存在版本，即为草稿进行更新，反之为版本脚本新增版本
            if(scriptInfoDto.getId() != null && StringUtils.isBlank(scriptInfoDto.getScriptVersionDto().getVersion())){
                logger.info("调用修改方法");
                res = iMyScriptService.updateMyScript(scriptInfoDto);
            }else {
                logger.info("调用新增方法");
                res = iMyScriptService.saveScript(scriptInfoDto);
            }
            //校验结果
            R<Object> validationResult = handleValidationResult(res);
            if (Objects.nonNull(validationResult) && StringUtils.equals(validationResult.getCode(),Constants.REPONSE_STATUS_VALIDATA_CODE)) {
                return validationResult;
            }
            logger.info("saveMyScript success");
            return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, scriptInfoDto,"保存成功");
        } catch (Exception e) {
            logger.error("saveMyScript error", e);
            return ValidationUtils.customFailResult("saveMyScript",e.getMessage());
        }
    }

    @PostMapping("/updateMyScript")
    @Auditable("我的脚本|编辑")
    @MethodPermission(MyScriptBtnPermitConstant.UPDATE_SCRIPT_PER)
    public R<Object> updateMyScript(@RequestBody @Validated(Update.class) ScriptInfoDto scriptInfoDto) {
        try {
            //校验参数名称是否重复
            boolean scriptNameExistFlag = iMyScriptService.paramsNameExistCheck(scriptInfoDto);
            if(scriptNameExistFlag){
                logger.error("参数名称不能重复");
                return R.fail(Constants.REPONSE_STATUS_VALIDATA_CODE, "", ValidationMessages.PARAMETER_NAME_EXIST_MESSAGE.getMessage());
            }
            ValidationResultDto res = iMyScriptService.updateMyScript(scriptInfoDto);
            //校验结果
            R<Object> validationResult = handleValidationResult(res);
            if (Objects.nonNull(validationResult) && StringUtils.equals(validationResult.getCode(),Constants.REPONSE_STATUS_VALIDATA_CODE)) {
                return validationResult;
            }
            logger.info("updateMyScript success");
            return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, scriptInfoDto,"修改成功");
        } catch (Exception e) {
            logger.error("updateMyScript error", e);
            return ValidationUtils.customFailResult("updateScript",e.getMessage());
        }
    }

    /**
     * 我的脚本查询列表
     * @param tableQueryDTO 请求参数
     * @return 脚本列表数据
     */
    @PostMapping("/listMyScript")
    public R<PageInfo<ScriptInfoApiDto>> listMyScript(@RequestBody TableQueryDto<ScriptInfoQueryDto> tableQueryDTO) {
        ScriptInfoQueryDto scriptInfoQueryDto = BeanUtils.copy(tableQueryDTO.getQueryParam(),ScriptInfoQueryDto.class);
        scriptInfoQueryDto.setPageSize(tableQueryDTO.getPageSize());
        scriptInfoQueryDto.setPageNum(tableQueryDTO.getPageNum());
        scriptInfoQueryDto.setDubboFlag(false);
        return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, iMyScriptService.selectScriptPageList(scriptInfoQueryDto), "查询成功");
    }
    /**
     * 获取脚本详情
     * 可以按照默认版本查询，也可以按照指定版本查询，俩种参数必须二选一，使用`查询能申请任务的脚本数据列表`接口获取俩个参数id
     * 1. 指定版本传入srcScriptUuid即可
     * 2. 默认版本传入scriptInfoId即可
     * @tags 对外API接口,兴业
     * @param scriptInfoQueryDto 脚本查询条件
     * @return 返回脚本信息
     */
    @PostMapping("/getScriptDetail")
    @MethodPermission(MenuPermitConstant.MY_SCRIPT_OR_TASK_APPLY_OR_TASK_APPLY_DUTY_OR_TEMPLATE_TASK_OR_DOUBLE_CHECK_PER)
    public R<ScriptInfoDto> getScriptDetail(@RequestBody ScriptInfoQueryDto scriptInfoQueryDto) throws ScriptException{
        scriptInfoQueryDto.setDubboFlag(false);
        ScriptInfoDto scriptDetail = iMyScriptService.getScriptDetail(scriptInfoQueryDto);
        return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, scriptDetail, Objects.nonNull(scriptDetail.getId()) ? "查询成功" : "未查询到数据");
    }

    @PostMapping("/deleteMyScript")
    @Auditable("我的脚本|删除")
    @MethodPermission(MyScriptBtnPermitConstant.REMOVE_SCRIPT_PER)
    public R<Object> deleteMyScript(@RequestBody ScriptDeleteDto scriptDeleteDto) {
        try {
            iMyScriptService.deleteMyScript(scriptDeleteDto, false);
            return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, "删除成功");
        } catch (ScriptException e) {
            return ValidationUtils.customFailResult("deleteScript",e.getMessage());
        }
    }

    @PostMapping("/publishScript")
    @Auditable("我的脚本|发布")
    @MethodPermission(MyScriptBtnPermitConstant.PUBLISH_SCRIPT_PER)
    public R<Object> publishScript(@RequestBody PublishDto publishDto) throws ScriptException {
        if (null == publishDto.getIds() || publishDto.getIds().length == 0) {
            return R.fail(Constants.REPONSE_STATUS_VALIDATA_CODE, "请校验参数");
        }
        try {
            iMyScriptService.publishScript(publishDto);
            return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, "发布成功");
        } catch (ScriptException e) {
            return ValidationUtils.customFailResult("publishScript",e.getMessage());
        }
    }

    /**
     * 与第三方itsm对接，发布脚本前需要上传脚本附件
     * @param srcScriptUuid 脚本版本uuid
     * @return 附件id
     * @throws ScriptException 脚本服务化异常
     */
    @PostMapping("/uploadItsmScriptAttachments")
    @Auditable("我的脚本|发布前上传附件")
    public R<Object> uploadItsmScriptAttachments(@RequestBody String srcScriptUuid) throws ScriptException {
        return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, iMyScriptService.uploadItsmScriptAttachments(srcScriptUuid), "上传附件成功");
    }

    /**
     * 校验我的脚本草稿是否是打回
     * @param scriptInfoVersionId 脚本id
     * @return 返回是否为打回数据
     */
    @PostMapping("/getDoubleCheckScriptFlag")
    @MethodPermission(MenuPermitConstant.MY_SCRIPT_OR_DOUBLE_CHECK_PER)
    public R<Object> publishScript(@RequestBody Long [] scriptInfoVersionId)  {
        if (scriptInfoVersionId == null || scriptInfoVersionId.length == 0) {
            // 处理空数组的逻辑
            return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, "不存在发布审核打回脚本");
        }
        boolean doubleCheckScriptFlag = iMyScriptService.getDoubleCheckScriptFlag(scriptInfoVersionId);
        //脚本为发布打回情况，这时需要调用双人复核归档接口，将双人复核数据归档，然后删除本地脚本
        if(doubleCheckScriptFlag){
            iMyScriptService.doubleCheckAutoArchive(scriptInfoVersionId);
        }
        return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, "不存在发布审核打回脚本");

    }

    /**
     * 脚本下载
     *
     * @param ids      版本id
     * @param response 请求
     */
    @GetMapping("/scriptDownload")
    @Auditable("我的脚本|下载脚本")
    @MethodPermission(MyScriptBtnPermitConstant.SCRIPT_DOWNLOAD_PER)
    public void scriptDownload(@RequestParam(value = "ids") Long[] ids, HttpServletResponse response) {
        boolean isDownload = iMyScriptService.downloadScript(ids, response);
        if (isDownload) {
            logger.info("scriptDownload success");
        } else {
            logger.error("scriptDownload error");
        }
    }


    /**
     * 查询脚本的全部版本的脚本信息
     *
     * @param serviceUuid 脚本uuid
     * @return list
     */
    @GetMapping("/getScriptServiceVersionListForAllScript")
    public R<List<ScriptVersionInfoDto>> getScriptServiceVersionListForAllScript(@RequestParam(value = "serviceUuid") String serviceUuid) {
        return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, iMyScriptService.getScriptServiceVersionListForAllScript(serviceUuid), "查询成功");
    }

    /**
     * 版本回退(最新脚本无版本)
     *
     * @param iid     回退到的id
     * @param oldId   需要回退的id
     * @param uuid    回退到的uuid
     * @param oldUuid 需要回退的uuid
     * @return object
     */
    @GetMapping("/noVersionRollBack")
    @Auditable("我的脚本|查看版本-版本回退")
    @MethodPermission(MyScriptBtnPermitConstant.VERSION_ROLL_BACK_PER)
    public R<Object> noVersionRollBack(@RequestParam(value = "iid") Long iid, @RequestParam(value = "oldId") Long oldId, @RequestParam(value = "uuid") String uuid, @RequestParam(value = "oldUuid") String oldUuid) {
        Map<String, Object> rollBackMap = iMyScriptService.noVersionRollBack(iid, oldId, oldUuid, uuid);
        return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, rollBackMap.get("success").toString() , rollBackMap.get("message").toString());
    }

    /**
     * 版本回退（最新脚本有版本）
     *
     * @param iid   回退到的id
     * @param oldId 回退的id
     * @return object
     */
    @GetMapping("/hasVersionRollBack")
    @Auditable("我的脚本|查看版本-版本回退")
    @MethodPermission(MyScriptBtnPermitConstant.VERSION_ROLL_BACK_PER)
    public R<Object> hasVersionRollBack(@RequestParam(value = "iid") Long iid, @RequestParam(value = "oldId") Long oldId){
        Map<String, Object> rollBackMap = iMyScriptService.hasVersionRollBack(iid, oldId);
        return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, rollBackMap.get("success").toString() , rollBackMap.get("message").toString());
    }

    /**
     * 查询基础数据管理-用户管理 分页所有用户
     * @param userInfoQueryDto 查询参数对象
     * @return 返回用户信息
     */
    @PostMapping("/getUserInfoList")
    public R<PageInfo<UserInfoApiDto>> getUserInfoList(@RequestBody UserInfoQueryDto userInfoQueryDto) {
        return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, iMyScriptService.getUserInfoList(userInfoQueryDto), "查询成功");
    }

    /**
     * 创建者转移功能
     * @param creatorTransferDto
     * @return
     */
    @PostMapping("/creatorTransfer")
    @Auditable("我的脚本|创建者转移")
    @MethodPermission(MyScriptBtnPermitConstant.CREATOR_TRANSFER_PER)
    public R<Object> creatorTransfer(@RequestBody CreatorTransferDto creatorTransferDto) {
        try {
            iMyScriptService.creatorTransfer(creatorTransferDto);
            return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, "创建者转移成功");
        } catch (Exception e) {
            logger.error("creatorTransfer error", e);
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, "创建者转移失败");
        }
    }


    /**
     * 我的脚本投产介质导出
     *
     * @param ids      版本id集合
     * @param response 响应
     */
    @GetMapping("/myScriptExportReleaseMedia")
    @Auditable("脚本分类|分类保存")
    @MethodPermission(MyScriptBtnPermitConstant.EXPORT_RELEASE_MEDIA_PER)
    public void myScriptExportReleaseMedia(@RequestParam(value = "ids") Long[] ids, HttpServletResponse response) {
        try {
            Long [] scriptVersionIds = iMyScriptService.getDefaultVersionIds(ids);
            if(null != scriptVersionIds && scriptVersionIds.length > 0){
                releaseMediaService.exportReleaseMedia(scriptVersionIds, response);
            }
            logger.info("exportReleaseMedia success");
        } catch (ScriptException e) {
            logger.error("exportReleaseMedia error", e);
        }
    }

    @PostMapping("/ignoreScriptRevision")
    @Auditable("我的脚本|不修订")
    @MethodPermission(MyScriptBtnPermitConstant.CONFIRM_SCRIPT_PER)
    public R<Object> ignoreScriptRevision(@RequestBody List<Long> ids){
        CurrentUser currentUser = CurrentUserUtil.getCurrentUser();
        iMyScriptService.ignoreScriptRevision(ids,currentUser);
        return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, "已确认");
    }

    /**
     * 获取是否允许删除标识，如果是双人复核打回的，页面上不允许删除
     * @param scriptAuditRelationId 脚本服务化审核关系表id
     * @return 返回是否允许删除标识
     * @throws ScriptException 脚本服务化异常
     */
    @PostMapping("/getDeleteFlag")
    public R<Object> getDeleteFlag(@RequestBody Long scriptAuditRelationId) throws ScriptException {
        if(!ObjectUtils.isEmpty(scriptAuditRelationId)){
            boolean doubleCheckScriptFlag = iMyScriptService.getDelDoubleCheckScriptFlag(scriptAuditRelationId);
            if(!doubleCheckScriptFlag){
                return R.fail(Constants.REPONSE_STATUS_SUSSCESS_CODE, "脚本不是发布审核打回");
            }
        }
        return R.ok(Constants.REPONSE_STATUS_VALIDATA_CODE, "脚本为发布审核打回");
    }

    @PostMapping("/getLabelList")
    public R<Set<String>> getLabelList(@RequestBody ScriptInfoQueryDto scriptInfoQueryDto){
      // 首先查看 Redis 中有没有
        RScoredSortedSet<String> zSet = redissonClient.getScoredSortedSet("script_labelZSet");
        Set<String> labelList;

        if (!zSet.isEmpty()) {
            labelList = new HashSet<>(zSet.readAll());
        } else {
            // 如果 Redis 中没有数据，从数据库获取数据
            labelList = iMyScriptService.getLabelList();
        }

        return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, labelList, LIST_SUCCESS);
    }

    @GetMapping("/getExecutorValidationList")
    public R<ExecutorValidation> getExecutorValidationList(){
        return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, iMyScriptService.getExecutorValidationList(), LIST_SUCCESS);
    }

    /**
     * 获取是否展示sql类型脚本标识
     * @return 开关值
     */
    @GetMapping("/getSqlShowFlag")
    public R<Object> getTaskCountByVersionId (){
            return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, iMyScriptService.getSqlShowFlag(),"");
    }

    /**
     * 获取发布脚本为白名单标识
     * @return 开关值
     */
    @GetMapping("/getPublishScriptWhiteFlag")
    public R<Object> getPublishScriptWhiteFlag(){
        return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, iMyScriptService.getPublishScriptWhiteFlag(),"");
    }

    /**
     * 获取发布脚本为白名单标识
     * @return 开关值
     */
    @GetMapping("/getRolePermissionFlag")
    public R<Object> getRolePermissionFlag(){
        return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, iMyScriptService.getRolePermissionFlag(),"");
    }

    /**
     * 查询引用情况数据
     * @tags 对外API接口,中信
     * @param tableQueryDTO 查询对象
     * @return 分页数据
     */
    @PostMapping("/myScriptQuote")
    public R<PageInfo<ScriptReferrerInfoDto>> myScriptQuote(@RequestBody TableQueryDto<ScriptReferrerInfoDto> tableQueryDTO) {
        return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, scriptReferrerInfoService.selectPageList(tableQueryDTO.getQueryParam(), tableQueryDTO.getPageNum(),
                tableQueryDTO.getPageSize()), LIST_SUCCESS);
    }

    /**
     * 获取脚本配置参数
     * @return 配置的参数内容
     */
    @GetMapping("/getScriptUpgradeTypeFlag")
    public R<Object> getScriptUpgradeTypeFlag (){
        return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, iMyScriptService.getScriptUpgradeTypeFlag(),"");
    }

    /**
     * 获取脚本执行用户必填开关标识
     * @return 是否必填脚本用户
     */
    @GetMapping("/getScriptUserRequiredFlag")
    public R<Boolean> getScriptUserRequiredFlag (){
        return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, iMyScriptService.getScriptUserRequiredFlag(),"");
    }

    /**
     * 脚本来源是否展示
     * @return 来源开关值
     */
    @GetMapping("/getScriptSourceShowFlag")
    public Object getScriptSourceShowFlag (){
        return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, iMyScriptService.getScriptSourceShowFlag(),"");
    }

    /**
     * 上传脚本压缩包（投产介质）zip文件到制品库
     * @param scriptInfoIds 要晋级的脚本id
     * @return 返回上传结果
     * @throws ScriptException 脚本服务化异常
     */
    @GetMapping("/scriptPromotion")
    @Auditable("我的脚本|上传制品库")
    @MethodPermission(MyScriptBtnPermitConstant.UPLOAD_PRODUCT_LIBRARY_PER)
    public R<Object> scriptPromotion(@RequestParam(value = "scriptInfoIds") Long [] scriptInfoIds) throws ScriptException {
        iMyScriptService.scriptPromotion(scriptInfoIds);
        return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE,"","提交成功");
    }

    /**
     * 根据制品库地址下载zip文件并执行服务投产
     * @param request 请求request
     * @return 返回提示信息
     * @throws ScriptException 脚本服务化异常
     */
    @PostMapping("/downloadPromotionScript")
    public R<Object> downloadPromotionScript(@Valid @RequestBody BatchDownloadDto request) throws ScriptException{

        logger.info("开始批量下载文件 - repo: {}, localBasePath: {}, fileCount: {}",
                request.getRepoKey(), request.getLocalBasePath(),
                request.getFilePath());
        iMyScriptService.downloadPromotionScript(request.getRepoKey(),request.getFilePath(),request.getLocalBasePath());
        return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE,"","晋级成功");
    }

}
