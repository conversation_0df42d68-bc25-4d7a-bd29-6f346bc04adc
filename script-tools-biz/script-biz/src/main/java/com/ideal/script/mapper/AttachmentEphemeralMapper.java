package com.ideal.script.mapper;

import com.ideal.script.model.entity.Attachment;

import java.util.List;

/**
 * 【接口上传附件】Mapper接口
 * 
 * <AUTHOR>
 */
public interface AttachmentEphemeralMapper
{

    /**
     * 根据id查询附件
     * @param ids 附件id集合
     * @return 附件集合
     */
    List<Attachment> selectAttachmentByIds(Long[] ids);

    /**
     * 新增
     * 
     * @param attachment 
     * @return 结果
     */
     int insertAttachment(Attachment attachment);

    /**
     * 删除
     * 
     * @param id 主键
     * @return 结果
     */
     int deleteAttachmentById(Long id);

}
