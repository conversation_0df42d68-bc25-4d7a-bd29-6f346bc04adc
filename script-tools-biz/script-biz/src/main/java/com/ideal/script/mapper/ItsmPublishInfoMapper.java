package com.ideal.script.mapper;

import com.ideal.script.model.entity.ItsmPublishScript;
import org.springframework.data.repository.query.Param;

import java.util.List;

/**
 * 【itsm工单与脚本信息表】Mapper接口
 *
 * <AUTHOR>
 */
public interface ItsmPublishInfoMapper {
    int insertItsmPublishInfo(ItsmPublishScript itsmPublishScript);

    List<ItsmPublishScript> getAuditingDataBySrcScriptUuid(@Param("srcScriptUuid") String srcScriptUuid);

    ItsmPublishScript getDetailByOrderNumber(@Param("orderNumber") String orderNumber);

    int deleteByIid(@Param("iid") Long iid);
}
