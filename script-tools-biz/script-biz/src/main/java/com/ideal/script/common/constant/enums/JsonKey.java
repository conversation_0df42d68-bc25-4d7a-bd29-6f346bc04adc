package com.ideal.script.common.constant.enums;

/**
 * <AUTHOR>
 */
public enum Json<PERSON><PERSON> {
    /**
     * scriptId
     */
    SCRIPT_ID("scriptId"),
    STDOUT("stdout"),
    LAST_LINE("lastLine"),
    STDERR("stderr"),
    TRANSCODING("transcoding"),
    STATUS("status");

    private final String key;

    JsonK<PERSON>(String key) {
        this.key = key;
    }

    public String getKey() {
        return key;
    }
}
