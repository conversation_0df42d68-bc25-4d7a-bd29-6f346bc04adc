package com.ideal.script.mapper;

import com.ideal.script.model.bean.ParameterValidationBean;
import com.ideal.script.model.entity.Parameter;
import com.ideal.script.model.entity.TaskParams;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Mapper接口
 * 
 * <AUTHOR>
 */
public interface ParameterMapper 
{
    /**
     * 查询
     * 
     * @param id 主键
     * @return 
     */
     Parameter selectParameterById(Long id);

    /**
     * 查询列表
     * 
     * @param parameter 
     * @return 集合
     */
     List<Parameter> selectParameterList(Parameter parameter);

    /**
     * 查询参数及校验规则
     * @param parameter 参数实体
     * @return  结果
     */
    List<ParameterValidationBean> selectParameterValidationList (Parameter parameter);
    /**
     * 新增
     * 
     * @param parameter 
     * @return 结果
     */
     int insertParameter(Parameter parameter);

    /**
     * 修改
     * 
     * @param parameter 
     * @return 结果
     */
     int updateParameter(Parameter parameter);

    /**
     * 删除
     * 
     * @param id 主键
     * @return 结果
     */
     int deleteParameterById(Long id);

    /**
     * 批量删除
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
     int deleteParameterByIds(@Param("ids")Long[] ids);

    /**
     * 删除
     * @param scriptUuid    脚本uuid
     * @return  结果
     */
     int deleteParameterByScriptUuid(String scriptUuid);

    /**
     * 删除
     * @param scriptUuid    脚本uuid
     * @return  结果
     */

     int deleteParameterByScriptUuids(String[] scriptUuid);
    
    
	/**
	 *  根据脚本id获取脚本参数
	 * @param scriptId  脚本id
	 * @return  结果
	 */
	List<Parameter> selectParameterListByScriptId(Long scriptId);

    /**
     * 删除
     * @param oldUuid   uuid
     * @return     结果
     */
    int deleteParameterByUuid(String oldUuid);
    /**
     * 查询
     * @param uuid  版本uuid
     * @return     结果
     */
    List<Parameter> getParameterByUuid( @Param("uuid") String uuid);

    List<TaskParams> getParameterByTaskId(Long taskId);
}
