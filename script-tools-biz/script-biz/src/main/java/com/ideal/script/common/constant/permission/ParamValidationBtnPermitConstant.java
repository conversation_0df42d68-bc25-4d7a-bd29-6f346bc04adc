package com.ideal.script.common.constant.permission;

/**
 * 参数验证菜单按钮权限常量类
 *
 * <AUTHOR>
 */
public class ParamValidationBtnPermitConstant {

    // 权限表达式前缀和连接符
    private static final String PER_PREFIX = "@dp.hasBtnPermission('";
    private static final String PER_SUFFIX = "')";

    /**
     * 删除按钮权限码
     */
    public static final String REMOVE_PARAMETER_CHECK = "removeParameterCheck";

    /**
     * 新增按钮权限码
     */
    public static final String SAVE_PARAMETER_CHECK = "saveParameterCheck";

    /**
     * 编辑按钮权限码
     */
    public static final String UPDATE_PARAMETER_CHECK = "updateParameterCheck";

    /**
     * 删除按钮权限表达式
     */
    public static final String REMOVE_PARAMETER_CHECK_PER = PER_PREFIX + REMOVE_PARAMETER_CHECK + PER_SUFFIX;



    /**
     * 新增按钮权限表达式
     */
    public static final String SAVE_PARAMETER_CHECK_PER = PER_PREFIX + SAVE_PARAMETER_CHECK + PER_SUFFIX;

    /**
     * 编辑按钮权限表达式
     */
    public static final String UPDATE_PARAMETER_CHECK_PER = PER_PREFIX + UPDATE_PARAMETER_CHECK + PER_SUFFIX;
}
