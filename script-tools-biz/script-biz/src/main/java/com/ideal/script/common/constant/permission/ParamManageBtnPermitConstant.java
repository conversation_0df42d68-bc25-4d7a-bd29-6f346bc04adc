package com.ideal.script.common.constant.permission;

/**
 * 参数管理菜单按钮权限常量类
 *
 * <AUTHOR>
 */
public class ParamManageBtnPermitConstant {

    // 权限表达式前缀和连接符
    private static final String PER_PREFIX = "@dp.hasBtnPermission('";
    private static final String PER_SUFFIX = "')";

    /**
     * 删除按钮权限码
     */
    public static final String ENUM_PARAMS_DEL_PERM = "enumParamsDelPerm";

    /**
     * 编辑按钮权限码
     */
    public static final String ENUM_PARAMS_EDIT_PERM = "enumParamsEditPerm";

    /**
     * 新增按钮权限码
     */
    public static final String ENUM_PARAMS_ADD_PERM = "enumParamsAddPerm";

    /**
     * 删除按钮权限表达式
     */
    public static final String ENUM_PARAMS_DEL_PERM_PER = PER_PREFIX + ENUM_PARAMS_DEL_PERM + PER_SUFFIX;



    /**
     * 编辑按钮权限表达式
     */
    public static final String ENUM_PARAMS_EDIT_PERM_PER = PER_PREFIX + ENUM_PARAMS_EDIT_PERM + PER_SUFFIX;

    /**
     * 新增按钮权限表达式
     */
    public static final String ENUM_PARAMS_ADD_PERM_PER = PER_PREFIX + ENUM_PARAMS_ADD_PERM + PER_SUFFIX;
}
