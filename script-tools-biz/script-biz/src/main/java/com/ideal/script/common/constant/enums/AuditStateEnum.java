package com.ideal.script.common.constant.enums;

/**
 * 审核状态枚举
 * <AUTHOR>
 */
public enum AuditStateEnum {
    /**
     * 审批中
     */
    PENDING(1,"审批中"),
    /**
     * 已通过
     */
    APPROVED(2,"已通过"),
    /**
     * 已退回
     */
    REJECTED(3,"已退回");

    private final int type;
    private final String  description;

    AuditStateEnum(int type, String description) {
        this.type = type;
        this.description = description;
    }

    public int getType() {
        return type;
    }

    public String getDescription() {
        return description;
    }
}
