package com.ideal.script.common.constant.enums;

/**
 * 编辑状态枚举类
 *
 * <AUTHOR>
 */
public enum EditStateEnum {
    /**
     * 可编辑
     */
    ENABLE(0,"草稿"),
    /**
     * 不可编辑
     */
    PUBLISH(1,"发布"),
    /**
     * 审核中
     */
    REVIEW(2,"审核中");
    private final int type;
    private final String description;

    EditStateEnum(int type, String description) {
        this.type = type;
        this.description = description;
    }

    public int getType() {
        return type;
    }

    public String getDescription() {
        return description;
    }
}
