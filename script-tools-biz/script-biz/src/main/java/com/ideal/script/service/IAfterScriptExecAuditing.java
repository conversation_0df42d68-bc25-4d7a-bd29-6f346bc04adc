package com.ideal.script.service;

import com.ideal.script.model.dto.cibitsm.FlowAutoTemplateResultDto;

import java.util.List;

public interface IAfterScriptExecAuditing {

    /**
     * cib，任务申请结束后给itsm推送消息
     * @param result 执行结果
     * @param workOrderNum 工单编号
     */
    void cibItsmAuditingResult(Object result,String workOrderNum);

    /**
     * cib，任务执行结束后给itsm推送消息
     * @param taskId 任务id
     */
    void scriptTaskFinishedToItsm(Long taskId);

    /**
     * 任务申请结束推送itsm结果
     * @param code 单号
     * @param user 用户
     * @param flowAutoTemplateResultList 列表信息
     */
    void scriptTaskApplyFinishedToItsm(String code, String user, List<FlowAutoTemplateResultDto> flowAutoTemplateResultList);

    /**
     * 获取itsm的token
     * @return token
     */
    String getItsmToken();
}
