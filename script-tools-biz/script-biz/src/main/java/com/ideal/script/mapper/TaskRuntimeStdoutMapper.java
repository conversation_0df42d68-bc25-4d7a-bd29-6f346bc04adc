package com.ideal.script.mapper;

import com.ideal.script.model.entity.TaskRuntimeStdout;

/**
 * agent运行实例标准输出
 *
 * <AUTHOR>
 */
public interface TaskRuntimeStdoutMapper {
    /**
     * 根据taskRunTimeId查询agent标准输出
     *
     * @param id agent运行实例主键
     * @return agent运行实例标准输出
     */
    TaskRuntimeStdout selectStdoutByTaskRuntimeId(Long id);

    /**
     * 插入标准输出业务数据
     *
     * @param taskRuntimeStdout TaskRuntimeStdout
     * @return 执行成功条数
     */
    int insert(TaskRuntimeStdout taskRuntimeStdout);


    /**
     * 更新标准输出业务数据
     *
     * @param taskRuntimeStdout TaskRuntimeStdout
     * @return 执行成功条数
     */
    int updateByRunTimeId(TaskRuntimeStdout taskRuntimeStdout);


}
