package com.ideal.script.common.constant.permission;

/**
 * 关键命令菜单按钮权限常量类
 *
 * <AUTHOR>
 */
public class KeyCommandsBtnPermitConstant {

    // 权限表达式前缀和连接符
    private static final String PER_PREFIX = "@dp.hasBtnPermission('";
    private static final String PER_SUFFIX = "')";

    /**
     * 新增按钮权限码
     */
    public static final String SAVE_DANGER_CMD = "saveDangerCmd";

    /**
     * 编辑按钮权限码
     */
    public static final String UPDATE_DANGER_CMD = "updateDangerCmd";

    /**
     * 删除按钮权限码
     */
    public static final String REMOVEL_DANGER_CMD = "removelDangerCmd";

    /**
     * 新增按钮权限表达式
     */
    public static final String SAVE_DANGER_CMD_PER = PER_PREFIX + SAVE_DANGER_CMD + PER_SUFFIX;



    /**
     * 编辑按钮权限表达式
     */
    public static final String UPDATE_DANGER_CMD_PER = PER_PREFIX + UPDATE_DANGER_CMD + PER_SUFFIX;

    /**
     * 删除按钮权限表达式
     */
    public static final String REMOVEL_DANGER_CMD_PER = PER_PREFIX + REMOVEL_DANGER_CMD + PER_SUFFIX;
}
