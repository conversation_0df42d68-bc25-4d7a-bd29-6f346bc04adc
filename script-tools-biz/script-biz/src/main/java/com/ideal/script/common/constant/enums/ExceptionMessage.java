package com.ideal.script.common.constant.enums;

/**
 * 异常提示信息枚举类
 * <AUTHOR>
 */
public enum ExceptionMessage {

    DOUBLE_CHECK_SUBMISSION_ERROR("double.check.submission.error"),
    SCRIPT_NAME_EXISTS("script.name.exists"),
    IMPORT_RELEASE_MEDIA_ERROR("import.release.media.error"),
    IMPORT_RELEASE_MEDIA_SAME_SCRIPT("import.release.media.same.script"),
    BUILD_TASK_APPLY_MESSAGE_ERROR("build.task.apply.message.error");
    // 定义更多的异常消息

    private final String messageKey;

    ExceptionMessage(String messageKey) {
        this.messageKey = messageKey;
    }

    public String getValue() {
        return messageKey;
    }
}
