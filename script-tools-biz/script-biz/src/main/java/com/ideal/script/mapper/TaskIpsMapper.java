package com.ideal.script.mapper;

import java.util.List;

import com.ideal.script.model.bean.TaskIpsAgentResultBean;
import com.ideal.script.model.entity.TaskIps;
import org.apache.ibatis.annotations.Param;

/**
 * 任务与agent关系Mapper接口
 * 
 * <AUTHOR>
 */
public interface TaskIpsMapper 
{
    /**
     * 查询任务与agent关系
     * 
     * @param id 任务与agent关系主键
     * @return 任务与agent关系
     */
     TaskIps selectTaskIpsById(Long id);

    /**
     * 查询任务与agent关系列表
     * 
     * @param taskIps 任务与agent关系
     * @return 任务与agent关系集合
     */
     List<TaskIps> selectTaskIpsList(TaskIps taskIps);

    /**
     * 新增任务与agent关系
     * 
     * @param taskIps 任务与agent关系
     * @return 结果
     */
     int insertTaskIps(TaskIps taskIps);

    /**
     * 修改任务与agent关系
     * 
     * @param taskIps 任务与agent关系
     * @return 结果
     */
     int updateTaskIps(TaskIps taskIps);

    /**
     * 删除任务与agent关系
     * 
     * @param id 任务与agent关系主键
     * @return 结果
     */
     int deleteTaskIpsById(Long id);

    /**
     * 删除任务与agent关系
     *
     * @param taskId 任务id
     * @return 结果
     */
    int deleteTaskIpsByTaskId(Long taskId);

    /**
     * 批量删除任务与agent关系
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
     int deleteTaskIpsByIds(@Param("ids")Long[] ids);

    /**
     * 查询脚本任务指定运行的agent总数
     *
     * @param scriptTaskId 脚本任务id
     * @return int
     * <AUTHOR>
     */
    int getTotalAgentCountForTask(Long scriptTaskId);

    /**
     * 查询脚本任务绑定的Agent中未运行的agent总数
     *
     * @param scriptTaskId 脚本任务id
     * @return int
     * <AUTHOR>
     */
    int getUnexecutedAgentCountForTask(Long scriptTaskId);

    /**
     *  获取当前任务在分批执行过程中，当前的最大执行批次
     *
     * @param scriptTaskId 脚本任务id
     * @return int
     * <AUTHOR>
     */
    int getMaxOperIdForTask(Long scriptTaskId);

    /**
     * 将选定的agent标记为已执行状态，并将本次脚本任务的执行批次号加一进行更新。
     *
     * @param taskIpIds      选定的执行agent （ieai_script_task_ips表Id集合）
     * @param executionBatch 执行批次
     * @return int
     * <AUTHOR>
     */
    int updateIpsStatusAndIncrementOperId(@Param("taskIpIds") Long[] taskIpIds, @Param("executionBatch") int executionBatch);

    /**
     * 获取选定的agent的信息
     *
     * @param runtimeIds agent实例id
     * @param taskId 任务id
     * @return List<TaskIpsAgentResultBean>
     * <AUTHOR>
     */
    List<TaskIpsAgentResultBean> getTaskIpsInfo(@Param("runtimeIds") Long [] runtimeIds,@Param("taskId") Long taskId);

    /**
     * 获取脚本任务绑定的agent
     *
     * @param scriptTaskId 脚本任务id
     * @return List<TaskIpsAgentResultBean>
     * <AUTHOR>
     */
    List<TaskIpsAgentResultBean> getBindAllTaskIpsInfo(@Param("scriptTaskId") Long scriptTaskId);


    /**
     * 根据taskId获取本次任务的所有ips
     * @param scriptTaskId
     * @return
     */
    List<TaskIpsAgentResultBean> getBindExecTaskIpsInfo(@Param("scriptTaskId") Long scriptTaskId);

    /**
     * 获取非首批ipsId
     * @param scriptTaskId 任务id
     * @param ipsIds ipsid
     * @return ips对象
     */
    List<TaskIpsAgentResultBean> getNotFirstBatchIpsId(@Param("scriptTaskId") Long scriptTaskId,@Param("ipsIds") Long [] ipsIds);

    /**
     * 获取运行中agent的基础信息（实时获取agent输出用到此方法）
     *
     * @param taskRuntimeId agent运行实例Id
     * @return List<TaskIpsAgentResultBean>
     * <AUTHOR>
     */
    List<TaskIpsAgentResultBean> getTaskIpsInfoByRuntimeId(@Param("taskRuntimeId") Long taskRuntimeId);

}
