package com.ideal.script.config;

/**
 * citic个性化配置
 *
 * <AUTHOR>
 **/
public class CiticProperties {

    /**
     * itsm-repoKey
     */
    private String itsmRepoKey;
    /**
     * itsm-apiKey
     */
    private String itsmApiKey;
    /**
     * itsm获取工单接口url
     */
    private String itsmGetOrderNumberUrl;
    /**
     * itsm上传制品接口url
     */
    private String itsmUploadProductFileUrl;
    /**
     * itsm下载制品接口
     */
    private String itsmDownloadProductFileUrl;

    public String getItsmRepoKey() {
        return itsmRepoKey;
    }

    public void setItsmRepoKey(String itsmRepoKey) {
        this.itsmRepoKey = itsmRepoKey;
    }

    public String getItsmApiKey() {
        return itsmApiKey;
    }

    public void setItsmApiKey(String itsmApiKey) {
        this.itsmApiKey = itsmApiKey;
    }

    public String getItsmGetOrderNumberUrl() {
        return itsmGetOrderNumberUrl;
    }

    public void setItsmGetOrderNumberUrl(String itsmGetOrderNumberUrl) {
        this.itsmGetOrderNumberUrl = itsmGetOrderNumberUrl;
    }

    public String getItsmUploadProductFileUrl() {
        return itsmUploadProductFileUrl;
    }

    public void setItsmUploadProductFileUrl(String itsmUploadProductFileUrl) {
        this.itsmUploadProductFileUrl = itsmUploadProductFileUrl;
    }

    public String getItsmDownloadProductFileUrl() {
        return itsmDownloadProductFileUrl;
    }

    public void setItsmDownloadProductFileUrl(String itsmDownloadProductFileUrl) {
        this.itsmDownloadProductFileUrl = itsmDownloadProductFileUrl;
    }
}
