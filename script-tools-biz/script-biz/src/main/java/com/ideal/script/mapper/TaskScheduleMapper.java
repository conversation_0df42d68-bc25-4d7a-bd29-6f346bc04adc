package com.ideal.script.mapper;

import com.ideal.script.model.entity.TaskScheduleEntity;

import java.util.List;

/**
 * Mapper接口
 *
 * <AUTHOR>
 * @date 2024-04-07
 */
public interface TaskScheduleMapper {
    /**
     * 查询
     *
     * @param id 主键
     * @return 
     */
    TaskScheduleEntity selectTaskScheduleById(Long id);

    /**
     * 查询列表
     *
     * @param taskSchedule 
     * @return 集合
     */
    List<TaskScheduleEntity> selectTaskScheduleList(TaskScheduleEntity taskSchedule);

    /**
     * 新增
     *
     * @param taskSchedule 
     * @return 结果
     */
    int insertTaskSchedule(TaskScheduleEntity taskSchedule);

    /**
     * 修改
     *
     * @param taskSchedule 
     * @return 结果
     */
    int updateTaskSchedule(TaskScheduleEntity taskSchedule);

    /**
     * 删除
     *
     * @param id 主键
     * @return 结果
     */
    int deleteTaskScheduleById(Long id);

    /**
     * 批量删除
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteTaskScheduleByIds(Long[] ids);

    /**
     * 修改
     *
     * @param taskSchedule 
     * @return 结果
     */
    int updateTaskScheduleByScheduleId(TaskScheduleEntity taskSchedule);

    /**
     * 获取定时信息
     *
     * @param taskId 脚本任务Id
     * @return {@link TaskScheduleEntity }
     */
    TaskScheduleEntity selectTaskScheduleByTaskId(Long taskId);
}
