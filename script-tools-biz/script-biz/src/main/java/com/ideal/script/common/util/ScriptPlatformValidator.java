package com.ideal.script.common.util;

import com.ideal.script.common.constant.enums.Enums;
import com.ideal.script.exception.ScriptException;

import java.util.*;

/**
 * 脚本类型与适用平台是否匹配校验
 *  <AUTHOR>
 */
public class ScriptPlatformValidator {
    private static final Map<String, List<String>> PLATFORM_MAPPING = new HashMap<>();
    private ScriptPlatformValidator() {
    }

    static {
        PLATFORM_MAPPING.put("sh", Arrays.asList(Enums.PlatForm.HP_UX.getValue(), Enums.PlatForm.UNIX.getValue(),  Enums.PlatForm.SUSE.getValue(), Enums.PlatForm.REDHAT.getValue(), Enums.PlatForm.CENTOS.getValue(),Enums.PlatForm.LINUX.getValue(),Enums.PlatForm.ORACLE_LINUX.getValue()));
        PLATFORM_MAPPING.put("perl", Arrays.asList(Enums.PlatForm.HP_UX.getValue(),  Enums.PlatForm.UNIX.getValue(),  Enums.PlatForm.SUSE.getValue(), Enums.PlatForm.REDHAT.getValue(),  Enums.PlatForm.CENTOS.getValue(), Enums.PlatForm.LINUX.getValue()));
        PLATFORM_MAPPING.put("bat", Collections.singletonList(Enums.PlatForm.WINDOWS.getValue()));
        PLATFORM_MAPPING.put("ps1", Collections.singletonList(Enums.PlatForm.WINDOWS.getValue()));
        PLATFORM_MAPPING.put("py", Arrays.asList(Enums.PlatForm.HP_UX.getValue(), Enums.PlatForm.UNIX.getValue(),  Enums.PlatForm.SUSE.getValue(), Enums.PlatForm.REDHAT.getValue(),  Enums.PlatForm.CENTOS.getValue(),  Enums.PlatForm.LINUX.getValue(),  Enums.PlatForm.WINDOWS.getValue(),Enums.PlatForm.ORACLE_LINUX.getValue()));
        PLATFORM_MAPPING.put("sql", Arrays.asList(Enums.PlatForm.HP_UX.getValue(), Enums.PlatForm.UNIX.getValue(),  Enums.PlatForm.SUSE.getValue(), Enums.PlatForm.REDHAT.getValue(),  Enums.PlatForm.CENTOS.getValue(),  Enums.PlatForm.LINUX.getValue(),  Enums.PlatForm.WINDOWS.getValue(),Enums.PlatForm.ORACLE_LINUX.getValue()));
    }

    /**
     * 校验脚本类型与适用平台是否匹配
     * @param platforms 适用平台
     * @param scriptType 脚本类型
     */
    public static void validatePlatformAndScript(List<String> platforms, String scriptType) throws ScriptException {
        if (PLATFORM_MAPPING.containsKey(scriptType)) {
            List<String> allowedPlatforms = PLATFORM_MAPPING.get(scriptType);
            // 检查是否存在平台列表
            if (!allowedPlatforms.isEmpty()) {
                for (String platform : platforms) {
                    if (!allowedPlatforms.contains(platform)) {
                        throw new ScriptException("script.type.mismatch.error");
                    }
                }
            } else {
                throw new ScriptException("unknown.script.type");
            }
        } else {
            throw new ScriptException("unknown.script.type");
        }
    }
}
