package com.ideal.script.config;

import com.ideal.sc.annotation.CustomerIdentity;
import com.ideal.sc.constants.Constants;
import com.ideal.sc.constants.CustomerConstants;
import com.ideal.sc.constants.StrPool;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * citic注册器
 *
 * <AUTHOR>
 */
@Configuration
@CustomerIdentity(CustomerConstants.CITIC)
public class CiticWebConfig implements WebMvcConfigurer {

    @Bean
    @ConfigurationProperties(prefix = Constants.CUSTOMER_PREFIX + StrPool.DOT + CustomerConstants.CITIC)
    public CiticProperties citicProperties() {
        return new CiticProperties();
    }

}