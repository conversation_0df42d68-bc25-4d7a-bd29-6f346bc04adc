package com.ideal.script.mapper;

import java.util.List;

import com.ideal.script.model.entity.VarAndFuncForEdit;
import com.ideal.script.model.entity.VariablePublish;
import org.apache.ibatis.annotations.Param;

/**
 * 变量库基础信息发布Mapper接口
 * 
 * <AUTHOR>
 */
public interface VariablePublishMapper
{
    /**
     * 查询变量库基础信息发布
     * 
     * @param id 变量库基础信息发布主键
     * @return 变量库基础信息发布
     */
     VariablePublish selectVariablePublishById(Long id);

    /**
     * 查询变量库基础信息发布列表
     * 
     * @param variableP 变量库基础信息发布
     * @return 变量库基础信息发布集合
     */
     List<VariablePublish> selectVariablePublishList(VariablePublish variableP);

    /**
     * 新增变量库基础信息发布
     * 
     * @param variableP 变量库基础信息发布
     * @return 结果
     */
     int insertVariableP(VariablePublish variableP);

    /**
     * 修改变量库基础信息发布
     * 
     * @param variableP 变量库基础信息发布
     * @return 结果
     */
     int updateVariableP(VariablePublish variableP);

    /**
     * 删除变量库基础信息发布
     * 
     * @param id 变量库基础信息发布主键
     * @return 结果
     */
     int deleteVariablePublishById(Long id);

    /**
     * 批量删除变量库基础信息发布
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
     int deleteVariablePublishByIds(@Param("ids")Long[] ids);

    /**
     * 查询
     * @param varAndFuncForEdit     函数、变量绑定关系实体
     * @return  结果
     */
    List<VariablePublish> selectVariablePublishListForEdit(VarAndFuncForEdit varAndFuncForEdit);
}
