package com.ideal.script.common.constant.enums;

/**
 * 验证提示消息枚举
 * <AUTHOR>
 */
public enum ValidationMessages {
    /**
     * 验证提示信息枚举
     */
    SCRIPT_BLOCKED_MESSAGE("脚本中存在屏蔽命令，无法保存"),
    CONTAINS_KEYWORD_MESSAGE("脚本中存在关键命令，是否继续保存"),
    PARAMETER_VALIDATION_FAILED_MESSAGE("参数验证不通过"),
    PARAMETER_NAME_EXIST_MESSAGE("参数名称不能重复");

    private final String message;

    ValidationMessages(String message) {
        this.message = message;
    }

    public String getMessage() {
        return message;
    }
}
