package com.ideal.script.mapper;

import com.ideal.script.model.bean.ScriptInfoQueryBean;
import com.ideal.script.model.bean.TaskApplyBean;
import com.ideal.script.model.bean.TaskAuthorityBean;
import com.ideal.script.model.entity.InfoVersion;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Mapper接口
 * 
 * <AUTHOR>
 */
public interface InfoVersionMapper 
{
    /**
     * 查询
     * 
     * @param id 主键
     * @return 
     */
     InfoVersion selectInfoVersionById(Long id);

    /**
     * 根据脚本版本id查询已发布的脚本信息
     *
     * @param id 脚本版本id
     * @return 脚本信息
     */
    InfoVersion selectPublishInfoVersionById(Long id);

    /**
     * 查询
     *
     * @param ids 主键
     * @return 
     */
    List<InfoVersion> selectInfoVersionByIds(@Param("ids")Long[] ids);
    /**
     * 查询列表
     * 
     * @param infoVersion 
     * @return 集合
     */
     List<InfoVersion> selectInfoVersionList(InfoVersion infoVersion);

    /**
     * 新增
     * 
     * @param infoVersion 
     * @return 结果
     */
     int insertInfoVersion(InfoVersion infoVersion);

    /**
     * 修改
     * 
     * @param infoVersion 
     * @return 结果
     */
     int updateInfoVersion(InfoVersion infoVersion);

    /**
     * 修改【批量更新版本表状态】
     * @param infoVersion   【版本信息】
     * @return  更新的行数
     */
     int batchUpdateInfoVersion(InfoVersion infoVersion);

    /**
     * 使用此方法的前提为，能够保证历史每个版本都是发布版本
     * @param infoVersion   脚本版本实体类
     * @return  结果
     */
     int updateInfoVersionBySrcUuid(InfoVersion infoVersion);

    /**
     * 根据infoUuid批量更新多版本的数据（创建者转移功能）
     * @param infoVersion 脚本版本实体类
     * @param uuids 脚本InfoUuid
     */
     void updateInfoVersionByInfoUuid(@Param("InfoVersion") InfoVersion infoVersion, @Param("uuids") List<String> uuids);
    /**
     * 将之前的版本的脚本默认属性置为0
     * @param infoVersion   脚本版本实体类
     * @return  结果
     */
    int updateInfoVersionDefaultValue(InfoVersion infoVersion);
    /**
     * 删除
     * 
     * @param id 主键
     * @return 结果
     */
     int deleteInfoVersionById(Long id);

    /**
     * 批量删除
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
     int deleteInfoVersionByIds(@Param("ids")Long[] ids);

    /**
     * 根据info表 uuid 获取最新版本信息
     * @param uuid  uuid
     * @return  结果
     */
     InfoVersion selectLastInfoVersionByInfoUuid(String uuid,Integer editState);

    /**
     * 根据infoVersion表 id 获取默认版本信息
     * @param ids   版本ids
     * @return  版本列表
     */
     List<InfoVersion> selectDefaultInfoVersionByIds(Long[] ids);

    /**
     * 统计
     * @param uniqueUuid    基础表唯一uuid
     * @return  结果
     */
     int countPublishVersion(String uniqueUuid);

    /**
     * 版本数量
     * @param id    版本id
     * @return 数量
     */
     int countVersion(Long id);

    /**
     * 任务申请列表（获取可申请的脚本任务列表) 查询当前登录人同组用户创建的脚本任务（包括自己）
     * <AUTHOR>
     * @param taskApplyBean 任务申请Bean
     * @return List<TaskApplyBean> 任务申请列表
     */
    List<TaskApplyBean> selectTaskApplyList(TaskApplyBean taskApplyBean);

    /**
     * 根据审核关系id查询脚本的分类id
     * @param relationId 审核关系数据
     * @return 脚本分类id
     */
    Long getScriptInfoByAuditRelationId(Long relationId);
    /**
     * 任务权限列表（对脚本任务的权限进行管理）
     * <AUTHOR>
     * @param taskAuthorityBean 任务权限Bean
     * @return List<TaskAuthorityBean>
     */
    List<TaskAuthorityBean> selectTaskAuthorityList(TaskAuthorityBean taskAuthorityBean);

    /**
     * 根据srcScriptUuid查询脚本任务信息
     * @param srcScriptUuid 脚本版本uuid
     * @return  结果
     */
    InfoVersion selectInfoVersionBysrcScriptUuid(String srcScriptUuid);

    /**
     * 根据srcScriptUuid查询脚本任务信息(默认版本)
     * @param srcScriptUuid 脚本版本uuid
     * @return  结果
     */
    InfoVersion selectDefaultInfoVersionBysrcScriptUuid(String srcScriptUuid);

    /**
     * 查询
     * @param srcScriptUuid 脚本版本uuid
     * @return  结果
     */
    String getScriptTypeBySrcScriptUuid(String srcScriptUuid);

    /**
     * 验证版本uuid是否存在
     *
     * @param srcScriptUuid 版本uuid
     * @return Boolean
     */
    Boolean validSrcScriptUuidExist(String srcScriptUuid);

    /**
     * 将旧版本脚本禁用
     * @param versionUuids   脚本的版本uuid集合
     * @param versionUuid   脚本的版本uuid
     * @return  结果
     */
    int disableOldVersionByUuid(List<String> versionUuids,String versionUuid);

    /**
     * 是否是白名单
     * @param id 主键
     * @return boolean
     */
    boolean isInWhiteList(Long id);

    /**
     * 查询脚本信息
     *
     * @param scriptInfoQueryBean 脚本查询信息
     * @return {@link List}<{@link InfoVersion}>
     */
    List<TaskApplyBean> getInfoVersionList(ScriptInfoQueryBean scriptInfoQueryBean);

    /**
     * 查询默认版本的脚本信息
     *
     * @param scriptInfoQueryBean 脚本信息查询Dto
     * @return {@link InfoVersion}
     */
    TaskApplyBean getInfoDefaultVersion(ScriptInfoQueryBean scriptInfoQueryBean);

    /**
     * 查询默认版本的脚本信息
     *
     * @param scriptInfoQueryBean 脚本信息查询Dto
     * @return {@link InfoVersion}
     */
    TaskApplyBean getInfoVersion(ScriptInfoQueryBean scriptInfoQueryBean);

    /**
     * 更新默认版本
     * @param lastId    版本id
     * @return  更新记录
     */
    int updateDefaultVersion(Long lastId);

    /**
     * 获取最新版本id
     * @param uuid    版本id
     * @return  最新版本id
     */
    Long getLastVersionByUuid(String uuid);

    /**
     * 根据脚本id查询uuid
     * @param id    脚本id
     * @return  脚本uuid
     */
    String getUuidById(Long id);

    /**
     * 根军一个版本uuid查询所以版本的uuid
     * @param versionUuid 版本uuid
     * @return List
     */
    List<String> getAllVersionUuidByUuid(String versionUuid);

    /**
     * 查看是否有未完成的任务
     *
     * @param scriptInfoVersionId 版本Id
     * @return {@link Integer }
     * <AUTHOR>
     */
    Integer checkExistRunTask(Long scriptInfoVersionId);

    /**
     * 根据uuid获取脚本id
     * @param versionUuid uuid
     * @return {@link List<Long> }
     * <AUTHOR>
     */
    List<Long> getScriptIdsByUuids(List<String> versionUuid);


    /**
     * 查询多个脚本的草稿脚本
     * @param uniqueUuidList info表uniqueUuid集合
     * @return  脚本列表
     */
    List<InfoVersion> selectInfoVersionListForEdit(List<String> uniqueUuidList);

    /**
     * 查询多个脚本的默认版本脚本
     * @param uniqueUuidList info表uniqueUuid集合
     * @return 脚本列表
     */
    List<InfoVersion> selectInfoVersionListForDefault(List<String> uniqueUuidList);

    /**
     * 通过版本uuid删除脚本
     * @param versionUuid  版本uuid
     */
    void deleteInfoVersionByUuid(String versionUuid);

    /**
     * 查询脚本未被禁用，未被删除的最新脚本信息
     * @param uniqueUuid    info表uniqueUuid
     * @return  脚本信息
     */
    InfoVersion selectLastEnableInfoVersion(String uniqueUuid);

    /**
     *根据脚本版本uuid查询默认版本信息
     * @param srcSrcScriptUuid  脚本uuid
     * @return  默认版本信息
     */
    InfoVersion selectDefaultInfoVersionByUuid(String srcSrcScriptUuid);

    /**
     * 查询该脚本已发布的版本数量
     * @param uniqueUuid    主表唯一uuid
     * @return  数量
     */
    int getCountVersionForPublish(String uniqueUuid);

    /**
     * 获取当前ids对应脚本中最大版本的
     * @param ids 版本id
     * @return  版本信息
     */
    InfoVersion getLastInfoVersionByIds(String uniqueUuid,Long[] ids);

    InfoVersion getInfoDefaultVersionByUniqueUuid(String uniqueUuid);

    Integer getTaskCountByVersionUuid(String versionUuid);

    /**
     * 根据infoUniqueUuid查询最高版本的数据
     * @param infoUniqueUuid 脚本uuid
     * @return  版本信息
     */
    List<InfoVersion> selectMaxInfoVersionByInfoUniqueUuid(String infoUniqueUuid);

    /**
     * 根据uniqueUuid获取此脚本的所有版本的版本号
     * @param uniqueUuid uuid
     * @return 版本号集合
     */
    List<String> getAllVersionByUniqueUuid(String uniqueUuid);

    /**
     * @param srcScriptUuid 版本uuid
     * @return 版本id
     */
    Long selectIdBySrcScriptUuid(@Param("srcScriptUuid")String srcScriptUuid);
}
