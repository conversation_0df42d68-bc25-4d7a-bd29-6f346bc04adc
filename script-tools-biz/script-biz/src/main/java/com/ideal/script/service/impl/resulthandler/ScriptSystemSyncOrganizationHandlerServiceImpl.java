package com.ideal.script.service.impl.resulthandler;

import com.ideal.script.service.ICategoryService;
import com.ideal.script.service.IMyScriptService;
import com.ideal.script.service.resulthandler.IScriptSystemSyncOrganizationHandlerService;
import com.ideal.system.dto.OrgManagementApiDto;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

/**
 * 基础数据组织机构变更处理脚本信息orgCode
 *
 * <AUTHOR>
 **/
@Service
public class ScriptSystemSyncOrganizationHandlerServiceImpl implements IScriptSystemSyncOrganizationHandlerService {
    private final Logger logger = LoggerFactory.getLogger(ScriptSystemSyncOrganizationHandlerServiceImpl.class);
    private final IMyScriptService myScriptService;
    private final ICategoryService categoryService;

    public ScriptSystemSyncOrganizationHandlerServiceImpl(IMyScriptService myScriptService, ICategoryService categoryService) {
        this.myScriptService = myScriptService;
        this.categoryService = categoryService;
    }
    /**
     * 更新处理脚本基础信息表、脚本分类组织机构关系表
     * @param orgManagementApiDto 组织机构dto
     */
    @Override
    public void handler(OrgManagementApiDto orgManagementApiDto) {
        String originalCode = orgManagementApiDto.getOriginalCode();
        String code = orgManagementApiDto.getCode();
        if(StringUtils.isNotBlank(originalCode) && StringUtils.isNotBlank(code) && !originalCode.equals(code)){
            int i = myScriptService.updateScriptInfoByOrgCode(originalCode, code);
            int i2 = categoryService.updateOrgCodeByOrgCode(originalCode, code);
            logger.info("orgManagementApiDto OriginalCode:{},orgManagementApiDto new code:{},update ieai_script_info num is : {},update ieai_script_cat_org_relation num is : {}", originalCode, code, i, i2);
        }else{
            logger.warn("orgManagementApiDto OriginalCode:{},orgManagementApiDto new code:{}", originalCode, code);
        }
    }
}
