package com.ideal.script.common.constant.enums;

/**
 * agent执行所需相关key
 * <AUTHOR>
 */
public class AgentExecuteKey {
    /**
     * AgentHashtableKey agent执行所需相关key
     */
    public enum AgentHashtableKey {

        /**
         * 命令
         */
        COMMAND("command"),
        SCRIPT_TIMEOUT("scriptItimeout"),
        SERVICE_NAME("serviceName"),
        SERVICES_TYPE("servicesType"),
        SCRIPT_WORK_DIR("scriptWorkDir"),
        ANNEX_FILE("annexFiles"),
        SCOPE_ID("_scopeId"),
        ADAPTOR_CONFIG("adaptorConfig"),
        SERVER_HOST("serverHost"),
        SERVER_PORT("serverPort"),
        ACT_STATE_DATA_VERSION("_actStateDataVersion"),
        TIME_OUT("timeout"),
        START_TIME("startTime"),
        AGENT_HOST("agentHost"),
        AGENT_PORT("agentPort"),
        ADAPTOR_DEF_UUID("adaptorDefUUID"),
        ACT_ID("actId"),
        ADAPTOR_DEF_NAME("adaptorDefName"),
        ACT_NAME("actName"),
        PROJECT_NAME("projectName"),
        FLOW_NAME("flowName"),
        STATUS("status"),
        LEVEL_OF_WEIGHT("levelOfWeight"),
        ID("Id"),
        FLOW_ID("flowId"),
        FLOW_POOL_NUM("flowPoolNum"),
        IS_SAFE_FIRST("isSafeFirst"),
        LEVEL_OF_PRI("levelOfPRI"),
        EXPECT_TYPE("expecttype"),
        INPUT("input");

        private final String value;

        AgentHashtableKey(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

    }

    /**
     * AgentScriptHashtableKey agent执行所需相关key
     */
    public enum AgentScriptHashtableKey{

        /**
         * 类型
         */
        SERVICES_TYPE("servicesType"),
        COMMAND("command"),
        PM("pm"),
        EXECUTE_USER_NAME("executeUsername"),
        EXPECT_TYPE("expecttype"),
        IS_SHUT_DOWN("isShutdown"),
        IS_TEST("isTest"),
        SCRIPT_INSTANCE_ID("scriptInstanceId"),
        ENV_PARAM("envparam"),
        BAT_SU_USER_PASS_WORD("batSuUserPasswd"),
        SCRIPT_INSTANCE_BEAN_ID("script.agent.consumer.scriptInstanceBeanId"),
        SCRIPT_ADAPTOR_BEAN_ID("script.agent.consumer.scriptAdaptorBeanId");


        private final String value;

        AgentScriptHashtableKey(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }
    }

    /**
     * DesUtilsKey
     * <AUTHOR>
     */
    public enum DesUtilsKey{
        /**
         * SCRIPT_SERVICE_LEEMENZ
         */
        SCRIPT_SERVICE_LEEMENZ("scriptService_leemenz");
        private final String value;

        DesUtilsKey(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }
    }
}
