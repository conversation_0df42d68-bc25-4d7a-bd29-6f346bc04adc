package com.ideal.script.mapper;

import com.ideal.script.model.bean.StatementBean;

import java.util.List;

public interface StatementMapper {

    List<StatementBean> selectScriptStatementPage(StatementBean statementBean);

    List<StatementBean> selectScriptStatementByIds(List<Long> ids);

    int updateScriptStatementByIds(StatementBean statementBean,List<Long> ids);


    StatementBean getScriptStatementByInfoId(Long infoId);
}
