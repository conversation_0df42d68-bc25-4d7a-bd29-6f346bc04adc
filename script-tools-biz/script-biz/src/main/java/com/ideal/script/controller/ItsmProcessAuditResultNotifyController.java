package com.ideal.script.controller;

import com.github.pagehelper.PageInfo;
import com.ideal.common.dto.R;
import com.ideal.common.dto.TableQueryDto;
import com.ideal.script.common.constant.Constants;
import com.ideal.script.common.validation.Create;
import com.ideal.script.dto.ItsmGetScriptAgentQueryDto;
import com.ideal.script.dto.ScriptInfoQueryDto;
import com.ideal.script.exception.ScriptException;
import com.ideal.script.model.bean.MyScriptItsmBean;
import com.ideal.script.model.bean.MyScriptItsmContentBean;
import com.ideal.script.model.dto.ItsmAgentInfoDto;
import com.ideal.script.model.dto.cibitsm.ItsmProcessResultNotifyDto;
import com.ideal.script.model.dto.cibitsm.ItsmScriptRequestDto;
import com.ideal.script.model.dto.cibitsm.ResponseResultDto;
import com.ideal.script.service.AuditSource;
import com.ideal.script.service.IMyScriptService;
import com.ideal.script.service.ITaskApplyService;
import com.ideal.script.service.ITaskGroupsService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * itsm相关接口controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("${app.script-tools-url:}/itsmProcessAuditResultNotify")
public class ItsmProcessAuditResultNotifyController {
    private static final Logger logger = LoggerFactory.getLogger(ItsmProcessAuditResultNotifyController.class);

    private final ITaskApplyService taskApplyService;
    private final AuditSource taskApplySource;
    private final IMyScriptService iMyScriptService;
    private final ITaskGroupsService taskGroupsService;

    public ItsmProcessAuditResultNotifyController(ITaskApplyService taskApplyService, @Qualifier("taskApplySource") AuditSource taskApplySource, IMyScriptService iMyScriptService, ITaskGroupsService taskGroupsService) {
        this.taskApplyService = taskApplyService;
        this.taskApplySource = taskApplySource;
        this.iMyScriptService = iMyScriptService;
        this.taskGroupsService = taskGroupsService;
    }

    /**
     * itsm审核流程结束调用脚本服务化任务申请
     * @param itsmProcessResultNotifyDto 参数对象
     * @return 创建任务结果
     * @throws ScriptException 脚本服务化异常
     */
    @PostMapping("/taskApply")
    public ResponseResultDto itsmProcessTaskApply(@RequestBody @Validated(Create.class) ItsmProcessResultNotifyDto itsmProcessResultNotifyDto)  throws ScriptException{
        taskApplyService.itsmProcessResult(itsmProcessResultNotifyDto,taskApplySource);
        ResponseResultDto responseResultDto = new ResponseResultDto();
        responseResultDto.setStatus(true);
        String returnMessage = "自动化平台已经收到itsm发起的任务申请，单号为：" + itsmProcessResultNotifyDto.getCode();
        responseResultDto.setMessage(returnMessage);
        responseResultDto.setTitle(returnMessage);
        responseResultDto.setErrorCode("");
        return responseResultDto;
    }

    /**
     * itsm审核流程结束调用脚本服务化脚本批量禁用
     * @param itsmProcessResultNotifyDto 参数对象
     * @return 创建任务结果
     * @throws ScriptException 脚本服务化异常
     */
    @PostMapping("/batchOfflineScripts")
    public ResponseResultDto batchOfflineScripts(@RequestBody @Validated(Create.class) ItsmProcessResultNotifyDto itsmProcessResultNotifyDto)  throws ScriptException{
        return taskApplyService.itsmDisabledScript(itsmProcessResultNotifyDto);
    }


    /**
     * itsm查询脚本列表
     * @param itsmScriptRequestDto 入参
     * @return 脚本列表
     */
    @PostMapping("/getMyScriptPage")
    public R<PageInfo<MyScriptItsmBean>> getMyScriptDefaultPage(@RequestBody ItsmScriptRequestDto itsmScriptRequestDto) {
        ScriptInfoQueryDto scriptInfoQueryDto = new ScriptInfoQueryDto();
        scriptInfoQueryDto.setPageSize(itsmScriptRequestDto.getPageSize());
        scriptInfoQueryDto.setPageNum(itsmScriptRequestDto.getPageNum());
        return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, iMyScriptService.getMyScriptDefaultPage(scriptInfoQueryDto), "查询成功");
    }

    /**
     * itsm查询脚本详情
     * @param itsmScriptRequestDto 入参
     * @return 脚本详情
     */
    @PostMapping("/getMyScriptDetail")
    public R<MyScriptItsmBean> getMyScriptDetail(@RequestBody ItsmScriptRequestDto itsmScriptRequestDto) {
        return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, iMyScriptService.getMyScriptDetail(itsmScriptRequestDto.getAutoCode(),false), "查询成功");
    }

    /**
     * itsm查询脚本详情、内容
     * @param itsmScriptRequestDto 入参
     * @return 脚本详情、内容
     */
    @PostMapping("/getMyScriptDetailContent")
    public R<MyScriptItsmContentBean> getMyScriptDetailContent(@RequestBody ItsmScriptRequestDto itsmScriptRequestDto) {
        return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, iMyScriptService.getMyScriptDetailContent(itsmScriptRequestDto.getAutoCode(),true), "查询成功");
    }

    /**
     * itsm获取设备列表
     * @param tableQueryDTO 查询条件
     * @return page列表数据
     */
    @PostMapping("/computerList")
    public R<PageInfo<ItsmAgentInfoDto>> computerList(@RequestBody TableQueryDto<ItsmGetScriptAgentQueryDto> tableQueryDTO) {
        return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE,taskGroupsService.queryAgentInfoItsmGroupRole(tableQueryDTO),"查询成功");
    }

}
