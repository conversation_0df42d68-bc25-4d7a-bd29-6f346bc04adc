package com.ideal.script.mapper;

import com.ideal.script.model.entity.InfoVersionText;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Mapper接口
 * 
 * <AUTHOR>
 */
public interface InfoVersionTextMapper 
{
    /**
     * 查询
     * 
     * @param id 主键
     * @return 
     */
     InfoVersionText selectInfoVersionTextById(Long id);

    /**
     * 查询列表
     * 
     * @param infoVersionText 
     * @return 集合
     */
     List<InfoVersionText> selectInfoVersionTextList(InfoVersionText infoVersionText);

    /**
     * 新增
     * 
     * @param infoVersionText 
     * @return 结果
     */
     int insertInfoVersionText(InfoVersionText infoVersionText);

    /**
     * 修改
     * 
     * @param infoVersionText 
     * @return 结果
     */
     int updateInfoVersionText(InfoVersionText infoVersionText);

    /**
     * 更新
     * @param infoVersionText   脚本内容实体
     * @return  结果
     */
     int updateInfoVersionTextByScriptUuid(InfoVersionText infoVersionText);

    /**
     * 删除
     * 
     * @param id 主键
     * @return 结果
     */
     int deleteInfoVersionTextById(Long id);

    /**
     * 批量删除
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
     int deleteInfoVersionTextByIds(@Param("ids")Long[] ids);

    /**
     * 删除
     * @param scriptUuids   脚本uuid
     * @return  结果
     */
     int deleteInfoVersionTextByScriptUuids(String[] scriptUuids);

	/**
	 * 根据脚本id获取脚本内容
	 * @param scriptId  脚本id
	 * @return  结果
	 */
	 InfoVersionText selectInfoVersionTextByScriptId(Long scriptId);

    /**
     * 查询
     * @param srcScriptUuid 脚本版本uuid
     * @return  结果
     */
    InfoVersionText selectInfoVersionTextByScriptUuid(@Param("srcScriptUuid") String srcScriptUuid);


}
