package com.ideal.script.mapper;

import java.util.List;
import com.ideal.script.model.entity.DangerCmd;
import org.apache.ibatis.annotations.Param;

/**
 * 关键命令(高危命令)Mapper接口
 * 
 * <AUTHOR>
 */
public interface DangerCmdMapper 
{
    /**
     * 查询关键命令(高危命令)
     * 
     * @param id 关键命令(高危命令)主键
     * @return 关键命令(高危命令)
     */
     DangerCmd selectDangerCmdById(Long id);

    /**
     * 查询关键命令(高危命令)列表
     * 
     * @param dangerCmd 关键命令(高危命令)
     * @return 关键命令(高危命令)集合
     */
     List<DangerCmd> selectDangerCmdList(DangerCmd dangerCmd);

    /**
     * 查询关键命令(高危命令)列表(通过分类id)
     *
     * @param categoryId 分类id
     * @return 关键命令(高危命令)集合
     */
    List<DangerCmd> selectDangerCmdByCategoryIdList(Long categoryId);

    /**
     * 新增关键命令(高危命令)
     * 
     * @param dangerCmd 关键命令(高危命令)
     * @return 结果
     */
     int insertDangerCmd(DangerCmd dangerCmd);

    /**
     * 修改关键命令(高危命令)
     * 
     * @param dangerCmd 关键命令(高危命令)
     * @return 结果
     */
     int updateDangerCmd(DangerCmd dangerCmd);

    /**
     * 删除关键命令(高危命令)
     * 
     * @param id 关键命令(高危命令)主键
     * @return 结果
     */
     int deleteDangerCmdById(Long id);

    /**
     * 批量删除关键命令(高危命令)
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
     int deleteDangerCmdByIds(@Param("ids")Long[] ids);

    List<DangerCmd> selectDangerCmdsByLabels(List<String> deletedLabels);
}
