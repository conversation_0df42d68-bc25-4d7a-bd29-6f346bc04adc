package com.ideal.script.mapper;

import java.util.List;
import com.ideal.script.model.entity.ToProductRelationEntity;
import org.apache.ibatis.annotations.Param;

/**
 * 投产记录关系Mapper接口
 *
 * <AUTHOR>
 * @date 2024-04-02
 */
public interface ToProductRelationMapper {
    /**
     * 查询投产记录关系
     *
     * @param id 投产记录关系主键
     * @return 投产记录关系
     */
    ToProductRelationEntity selectToProductRelationById(Long id);

    /**
     * 查询投产记录关系列表
     *
     * @param toProductRelation 投产记录关系
     * @return 投产记录关系集合
     */
    List<ToProductRelationEntity> selectToProductRelationList(ToProductRelationEntity toProductRelation);

    /**
     * 新增投产记录关系
     *
     * @param toProductRelation 投产记录关系
     * @return 结果
     */
    int insertToProductRelation(ToProductRelationEntity toProductRelation);

    /**
     * 修改投产记录关系
     *
     * @param toProductRelation 投产记录关系
     * @return 结果
     */
    int updateToProductRelation(ToProductRelationEntity toProductRelation);

    /**
     * 删除投产记录关系
     *
     * @param id 投产记录关系主键
     * @return 结果
     */
    int deleteToProductRelationById(Long id);

    /**
     * 批量删除投产记录关系
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteToProductRelationByIds(@Param("ids")Long[] ids);
}
