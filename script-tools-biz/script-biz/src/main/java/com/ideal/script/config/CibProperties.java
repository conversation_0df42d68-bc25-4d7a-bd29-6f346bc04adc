package com.ideal.script.config;

/**
 * cib个性化配置
 *
 * <AUTHOR>
 **/
public class CibProperties {

    /**
     * 上传附件url
     */
    private String uploadScriptAttachmentUrl;

    /**
     * 任务申请结束后推送itsm结果url配置
     */
    private String itsmExecAuditingResultUrl;

    /**
     * 获取itsm平台token接口用户名配置
     */
    private String itsmExecAuditingTokenUserName;

    /**
     * 获取itsm平台token接口密码配置
     */
    private String itsmExecAuditingTokenPassword;

    /**
     * 获取itsm平台token接口url配置
     */
    private String itsmExecAuditingTokenUrl;

    /**
     * cib脚本上传itsm制品库url地址
     */
    private String scriptUploadToItsmProductLibraryUrl;

    /**
     * 调用itsm接口token，保存到本地redis超时时间，默认60分钟
     */
    private Integer itsmTokenTimeout = 60;

    /**
     * 投产介质上传制品库名称
     */
    private String repositoryName;

    /**
     * 研发侧：制品库基础URL：http://xx.xx.xx.xx/artifactory/
     * 用途：制品文件上传时使用此仓库url
     */
    private String devBaseUrl;

    /**
     * 生产侧：制品库基础URL：http://xx.xx.xx.xx/artifactory/
     * 用途：制品文件下载，使用此仓库url
     */
    private String prodBaseUrl;

    /**
     * 文件下载认证配置
     */
    private ItsmDownloadAuthConfig auth = new ItsmDownloadAuthConfig();

    public ItsmDownloadAuthConfig getAuth() {
        return auth;
    }

    public void setAuth(ItsmDownloadAuthConfig auth) {
        this.auth = auth;
    }

    public String getDevBaseUrl() {
        return devBaseUrl;
    }

    public void setDevBaseUrl(String devBaseUrl) {
        this.devBaseUrl = devBaseUrl;
    }

    public String getProdBaseUrl() {
        return prodBaseUrl;
    }

    public void setProdBaseUrl(String prodBaseUrl) {
        this.prodBaseUrl = prodBaseUrl;
    }

    public String getRepositoryName() {
        return repositoryName;
    }

    public void setRepositoryName(String repositoryName) {
        this.repositoryName = repositoryName;
    }

    public String getUploadScriptAttachmentUrl() {
        return uploadScriptAttachmentUrl;
    }

    public void setUploadScriptAttachmentUrl(String uploadScriptAttachmentUrl) {
        this.uploadScriptAttachmentUrl = uploadScriptAttachmentUrl;
    }

    public String getItsmExecAuditingResultUrl() {
        return itsmExecAuditingResultUrl;
    }

    public void setItsmExecAuditingResultUrl(String itsmExecAuditingResultUrl) {
        this.itsmExecAuditingResultUrl = itsmExecAuditingResultUrl;
    }

    public String getItsmExecAuditingTokenUserName() {
        return itsmExecAuditingTokenUserName;
    }

    public void setItsmExecAuditingTokenUserName(String itsmExecAuditingTokenUserName) {
        this.itsmExecAuditingTokenUserName = itsmExecAuditingTokenUserName;
    }

    public String getItsmExecAuditingTokenPassword() {
        return itsmExecAuditingTokenPassword;
    }

    public void setItsmExecAuditingTokenPassword(String itsmExecAuditingTokenPassword) {
        this.itsmExecAuditingTokenPassword = itsmExecAuditingTokenPassword;
    }

    public String getItsmExecAuditingTokenUrl() {
        return itsmExecAuditingTokenUrl;
    }

    public void setItsmExecAuditingTokenUrl(String itsmExecAuditingTokenUrl) {
        this.itsmExecAuditingTokenUrl = itsmExecAuditingTokenUrl;
    }

    public String getScriptUploadToItsmProductLibraryUrl() {
        return scriptUploadToItsmProductLibraryUrl;
    }

    public void setScriptUploadToItsmProductLibraryUrl(String scriptUploadToItsmProductLibraryUrl) {
        this.scriptUploadToItsmProductLibraryUrl = scriptUploadToItsmProductLibraryUrl;
    }

    public Integer getItsmTokenTimeout() {
        return itsmTokenTimeout;
    }

    public void setItsmTokenTimeout(Integer itsmTokenTimeout) {
        this.itsmTokenTimeout = itsmTokenTimeout;
    }
}
