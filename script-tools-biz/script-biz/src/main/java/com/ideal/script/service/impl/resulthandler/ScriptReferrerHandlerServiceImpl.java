package com.ideal.script.service.impl.resulthandler;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ideal.common.util.BeanUtils;
import com.ideal.common.util.batch.BatchHandler;
import com.ideal.script.common.constant.enums.Enums;
import com.ideal.script.exception.ScriptException;
import com.ideal.script.model.dto.ScriptReferrerInfoDto;
import com.ideal.script.model.entity.ScriptReferrerInfo;
import com.ideal.script.service.IScriptReferrerInfoService;
import com.ideal.script.service.resulthandler.IScriptReferrerInfoHandlerService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.function.Consumer;

import com.fasterxml.jackson.core.type.TypeReference;
/**
 * 其它模块引用脚本mq信息处理类
 *
 * <AUTHOR>
 */
@Service
public class ScriptReferrerHandlerServiceImpl implements IScriptReferrerInfoHandlerService {
    private final Logger logger = LoggerFactory.getLogger(ScriptReferrerHandlerServiceImpl.class);

    private final IScriptReferrerInfoService scriptReferrerInfoService;
    private final BatchHandler batchHandler;

    public ScriptReferrerHandlerServiceImpl(BatchHandler batchHandler,IScriptReferrerInfoService scriptReferrerInfoService) {
        this.batchHandler = batchHandler;
        this.scriptReferrerInfoService = scriptReferrerInfoService;
    }


    /**
     * 其它模块引用脚本服务化脚本情况写入、删除
     *
     * @param message 其它模块引用脚本服务化脚本情况写入、删除json格式字符串
     */
    @Override
    @Transactional(rollbackFor = ScriptException.class)
    @SuppressWarnings({"java:S2222"})
    public void scriptReferrerInfoHandler(String message) throws ScriptException {
        //将mq的数据转成对象
        logger.info("get script referrer info:{}", message);
        try {
            ObjectMapper mapper = new ObjectMapper();
            //转换字符出串为list对象
            List<ScriptReferrerInfoDto> list = mapper.readValue(message, new TypeReference<List<ScriptReferrerInfoDto>>() {});
            if(Objects.nonNull(list) && !list.isEmpty()) {
                List<ScriptReferrerInfo> insertList = new ArrayList<>();
                List<ScriptReferrerInfo> deleteList = new ArrayList<>();
                for(ScriptReferrerInfoDto scriptReferrerInfoDto : list) {
                    //获取新增数据
                    if(Enums.ScriptReferrerType.SAVE.getValue().equals(scriptReferrerInfoDto.getBizType())){
                        String [] scriptSrcUuidArray = scriptReferrerInfoDto.getScriptSrcUuid();
                        //循环整合数据
                        for(String srcUuid : scriptSrcUuidArray) {
                            ScriptReferrerInfo scriptReferrerInfo = BeanUtils.copy(scriptReferrerInfoDto, ScriptReferrerInfo.class);
                            scriptReferrerInfo.setScriptSrcUuidSingle(srcUuid);
                            insertList.add(scriptReferrerInfo);
                        }
                    }
                    //获取删除数据
                    if(Enums.ScriptReferrerType.DELETE.getValue().equals(scriptReferrerInfoDto.getBizType())){
                        deleteList.add(BeanUtils.copy(scriptReferrerInfoDto, ScriptReferrerInfo.class));
                    }
                }

                //删除操作
                if(!deleteList.isEmpty()){
                    Consumer<ScriptReferrerInfo> consumer = scriptReferrerInfoService::deleteByReferrerBizId;
                    batchHandler.batchData(deleteList, consumer);
                }

                //批量新增数据
                if (!insertList.isEmpty()) {
                    //先批量删除，然后再新增
                    //删除
                    Consumer<ScriptReferrerInfo> consumerDel = scriptReferrerInfoService::deleteByReferrerBizId;
                    batchHandler.batchData(insertList, consumerDel);
                    //新增
                    Consumer<ScriptReferrerInfo> consumer = scriptReferrerInfoService::insertReferrerInfo;
                    batchHandler.batchData(insertList, consumer);
                }
            }
        }catch (Exception e) {
            logger.error("get script referrer info error", e);
            throw new ScriptException("script.monitor.referrer.mq.analysis.error",e);
        }
    }

}
