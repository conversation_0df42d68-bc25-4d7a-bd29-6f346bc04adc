package com.ideal.script.common.constant.permission;

/**
 * 定时周期维护菜单按钮权限常量类
 *
 * <AUTHOR>
 */
public class TimeTaskManagerBtnPermitConstant {

    // 权限表达式前缀和连接符
    private static final String PER_PREFIX = "@dp.hasBtnPermission('";
    private static final String PER_SUFFIX = "')";

    /**
     * 停止按钮权限码
     */
    public static final String SCRIPT_TIMED_TASK_STOP = "scriptTimedTaskStop";

    /**
     * 终止按钮权限码
     */
    public static final String SCRIPT_TIMED_TASK_OVER = "scriptTimedTaskOver";

    /**
     * 启动按钮权限码
     */
    public static final String SCRIPT_TIMED_TASK_START = "scriptTimedTaskStart";

    /**
     * 停止按钮权限表达式
     */
    public static final String SCRIPT_TIMED_TASK_STOP_PER = PER_PREFIX + SCRIPT_TIMED_TASK_STOP + PER_SUFFIX;

    /**
     * 终止按钮权限表达式
     */
    public static final String SCRIPT_TIMED_TASK_OVER_PER = PER_PREFIX + SCRIPT_TIMED_TASK_OVER + PER_SUFFIX;

    /**
     * 启动按钮权限表达式
     */
    public static final String SCRIPT_TIMED_TASK_START_PER = PER_PREFIX + SCRIPT_TIMED_TASK_START + PER_SUFFIX;

    /**
     * 定时任务维护-停止 定时任务维护-启动 权限表达式
     */
    public static final String SCRIPT_TIMED_TASK_START_OR_STOP_PER = SCRIPT_TIMED_TASK_START_PER + MenuPermitConstant.OR + SCRIPT_TIMED_TASK_STOP_PER;
}
