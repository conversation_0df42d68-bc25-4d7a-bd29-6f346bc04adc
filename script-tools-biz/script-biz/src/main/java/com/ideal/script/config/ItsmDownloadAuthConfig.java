package com.ideal.script.config;

public class ItsmDownloadAuthConfig {
    /**
     * 认证类型：BASIC, BEARER
     */
    private String type = "BEARER";

    /**
     * 用户名（Basic认证）
     */
    private String username;

    /**
     * 密码或API Key（Basic认证）
     */
    private String password;

    /**
     * Bearer <PERSON>（推荐）
     */
    private String token;

    // Getters and Setters
    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }
}
