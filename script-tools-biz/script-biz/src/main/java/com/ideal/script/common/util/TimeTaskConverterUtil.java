package com.ideal.script.common.util;

import cn.idev.excel.converters.Converter;
import cn.idev.excel.metadata.GlobalConfiguration;
import cn.idev.excel.metadata.data.ReadCellData;
import cn.idev.excel.metadata.data.WriteCellData;
import cn.idev.excel.metadata.property.ExcelContentProperty;

import java.util.Collections;
import java.util.EnumMap;
import java.util.HashMap;
import java.util.Map;

public class TimeTaskConverterUtil  implements Converter<Integer> {


    public enum ConversionType {
        /**
         * 导入导出字段枚举
         */
        READYTOEXECUTE,
        LEVEL,
        TASKSCHEDULER;


        private static final Map<TimeTaskConverterUtil.ConversionType, Map<Integer, String>> MAPPINGS = new EnumMap<>(TimeTaskConverterUtil.ConversionType.class);


        static {
            //状态
            Map<Integer, String> readyToExecuteMap = new HashMap<>();
            readyToExecuteMap.put(1, "已启动");
            readyToExecuteMap.put(0,"已停止");

            //风险级别
            Map<Integer,String> levelMap = new HashMap<>();
            levelMap.put(0,"白名单");
            levelMap.put(1,"高级风险");
            levelMap.put(2,"中级风险");
            levelMap.put(3,"低级风险");

            Map<Integer,String> taskSchedulerMap = new HashMap<>();
            taskSchedulerMap.put(0,"触发执行");
            taskSchedulerMap.put(1,"周期执行");
            taskSchedulerMap.put(2,"定时执行");






            MAPPINGS.put(READYTOEXECUTE, readyToExecuteMap);
            MAPPINGS.put(LEVEL,levelMap);
            MAPPINGS.put(TASKSCHEDULER,taskSchedulerMap);
        }

        public Map<Integer, String> getMapping() {
            return Collections.unmodifiableMap(MAPPINGS.get(this));
        }
    }




    private final Map<Integer, String> intToString;
    private final Map<String, Integer> stringToInt;

    public TimeTaskConverterUtil(TimeTaskConverterUtil.ConversionType type) {
        this.intToString = type.getMapping();
        this.stringToInt = new HashMap<>();
        for (Map.Entry<Integer, String> entry : intToString.entrySet()) {
            this.stringToInt.put(entry.getValue(), entry.getKey());
        }
    }

    @Override
    public Class<Integer> supportJavaTypeKey() {
        return Integer.class;
    }

    @Override
    public WriteCellData<Integer> convertToExcelData(Integer value,
                                                     ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) {
        return new WriteCellData<>(intToString.getOrDefault(value, "未知任务"));
    }

    @Override
    public Integer convertToJavaData(ReadCellData<?> cellData,
                                     ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) {
        String stringValue = cellData.getStringValue();
        return stringToInt.get(stringValue);
    }

    /**
     * 静态内部类作为转换器
     */
    public static class ReadyToExecuteConverter extends TimeTaskConverterUtil {
        public ReadyToExecuteConverter() {
            super(ConversionType.READYTOEXECUTE);
        }
    }

    public static class LevelConverter extends TimeTaskConverterUtil {
        public LevelConverter() {
            super(ConversionType.LEVEL);
        }
    }

    public static class TaskSchedulerConverter extends TimeTaskConverterUtil {
        public TaskSchedulerConverter() {
            super(ConversionType.TASKSCHEDULER);
        }
    }
}
