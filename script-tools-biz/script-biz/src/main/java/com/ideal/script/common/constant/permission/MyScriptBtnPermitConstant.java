package com.ideal.script.common.constant.permission;

/**
 * 我的脚本菜单按钮权限常量类
 *
 * <AUTHOR>
 */
public class MyScriptBtnPermitConstant {

    // 权限表达式前缀和连接符
    private static final String PER_PREFIX = "@dp.hasBtnPermission('";
    private static final String PER_SUFFIX = "')";

    /**
     * 删除按钮权限码
     */
    public static final String REMOVE_SCRIPT = "removeScript";

    /**
     * 共享按钮权限码
     */
    public static final String SCRIPT_SHARE = "scriptShare";

    /**
     * 不修订按钮权限码
     */
    public static final String CONFIRM_SCRIPT = "confirmScript";

    /**
     * 测试按钮权限码
     */
    public static final String SCRIPT_TEST_EXECUTION = "scriptTestExecution";

    /**
     * 创建者转移按钮权限码
     */
    public static final String CREATOR_TRANSFER = "creatorTransfer";

    /**
     * 投产介质按钮权限码
     */
    public static final String EXPORT_RELEASE_MEDIA = "exportReleaseMedia";

    /**
     * 启用禁用按钮权限码
     */
    public static final String UPDATE_USE_STATE = "updateUseState";

    /**
     * 下发按钮权限码
     */
    public static final String SEND_SCRIPT_TO_AGENTS = "sendScriptToAgents";

    /**
     * 发布按钮权限码
     */
    public static final String PUBLISH_SCRIPT = "publishScript";

    /**
     * 编辑按钮权限码
     */
    public static final String UPDATE_SCRIPT = "updateScript";

    /**
     * 查看版本-版本回退按钮权限码
     */
    public static final String VERSION_ROLL_BACK = "versionRollBack";

    /**
     * 下载脚本按钮权限码
     */
    public static final String SCRIPT_DOWNLOAD = "scriptDownload";

    /**
     * 新增按钮权限码
     */
    public static final String SAVE_SCRIPT = "saveScript";

    /**
     * 我的脚本上传制品库按钮权限码
     */
    public static final String UPLOAD_PRODUCT_LIBRARY = "uploadProductLibrary";

    /**
     * 删除按钮权限表达式
     */
    public static final String REMOVE_SCRIPT_PER = PER_PREFIX + REMOVE_SCRIPT + PER_SUFFIX;

    /**
     * 共享按钮权限表达式
     */
    public static final String SCRIPT_SHARE_PER = PER_PREFIX + SCRIPT_SHARE + PER_SUFFIX;

    /**
     * 不修订按钮权限表达式
     */
    public static final String CONFIRM_SCRIPT_PER = PER_PREFIX + CONFIRM_SCRIPT + PER_SUFFIX;

    /**
     * 测试按钮权限表达式
     */
    public static final String SCRIPT_TEST_EXECUTION_PER = PER_PREFIX + SCRIPT_TEST_EXECUTION + PER_SUFFIX;

    /**
     * 创建者转移按钮权限表达式
     */
    public static final String CREATOR_TRANSFER_PER = PER_PREFIX + CREATOR_TRANSFER + PER_SUFFIX;

    /**
     * 投产介质按钮权限表达式
     */
    public static final String EXPORT_RELEASE_MEDIA_PER = PER_PREFIX + EXPORT_RELEASE_MEDIA + PER_SUFFIX;

    /**
     * 启用禁用按钮权限表达式
     */
    public static final String UPDATE_USE_STATE_PER = PER_PREFIX + UPDATE_USE_STATE + PER_SUFFIX;

    /**
     * 下发按钮权限表达式
     */
    public static final String SEND_SCRIPT_TO_AGENTS_PER = PER_PREFIX + SEND_SCRIPT_TO_AGENTS + PER_SUFFIX;

    /**
     * 发布按钮权限表达式
     */
    public static final String PUBLISH_SCRIPT_PER = PER_PREFIX + PUBLISH_SCRIPT + PER_SUFFIX;

    /**
     * 编辑按钮权限表达式
     */
    public static final String UPDATE_SCRIPT_PER = PER_PREFIX + UPDATE_SCRIPT + PER_SUFFIX;

    /**
     * 查看版本-版本回退按钮权限表达式
     */
    public static final String VERSION_ROLL_BACK_PER = PER_PREFIX + VERSION_ROLL_BACK + PER_SUFFIX;

    /**
     * 任务申请、我的脚本 下载脚本按钮权限表达式
     */
    public static final String SCRIPT_DOWNLOAD_PER = PER_PREFIX + SCRIPT_DOWNLOAD + PER_SUFFIX;

    /**
     * 新增按钮权限表达式
     */
    public static final String SAVE_SCRIPT_PER = PER_PREFIX + SAVE_SCRIPT + PER_SUFFIX;

    /**
     * 我的脚本、任务申请附件上传按钮权限表达式，有保存脚本、任务申请权限即可
     */
    public static final String SCRIPT_UPLOAD_ATTACHMENT_PER = PER_PREFIX + SAVE_SCRIPT + PER_SUFFIX + MenuPermitConstant.OR + PER_PREFIX + TaskApplyBtnPermitConstant.SCRIPT_EXEC_AUDITING + PER_SUFFIX;

    /**
     * 我的脚本上传制品库按钮权限表达式
     */
    public static final String UPLOAD_PRODUCT_LIBRARY_PER = PER_PREFIX + UPLOAD_PRODUCT_LIBRARY + PER_SUFFIX;
}
