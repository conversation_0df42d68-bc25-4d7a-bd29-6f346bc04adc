package com.ideal.script.mapper;

import com.ideal.script.model.bean.TaskHisParamsBean;
import com.ideal.script.model.entity.TaskParams;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 关联关系，包含任务执行的参数信息Mapper接口
 *
 * <AUTHOR>
 */
public interface TaskParamsMapper {
    /**
     * 查询关联关系，包含任务执行的参数信息
     *
     * @param id 关联关系，包含任务执行的参数信息主键
     * @return 关联关系，包含任务执行的参数信息
     */
    TaskParams selectTaskParamsById(Long id);

    /**
     * 查询关联关系，包含任务执行的参数信息列表
     *
     * @param taskParams 关联关系，包含任务执行的参数信息
     * @return 关联关系，包含任务执行的参数信息集合
     */
    List<TaskParams> selectTaskParamsList(TaskParams taskParams);

    /**
     * 新增关联关系，包含任务执行的参数信息
     *
     * @param taskParams 关联关系，包含任务执行的参数信息
     * @return 结果
     */
    int insertTaskParams(TaskParams taskParams);

    /**
     * 修改关联关系，包含任务执行的参数信息
     *
     * @param taskParams 关联关系，包含任务执行的参数信息
     * @return 结果
     */
    int updateTaskParams(TaskParams taskParams);

    /**
     * 删除关联关系，包含任务执行的参数信息
     *
     * @param id 关联关系，包含任务执行的参数信息主键
     * @return 结果
     */
    int deleteTaskParamsById(Long id);

    /**
     * 删除关联关系，包含任务执行的参数信息
     *
     * @param taskId 任务id
     * @return 结果
     */
    int deleteTaskParamsByTaskId(Long taskId);

    /**
     * 批量删除关联关系，包含任务执行的参数信息
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteTaskParamsByIds(@Param("ids")Long[] ids);

    /**
     * 查询历史参数
     *
     * @param srcScriptUuid 脚本版本表uuid
     * @return int
     * <AUTHOR>
     */
    List<TaskHisParamsBean> selectHisParam(String srcScriptUuid);

    /**
     * 双人复核详情页面-查询参数
     * @param serviceId 业务主键
     * @return List<TaskParams>
     */

    List<TaskParams> selectTaskParamsByServiceId(Long serviceId,Long taskId);
}
