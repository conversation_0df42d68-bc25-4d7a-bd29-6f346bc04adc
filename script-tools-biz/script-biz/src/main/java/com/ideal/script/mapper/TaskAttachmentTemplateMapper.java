package com.ideal.script.mapper;

import com.ideal.script.model.entity.TaskAttachment;

import java.util.List;

/**
 * 常用/克隆任务附件附Mapper接口
 * 
 * <AUTHOR>
 */
public interface TaskAttachmentTemplateMapper
{

    /**
     * 新增脚本任务附件附
     * 
     * @param taskAttachment 任务附件附
     * @return 结果
     */
     int insertTaskAttachment(TaskAttachment taskAttachment);

    /**
     * 根据任务id获取附件信息
     * @param taskId 任务id
     * @return 附件信息
     */
    List<TaskAttachment> selectTaskAttachmentByTaskId(Long taskId);

    /**
     * 根据任务id删除克隆任务
     * @param taskId 任务id
     * @return 执行结果
     */
    int deleteByTaskId(Long taskId);

    /**
     * 根据id删除克隆任务附件
     * @param id 附件id
     * @return 执行结果
     */
    int deleteById(Long id);

    /**
     * 批量更新附件的taskId
     * @return 执行成功条数结果
     */
    int batchUpdateByIds(Long taskId,Long [] ids);

    /**
     * 根据附件id获取附件
     * @param id   附件id
     * @return  附件对象
     */
    TaskAttachment selectAttachmentById(Long id);
}
