package com.ideal.script.mapper;

import java.util.List;

import com.ideal.script.model.bean.IssuerecordBean;
import com.ideal.script.model.entity.IssuerecordEntity;

/**
 * 脚本下发的Mapper接口
 *
 * <AUTHOR>
 * @date 2024-04-13
 */
public interface IssuerecordMapper {
    /**
     * 查询脚本下发的
     *
     * @param id 脚本下发的主键
     * @return 脚本下发的
     */
    IssuerecordEntity selectIssuerecordById(Long id);

    /**
     * 查询脚本下发的列表
     *
     * @param issuerecord 脚本下发的
     * @return 脚本下发的集合
     */
    List<IssuerecordEntity> selectIssuerecordList(IssuerecordBean issuerecord);

    /**
     * 新增脚本下发的
     *
     * @param issuerecord 脚本下发的
     * @return 结果
     */
    int insertIssuerecord(IssuerecordEntity issuerecord);

    /**
     * 修改脚本下发的
     *
     * @param issuerecord 脚本下发的
     * @return 结果
     */
    int updateIssuerecord(IssuerecordEntity issuerecord);

    /**
     * 删除脚本下发的
     *
     * @param id 脚本下发的主键
     * @return 结果
     */
    int deleteIssuerecordById(Long id);

    /**
     * 批量删除脚本下发的
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteIssuerecordByIds(Long[] ids);

    /**
     * 更新脚本下发结果状态
     *
     * @param issuerecord 脚本下发
     * @return int
     */
    int updateIssuerecordByBatchNumber(IssuerecordEntity issuerecord);
}
