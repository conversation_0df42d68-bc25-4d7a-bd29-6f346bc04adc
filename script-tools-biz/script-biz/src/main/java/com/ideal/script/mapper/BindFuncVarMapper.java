package com.ideal.script.mapper;

import com.ideal.script.model.entity.BindFuncVar;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Mapper接口
 *
 * <AUTHOR>
 */
public interface BindFuncVarMapper {
    /**
     * 查询
     *
     * @param id 主键
     * @return 
     */
    BindFuncVar selectBindFuncVarById(Long id);

    /**
     * 查询【函数/变量绑定关系】列表
     *
     * @param bindFuncVar 【函数/变量实体】
     * @return 【函数/变量绑定关系】集合
     */
    List<BindFuncVar> selectBindFuncVarList(BindFuncVar bindFuncVar);

    /**
     * 新增【函数/变量绑定关系】
     *
     * @param bindFuncVar 【函数/变量实体】
     * @return 结果
     */
    int insertBindFuncVar(BindFuncVar bindFuncVar);

    /**
     * 修改【函数/变量绑定关系】
     *
     * @param bindFuncVar 【函数/变量实体】
     * @return 结果
     */
    int updateBindFuncVar(BindFuncVar bindFuncVar);

    /**
     * 删除【函数/变量绑定关系】
     *
     * @param id 【函数/变量的id】主键
     * @return 结果
     */
    int deleteBindFuncVarById(Long id);

    /**
     * 批量删除【函数/变量绑定关系】
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteBindFuncVarByIds(@Param("ids")Long[] ids);

    /**
     * 根据版本uuid 删除绑定关系
     *
     * @param scriptUuid 脚本uuid
     * @return 结果
     */
    int deleteBindFuncVarByScriptUuid(String scriptUuid);

    /**
     * 查询
     *
     * @param bindFuncVar 函数变量绑定关系实体
     * @return 结果
     */
    List<Long> selectBindObjIdList(BindFuncVar bindFuncVar);

    /**
     * 查询
     *
     * @param uuid uuid
     * @return 结果
     */
    List<BindFuncVar> getBindFuncVarByUuid( @Param("uuid") String uuid);

    /**
     * 删除
     *
     * @param oldUuid uuid
     * @return 结果
     */
    int deleteBindFuncVarByUuid(@Param("oldUuid") String oldUuid);
}
