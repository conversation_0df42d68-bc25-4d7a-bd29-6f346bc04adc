<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ideal.script.mapper.AuditRelationMapper">

    <resultMap type="com.ideal.script.model.entity.AuditRelation" id="AuditRelationResult">
            <result property="id" column="iid"/>
            <result property="scriptTaskId" column="iscript_task_id"/>
            <result property="apprWorkitemId" column="iappr_workitem_id"/>
            <result property="srcScriptUuid" column="isrc_script_uuid"/>
            <result property="auditType" column="iaudit_type"/>
            <result property="state" column="istate"/>
            <result property="applyTime" column="iapply_time"/>
            <result property="auditTime" column="iaudit_time"/>
            <result property="auditUser" column="iaudit_user"/>
            <result property="applyUser" column="iapply_user"/>
            <result property="auditUserId" column="iaudit_user_id"/>
            <result property="applyUserId" column="iapply_user_id"/>
            <result property="banOldVersion" column="iban_old_version"/>
            <result property="infoId" column="iinfo_id"/>
            <result property="publicDesc" column="ipublish_desc"/>
    </resultMap>

    <sql id="selectAuditRelationVo">
        select iid, iscript_task_id, iappr_workitem_id, isrc_script_uuid, iaudit_type, istate, iapply_time, iaudit_time, iaudit_user, iapply_user, iaudit_user_id, iapply_user_id,iban_old_version,iwork_order_number
        from ieai_script_audit_relation
    </sql>

    <select id="selectInfoIdAndSrcScriptUuidByAuditRelationId" parameterType="Long"
            resultMap="AuditRelationResult">
        select
            b.isrc_script_uuid as isrc_script_uuid,
            i.iid as iinfo_id,
            r.iaudit_user,
            r.iban_old_version,
            r.ipublish_desc,
            r.iaudit_user_id
        from
            ieai_script_audit_relation r,
            ieai_script_info i,
            ieai_script_info_version b
        where
         i.iunique_uuid=b.iinfo_unique_uuid
        and r.isrc_script_uuid=b.isrc_script_uuid
        and r.iid= #{id}
    </select>


    <select id="selectAuditRelationForAudit" parameterType="com.ideal.script.model.entity.AuditRelation"
            resultMap="AuditRelationResult">
        <include refid="selectAuditRelationVo"/>
        <where>
            <if test="srcScriptUuid != null and srcScriptUuid != '' ">
                and isrc_script_uuid = #{srcScriptUuid}
            </if>
            <if test="scriptTaskId != null and scriptTaskId != '' ">
                and iscript_task_id = #{scriptTaskId}
            </if>
            <if test="auditType != null and auditType != '' ">
                and iaudit_type = #{auditType}
            </if>
        </where>
    </select>


    <select id="selectAuditRelationList" parameterType="com.ideal.script.model.entity.AuditRelation" resultMap="AuditRelationResult">
        <include refid="selectAuditRelationVo"/>
        <where>
                        <if test="scriptTaskId != null ">
                            and iscript_task_id = #{scriptTaskId}
                        </if>
                        <if test="apprWorkitemId != null ">
                            and iappr_workitem_id = #{apprWorkitemId}
                        </if>
                        <if test="srcScriptUuid != null  and srcScriptUuid != ''">
                            and isrc_script_uuid = #{srcScriptUuid}
                        </if>
                        <if test="auditType != null ">
                            and iaudit_type = #{auditType}
                        </if>
                        <if test="state != null ">
                            and istate = #{state}
                        </if>
                        <if test="applyTime != null ">
                            and iapply_time = #{applyTime}
                        </if>
                        <if test="auditTime != null ">
                            and iaudit_time = #{auditTime}
                        </if>
                        <if test="auditUser != null  and auditUser != ''">
                            and iaudit_user = #{auditUser}
                        </if>
                        <if test="applyUser != null  and applyUser != ''">
                            and iapply_user = #{applyUser}
                        </if>
                        <if test="auditUserId != null ">
                            and iaudit_user_id = #{auditUserId}
                        </if>
                        <if test="applyUserId != null ">
                            and iapply_user_id = #{applyUserId}
                        </if>
        </where>
    </select>

    <select id="selectAuditRelationById" parameterType="Long"
            resultMap="AuditRelationResult">
            <include refid="selectAuditRelationVo"/>
            where iid = #{id}
    </select>

    <select id="selectAuditRelationByTaskId" parameterType="Long"
            resultMap="AuditRelationResult">
        <include refid="selectAuditRelationVo"/>
        where iscript_task_id = #{taskId}
    </select>
    <select id="selectAuditRelationByWorkItemId"  parameterType="Long"  resultMap="AuditRelationResult">
            <include refid="selectAuditRelationVo"/>
            where iappr_workitem_id = #{workItemId}
    </select>

    <insert id="insertAuditRelation" parameterType="com.ideal.script.model.entity.AuditRelation" useGeneratedKeys="true"
            keyProperty="id">
        insert into ieai_script_audit_relation
        <trim prefix="(" suffix=")" suffixOverrides=",">
                 <if test="id != null">
                        iid,
                    </if>
                    <if test="scriptTaskId != null">iscript_task_id,
                    </if>
                    <if test="apprWorkitemId != null">iappr_workitem_id,
                    </if>
                    <if test="srcScriptUuid != null">isrc_script_uuid,
                    </if>
                    <if test="auditType != null">iaudit_type,
                    </if>
                    <if test="state != null">istate,
                    </if>
                    iapply_time,
                    <if test="auditTime != null">iaudit_time,
                    </if>
                    <if test="auditUser != null">iaudit_user,
                    </if>
                    <if test="applyUser != null">iapply_user,
                    </if>
                    <if test="auditUserId != null">iaudit_user_id,
                    </if>
                    <if test="applyUserId != null">iapply_user_id,
                    </if>
                    <if test="banOldVersion != null">iban_old_version,
                    </if>
                    <if test="publicDesc != null">ipublish_desc,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="id != null">
                        #{id},
                    </if>
                    <if test="scriptTaskId != null">#{scriptTaskId},
                    </if>
                    <if test="apprWorkitemId != null">#{apprWorkitemId},
                    </if>
                    <if test="srcScriptUuid != null">#{srcScriptUuid},
                    </if>
                    <if test="auditType != null">#{auditType},
                    </if>
                    <if test="state != null">#{state},
                    </if>
                    ${@com.ideal.common.util.DbUtils@getCurrentTime()},
                    <if test="auditTime != null">#{auditTime},
                    </if>
                    <if test="auditUser != null">#{auditUser},
                    </if>
                    <if test="applyUser != null">#{applyUser},
                    </if>
                    <if test="auditUserId != null">#{auditUserId},
                    </if>
                    <if test="applyUserId != null">#{applyUserId},
                    </if>
                    <if test="banOldVersion != null">#{banOldVersion},
                    </if>
                    <if test="publicDesc != null">#{publicDesc},</if>
        </trim>
    </insert>

    <update id="updateAuditRelation" parameterType="com.ideal.script.model.entity.AuditRelation">
        update ieai_script_audit_relation
        <trim prefix="SET" suffixOverrides=",">
                    <if test="scriptTaskId != null">iscript_task_id =
                        #{scriptTaskId},
                    </if>
                    <if test="apprWorkitemId != null">iappr_workitem_id =
                        #{apprWorkitemId},
                    </if>
                    <if test="srcScriptUuid != null">isrc_script_uuid =
                        #{srcScriptUuid},
                    </if>
                    <if test="auditType != null">iaudit_type =
                        #{auditType},
                    </if>
                    <if test="state != null">istate =
                        #{state},
                    </if>
                    <if test="applyTime != null">iapply_time =
                        #{applyTime},
                    </if>
                     iaudit_time = ${@com.ideal.common.util.DbUtils@getCurrentTime()},
                    <if test="auditUser != null">iaudit_user =
                        #{auditUser},
                    </if>
                    <if test="applyUser != null">iapply_user =
                        #{applyUser},
                    </if>
                    <if test="auditUserId != null">iaudit_user_id =
                        #{auditUserId},
                    </if>
                    <if test="applyUserId != null">iapply_user_id =
                        #{applyUserId},
                    </if>
        </trim>
        where iid = #{id}
    </update>
    <update id="updateAuditRelationByWorkItemId">
        update ieai_script_audit_relation
        <trim prefix="SET" suffixOverrides=",">
            <if test="scriptTaskId != null">iscript_task_id =
                #{scriptTaskId},
            </if>
            <if test="srcScriptUuid != null">isrc_script_uuid =
                #{srcScriptUuid},
            </if>
            <if test="auditType != null">iaudit_type =
                #{auditType},
            </if>
            <if test="state != null">istate =
                #{state},
            </if>
            <if test="applyTime != null">iapply_time =
                #{applyTime},
            </if>
            iaudit_time = ${@com.ideal.common.util.DbUtils@getCurrentTime()},
            <if test="auditUser != null">iaudit_user =
                #{auditUser},
            </if>
            <if test="applyUser != null">iapply_user =
                #{applyUser},
            </if>
            <if test="auditUserId != null">iaudit_user_id =
                #{auditUserId},
            </if>
            <if test="applyUserId != null">iapply_user_id =
                #{applyUserId},
            </if>
        </trim>
        where iappr_workitem_id = #{apprWorkitemId}
    </update>

    <update id="updateAuditRelationForAudit">
        update ieai_script_audit_relation set istate = #{state}
        <where>
            <if test="srcScriptUuid != null and srcScriptUuid != '' ">
                and isrc_script_uuid = #{srcScriptUuid}
            </if>
            <if test="scriptTaskId != null and scriptTaskId != '' ">
                and iscript_task_id = #{scriptTaskId}
            </if>
        </where>
    </update>

    <delete id="deleteAuditRelationById" parameterType="Long">
        delete
        from ieai_script_audit_relation where iid = #{id}
    </delete>

    <delete id="deleteAuditRelationByIds" parameterType="String">
        delete from ieai_script_audit_relation where iid in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>


    <select id="selectAuditRelationByScriptInfoVersionId" parameterType="Long"
            resultMap="AuditRelationResult">
        <include refid="selectAuditRelationVo"/>
        where iaudit_type = 1
        AND isrc_script_uuid IN (
        select isrc_script_uuid from ieai_script_info_version where iid in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
        )
    </select>

</mapper>