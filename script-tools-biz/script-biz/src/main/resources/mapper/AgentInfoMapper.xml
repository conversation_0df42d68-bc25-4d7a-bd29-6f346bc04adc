<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ideal.script.mapper.AgentInfoMapper">

    <resultMap type="com.ideal.script.model.entity.AgentInfo" id="AgentInfoResult">
            <result property="id" column="iid"/>
            <result property="sysmAgentInfoId" column="isysm_agent_info_id"/>
            <result property="agentIp" column="iagent_ip"/>
            <result property="agentName" column="iagent_name"/>
            <result property="agentPort" column="iagent_port"/>
            <result property="agentState" column="iagent_state"/>
            <result property="createTime" column="icreate_time"/>
            <result property="execUserName" column="iexec_user_name"/>
            <result property="deviceName" column="idevice_name"/>
            <result property="osName" column="ios_name"/>
            <result property="centerName" column="icenter_name"/>
    </resultMap>

    <resultMap type="com.ideal.script.model.bean.TaskBindAgentInfoBean" id="TaskBindAgentInfoBean">

            <result property="taskIpsId" column="itask_ips_id"/>
            <result property="id" column="iid"/>
            <result property="sysmAgentInfoId" column="isysm_agent_info_id"/>
            <result property="agentIp" column="iagent_ip"/>
            <result property="agentName" column="iagent_name"/>
            <result property="agentPort" column="iagent_port"/>
            <result property="agentState" column="iagent_state"/>
            <result property="createTime" column="icreate_time"/>
            <result property="execUserName" column="iexec_user_name"/>
            <result property="deviceName" column="idevice_name"/>
            <result property="osName" column="ios_name"/>
            <result property="centerId" column="icenter_id"/>
            <result property="centerName" column="icenter_name"/>

    </resultMap>

    <sql id="selectAgentInfoVo">
        select iid, isysm_agent_info_id, iagent_ip, iagent_name, iagent_port, iagent_state, icreate_time, iexec_user_name, idevice_name, ios_name
        from ieai_script_agent_info
    </sql>

    <select id="selectAgentInfoList" parameterType="com.ideal.script.model.entity.AgentInfo" resultMap="AgentInfoResult">
        <include refid="selectAgentInfoVo"/>
        <where>
                        <if test="sysmAgentInfoId != null ">
                            and isysm_agent_info_id = #{sysmAgentInfoId}
                        </if>
                        <if test="agentIp != null  and agentIp != ''">
                            and iagent_ip = #{agentIp}
                        </if>
                        <if test="agentName != null  and agentName != ''">
                            and iagent_name like concat('%', #{agentName}, '%')
                        </if>
                        <if test="agentPort != null ">
                            and iagent_port = #{agentPort}
                        </if>
                        <if test="agentState != null ">
                            and iagent_state = #{agentState}
                        </if>
                        <if test="execUserName != null  and execUserName != ''">
                            and iexec_user_name like concat('%', #{execUserName}, '%')
                        </if>
                        <if test="deviceName != null  and deviceName != ''">
                            and idevice_name like concat('%', #{deviceName}, '%')
                        </if>
                        <if test="osName != null  and osName != ''">
                            and ios_name like concat('%', #{osName}, '%')
                        </if>
        </where>
    </select>

    <select id="selectAgentInfoById" parameterType="Long"
            resultMap="AgentInfoResult">
            <include refid="selectAgentInfoVo"/>
            where iid = #{id}
    </select>

    <select id="getTaskRuntimeAgentInfo" resultMap="TaskBindAgentInfoBean">
        SELECT
        a.iid as itask_ips_id,
        b.iid,
        b.isysm_agent_info_id,
        b.iagent_ip,
        b.iagent_name,
        b.iagent_port,
        b.iagent_state,
        b.icreate_time,
        b.iexec_user_name,
        b.idevice_name,
        b.ios_name,
        b.icenter_id,
        b.icenter_name
        FROM
        ieai_script_task_ips a,
        ieai_script_agent_info b,
        ieai_script_task_runtime c
        WHERE
        a.iscript_agentinfo_id = b.iid
        AND a.iid = c.iscript_task_ips_id
        AND a.iscript_task_id = #{scriptTaskId}
        AND c.iscript_task_id = #{scriptTaskId}
        AND c.istate = 80
        <if  test="queryType == null ">
            and a.ialreadyimp_flag = 0
        </if>
        <if test="agentInfo.sysmAgentInfoId != null ">
            and b.isysm_agent_info_id = #{agentInfo.sysmAgentInfoId}
        </if>
        <if test="agentInfo.agentIp != null  and agentInfo.agentIp != ''">
            and b.iagent_ip = #{agentInfo.agentIp}
        </if>
        <if test="agentInfo.agentName != null  and agentInfo.agentName != ''">
            and b.iagent_name like concat('%', #{agentInfo.agentName}, '%')
        </if>
        <if test="agentInfo.agentPort != null ">
            and b.iagent_port = #{agentInfo.agentPort}
        </if>
        <if test="agentInfo.agentState != null ">
            and b.iagent_state = #{agentInfo.agentState}
        </if>
        <if test="agentInfo.osName != null  and agentInfo.osName != ''">
            and b.ios_name like concat('%', #{agentInfo.osName}, '%')
        </if>

    </select>

    <select id="getTaskBindAgentInfo" resultMap="TaskBindAgentInfoBean">
        SELECT
            a.iid as itask_ips_id,
            b.iid,
            b.isysm_agent_info_id,
            b.iagent_ip,
            b.iagent_name,
            b.iagent_port,
            b.iagent_state,
            b.icreate_time,
            b.iexec_user_name,
            b.idevice_name,
            b.ios_name,
            b.icenter_id,
            b.icenter_name
        FROM
            ieai_script_task_ips a,
            ieai_script_agent_info b
        WHERE
            a.iscript_agentinfo_id = b.iid
        AND a.iscript_task_id = #{scriptTaskId}
        <if  test="queryType == null ">
           and a.ialreadyimp_flag = 0
        </if>
        <if test="agentInfo.sysmAgentInfoId != null ">
            and b.isysm_agent_info_id = #{agentInfo.sysmAgentInfoId}
        </if>
        <if test="agentInfo.agentIp != null  and agentInfo.agentIp != ''">
            and b.iagent_ip = #{agentInfo.agentIp}
        </if>
        <if test="agentInfo.agentName != null  and agentInfo.agentName != ''">
            and b.iagent_name like concat('%', #{agentInfo.agentName}, '%')
        </if>
        <if test="agentInfo.agentPort != null ">
            and b.iagent_port = #{agentInfo.agentPort}
        </if>
        <if test="agentInfo.agentState != null ">
            and b.iagent_state = #{agentInfo.agentState}
        </if>
        <if test="agentInfo.osName != null  and agentInfo.osName != ''">
            and b.ios_name like concat('%', #{agentInfo.osName}, '%')
        </if>

    </select>

    <insert id="insertAgentInfo" parameterType="com.ideal.script.model.entity.AgentInfo">
        insert into ieai_script_agent_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="id != null">iid,
                    </if>
                    <if test="sysmAgentInfoId != null">isysm_agent_info_id,
                    </if>
                    <if test="agentIp != null">iagent_ip,
                    </if>
                    <if test="agentName != null">iagent_name,
                    </if>
                    <if test="agentPort != null">iagent_port,
                    </if>
                    <if test="agentState != null">iagent_state,
                    </if>
                    icreate_time,
                    <if test="execUserName != null">iexec_user_name,
                    </if>
                    <if test="deviceName != null">idevice_name,
                    </if>
                    <if test="osName != null">ios_name,
                    </if>
                    <if test="centerName != null">
                        icenter_name,
                    </if>
                    <if test="centerId != null">
                        icenter_id
                    </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="id != null">#{id},
                    </if>
                    <if test="sysmAgentInfoId != null">#{sysmAgentInfoId},
                    </if>
                    <if test="agentIp != null">#{agentIp},
                    </if>
                    <if test="agentName != null">#{agentName},
                    </if>
                    <if test="agentPort != null">#{agentPort},
                    </if>
                    <if test="agentState != null">#{agentState},
                    </if>
                    ${@com.ideal.common.util.DbUtils@getCurrentTime()},
                    <if test="execUserName != null">#{execUserName},
                    </if>
                    <if test="deviceName != null">#{deviceName},
                    </if>
                    <if test="osName != null">#{osName},
                    </if>
                    <if test="centerName != null">
                        #{centerName},
                    </if>
                    <if test="centerId != null">
                        #{centerId}
                    </if>
        </trim>
    </insert>

    <update id="updateAgentInfo" parameterType="com.ideal.script.model.entity.AgentInfo">
        update ieai_script_agent_info
        <trim prefix="SET" suffixOverrides=",">
                    <if test="sysmAgentInfoId != null">isysm_agent_info_id =
                        #{sysmAgentInfoId},
                    </if>
                    <if test="agentIp != null">iagent_ip =
                        #{agentIp},
                    </if>
                    <if test="agentName != null">iagent_name =
                        #{agentName},
                    </if>
                    <if test="agentPort != null">iagent_port =
                        #{agentPort},
                    </if>
                    <if test="agentState != null">iagent_state =
                        #{agentState},
                    </if>
                    <if test="execUserName != null">iexec_user_name =
                        #{execUserName},
                    </if>
                    <if test="deviceName != null">idevice_name =
                        #{deviceName},
                    </if>
                    <if test="osName != null">ios_name =
                        #{osName},
                    </if>
        </trim>
        where iid = #{id}
    </update>

    <delete id="deleteAgentInfoById" parameterType="Long">
        delete
        from ieai_script_agent_info where iid = #{id}
    </delete>

    <delete id="deleteAgentInfoByIds" parameterType="String">
        delete from ieai_script_agent_info where iid in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!--检查ieai_script_agent_info表中是否已存在相同的记录(IP+端口)-->
    <select id="checkAgentInfoExists" resultType="boolean">
        select case when count(1) > 0 then true else false end as iexist
         from ieai_script_agent_info
        where iagent_ip = #{agentIp} and iagent_port = #{agentPort}
    </select>

    <select id="selectAgentInfoByServiceId"  parameterType="Long"
            resultMap="AgentInfoResult">
             select
            a.iid,
            a.isysm_agent_info_id,
            a.iagent_ip,
            a.iagent_name,
            a.iagent_port,
            a.iagent_state,
            a.icreate_time,
            a.iexec_user_name,
            a.idevice_name,
            a.ios_name,
            a.icenter_name
        from
            ieai_script_agent_info a,
            ieai_script_task_ips b
        where
            b.iscript_agentinfo_id = a.iid
          <if test="serviceId != null">
              and b.iscript_task_id in (
              select
              iscript_task_id
              from
              ieai_script_audit_relation
              where
              iid = #{serviceId}
              )
          </if>
          <if test="serviceId == null and taskId != null">
              and b.iscript_task_id = #{taskId}
          </if>

        and b.isysm_computer_group_id is null
     </select>


    <select id="selectAgentInfoByIpAndPort"  parameterType="List"
            resultMap="AgentInfoResult">
        select * from ieai_script_agent_info
        where 1 =1 and
        <foreach collection="agentInfoDtoList" item="listValue" open="(" separator="or" close=")">
            ( iagent_ip = #{listValue.agentIp} and iagent_port = #{listValue.agentPort} )
        </foreach>
    </select>


</mapper>