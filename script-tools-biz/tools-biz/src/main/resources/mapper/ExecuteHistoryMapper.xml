<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ideal.tools.mapper.ExecuteHistoryMapper">
    <resultMap type="com.ideal.tools.model.entity.ExecuteHistoryEntity" id="ExecuteHistoryResult">
        <result property="id" column="iid"/>
        <result property="toolId" column="itool_id"/>
        <result property="code" column="icode"/>
        <result property="name" column="iname"/>
        <result property="type" column="itype"/>
        <result property="scriptName" column="iscript_name"/>
        <result property="scriptIds" column="iscript_ids"/>
        <result property="scriptOperatingUser" column="iscript_operating_user"/>
        <result property="scriptType" column="iscript_type"/>
        <result property="effect" column="ieffect"/>
        <result property="usageScenario" column="iusage_scenario"/>
        <result property="scriptEditing" column="iscript_editing"/>
        <result property="estimateOperationalRisk" column="iestimate_operational_risk"/>
        <result property="keyword" column="ikeyword"/>
        <result property="businessSystemId" column="ibusiness_system_id"/>
        <result property="businessSystemName" column="ibusiness_system_name"/>
        <result property="matchIp" column="imatch_ip"/>
        <result property="firstDescribe" column="ifirst_describe"/>
        <result property="describe" column="idescribe"/>
        <result property="osType" column="ios_type"/>
        <result property="oneTypeId" column="ione_type_id"/>
        <result property="twoTypeId" column="itwo_type_id"/>
        <result property="threeTypeId" column="ithree_type_id"/>
        <result property="highRisk" column="ihigh_risk"/>
        <result property="childIds" column="ichild_ids"/>
        <result property="deliveryStatus" column="idelivery_status"/>
        <result property="deliveryAuditorId" column="idelivery_auditor_id"/>
        <result property="deliveryReturnCause" column="idelivery_return_cause"/>
        <result property="deliveryReceptionTime" column="idelivery_reception_time"/>
        <result property="creatorId" column="icreator_id"/>
        <result property="creatorName" column="icreator_name"/>
        <result property="createTime" column="icreate_time"/>
        <result property="eventNum" column="ievent_num"/>
        <result property="runStatus" column="irun_status"/>
        <result property="runTime" column="irun_time"/>
        <result property="endTime" column="iend_time"/>
        <result property="executorId" column="iexecutor_id"/>
        <result property="executorName" column="iexecutor_name"/>
        <result property="dangerCmd" column="idanger_cmd"/>
        <result property="oneTypeName" column="ione_type_name"/>
        <result property="twoTypeName" column="itwo_type_name"/>
        <result property="threeTypeName" column="ithree_type_name"/>
        <result property="execFrom" column="iexec_from"/>
        <result property="tdAuditId" column="itd_audit_id"/>
        <result property="runningId" column="irunning_id"/>
        <result property="classification" column="iclassification"/>
    </resultMap>

    <resultMap type="com.ideal.tools.model.entity.ExecuteHistoryFilesEntity" id="ExecuteHistoryFilesResult">
        <result property="id" column="iid"/>
        <result property="toolsId" column="itd_tools_id"/>
        <result property="tdHistoryId" column="itd_history_id"/>
        <result property="name" column="iname"/>
        <result property="files" column="ifiles"/>
        <result property="creatorId" column="icreator_id"/>
        <result property="creatorName" column="icreator_name"/>
        <result property="createTime" column="icreate_time"/>
        <result property="size" column="isize"/>
    </resultMap>

    <resultMap type="com.ideal.tools.model.bean.ExecuteHistoryBean" id="ExecuteHistoryBeanResult">
        <result property="id" column="iid"/>
        <result property="toolId" column="itool_id"/>
        <result property="code" column="icode"/>
        <result property="name" column="iname"/>
        <result property="type" column="itype"/>
        <result property="scriptName" column="iscript_name"/>
        <result property="scriptIds" column="iscript_ids"/>
        <result property="scriptOperatingUser" column="iscript_operating_user"/>
        <result property="scriptType" column="iscript_type"/>
        <result property="effect" column="ieffect"/>
        <result property="usageScenario" column="iusage_scenario"/>
        <result property="scriptEditing" column="iscript_editing"/>
        <result property="estimateOperationalRisk" column="iestimate_operational_risk"/>
        <result property="keyword" column="ikeyword"/>
        <result property="businessSystemId" column="ibusiness_system_id"/>
        <result property="businessSystemName" column="ibusiness_system_name"/>
        <result property="matchIp" column="imatch_ip"/>
        <result property="firstDescribe" column="ifirst_describe"/>
        <result property="describe" column="idescribe"/>
        <result property="osType" column="ios_type"/>
        <result property="oneTypeId" column="ione_type_id"/>
        <result property="twoTypeId" column="itwo_type_id"/>
        <result property="threeTypeId" column="ithree_type_id"/>
        <result property="highRisk" column="ihigh_risk"/>
        <result property="childIds" column="ichild_ids"/>
        <result property="deliveryStatus" column="idelivery_status"/>
        <result property="deliveryAuditorId" column="idelivery_auditor_id"/>
        <result property="deliveryReturnCause" column="idelivery_return_cause"/>
        <result property="deliveryReceptionTime" column="idelivery_reception_time"/>
        <result property="creatorId" column="icreator_id"/>
        <result property="creatorName" column="icreator_name"/>
        <result property="createTime" column="icreate_time"/>
        <result property="eventNum" column="ievent_num"/>
        <result property="runStatus" column="irun_status"/>
        <result property="runTime" column="irun_time"/>
        <result property="endTime" column="iend_time"/>
        <result property="executorId" column="iexecutor_id"/>
        <result property="executorName" column="iexecutor_name"/>
        <result property="dangerCmd" column="idanger_cmd"/>
        <result property="oneTypeName" column="ione_type_name"/>
        <result property="twoTypeName" column="itwo_type_name"/>
        <result property="threeTypeName" column="ithree_type_name"/>
        <result property="execFrom" column="iexec_from"/>
        <result property="tdAuditId" column="itd_audit_id"/>
        <result property="runningId" column="irunning_id"/>
        <result property="classification" column="iclassification"/>
        <collection property="params" ofType="com.ideal.tools.model.bean.ExecuteParamsBean">
            <result property="name" column="iname_params"/>
            <result property="sort" column="isort_params"/>
            <result property="type" column="itype_params"/>
            <result property="value" column="ivalue_params"/>
            <result property="description" column="idescription_params"/>
        </collection>
        <collection property="files" ofType="com.ideal.tools.model.bean.ExecuteFilesBean">
            <result property="id" column="files_id"/>
            <result property="toolsId" column="files_tools_id"/>
            <result property="tdHistoryId" column="files_history_id"/>
            <result property="name" column="files_name"/>
            <result property="files" column="files_ifiles"/>
            <result property="creatorId" column="files_creator_id"/>
            <result property="creatorName" column="files_creator_name"/>
            <result property="createTime" column="files_create_time"/>
            <result property="size" column="files_size"/>
        </collection>
        <collection property="projects" ofType="com.ideal.tools.model.bean.ExecuteProjectBean">
            <result property="id" column="iid_projects"/>
            <result property="parentId" column="iparent_id_projects"/>
            <result property="nodeName" column="inode_name_projects"/>
            <result property="nodeStatus" column="inode_status_projects"/>
            <result property="projectId" column="iproject_id_projects"/>
            <result property="projectName" column="iproject_name_projects"/>
            <result property="toolId" column="itool_id_projects"/>
            <result property="toolType" column="itool_type_projects"/>
            <result property="workflowId" column="iworkflow_id_projects"/>
            <result property="workflowName" column="iworkflow_name_projects"/>
            <result property="flowId" column="iflow_id_projects"/>
            <result property="nodeId" column="itd_node_id_projects"/>
            <result property="actStatus" column="iact_status_projects"/>
            <result property="xml" column="ixml_projects"/>
        </collection>
    </resultMap>

    <insert id="insertHistory" parameterType="com.ideal.tools.model.entity.ExecuteHistoryEntity" >
        insert into ieai_tb_execute_history
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">iid,
            </if>
            <if test="toolId != null">itool_id,
            </if>
            <if test="code != null">icode,
            </if>
            <if test="name != null">iname,
            </if>
            <if test="type != null">itype,
            </if>
            <if test="scriptName != null">iscript_name,
            </if>
            <if test="scriptIds != null">iscript_ids,
            </if>
            <if test="scriptOperatingUser != null">iscript_operating_user,
            </if>
            <if test="scriptType != null">iscript_type,
            </if>
            <if test="effect != null">ieffect,
            </if>
            <if test="usageScenario != null">iusage_scenario,
            </if>
            <if test="scriptEditing != null">iscript_editing,
            </if>
            <if test="estimateOperationalRisk != null">iestimate_operational_risk,
            </if>
            <if test="keyword != null">ikeyword,
            </if>
            <if test="businessSystemId != null">ibusiness_system_id,
            </if>
            <if test="businessSystemName != null">ibusiness_system_name,
            </if>
            <if test="matchIp != null">imatch_ip,
            </if>
            <if test="firstDescribe != null">ifirst_describe,
            </if>
            <if test="describe != null">idescribe,
            </if>
            <if test="osType != null">ios_type,
            </if>
            <if test="oneTypeId != null">ione_type_id,
            </if>
            <if test="twoTypeId != null">itwo_type_id,
            </if>
            <if test="threeTypeId != null">ithree_type_id,
            </if>
            <if test="highRisk != null">ihigh_risk,
            </if>
            <if test="childIds != null">ichild_ids,
            </if>
            <if test="deliveryStatus != null">idelivery_status,
            </if>
            <if test="deliveryAuditorId != null">idelivery_auditor_id,
            </if>
            <if test="deliveryReturnCause != null">idelivery_return_cause,
            </if>
            <if test="deliveryReceptionTime != null">idelivery_reception_time,
            </if>
            <if test="creatorId != null">icreator_id,
            </if>
            <if test="creatorName != null">icreator_name,
            </if>
            <if test="createTime != null">icreate_time,
            </if>
            <if test="eventNum != null">ievent_num,
            </if>
            <if test="runStatus != null">irun_status,
            </if>
            <if test="executorId != null">iexecutor_id,
            </if>
            <if test="executorName != null">iexecutor_name,
            </if>
            <if test="dangerCmd != null">idanger_cmd,
            </if>
            <if test="execFrom != null">iexec_from,
            </if>
            <if test="tdAuditId != null">itd_audit_id,
            </if>
            <if test="runningId != null">irunning_id,
            </if>
            <if test="classification != null">iclassification,
            </if>
            <if test="runTime != null">irun_time,
            </if>
            <if test="runTime == null and type == 1">irun_time,
            </if>
            iend_time,
            <if test="sortTime != null">
                isort_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},
            </if>
            <if test="toolId != null">#{toolId},
            </if>
            <if test="code != null">#{code},
            </if>
            <if test="name != null">#{name},
            </if>
            <if test="type != null">#{type},
            </if>
            <if test="scriptName != null">#{scriptName},
            </if>
            <if test="scriptIds != null">#{scriptIds},
            </if>
            <if test="scriptOperatingUser != null">#{scriptOperatingUser},
            </if>
            <if test="scriptType != null">#{scriptType},
            </if>
            <if test="effect != null">#{effect},
            </if>
            <if test="usageScenario != null">#{usageScenario},
            </if>
            <if test="scriptEditing != null">#{scriptEditing},
            </if>
            <if test="estimateOperationalRisk != null">#{estimateOperationalRisk},
            </if>
            <if test="keyword != null">#{keyword},
            </if>
            <if test="businessSystemId != null">#{businessSystemId},
            </if>
            <if test="businessSystemName != null">#{businessSystemName},
            </if>
            <if test="matchIp != null">#{matchIp},
            </if>
            <if test="firstDescribe != null">#{firstDescribe},
            </if>
            <if test="describe != null">#{describe},
            </if>
            <if test="osType != null">#{osType},
            </if>
            <if test="oneTypeId != null">#{oneTypeId},
            </if>
            <if test="twoTypeId != null">#{twoTypeId},
            </if>
            <if test="threeTypeId != null">#{threeTypeId},
            </if>
            <if test="highRisk != null">#{highRisk},
            </if>
            <if test="childIds != null">#{childIds},
            </if>
            <if test="deliveryStatus != null">#{deliveryStatus},
            </if>
            <if test="deliveryAuditorId != null">#{deliveryAuditorId},
            </if>
            <if test="deliveryReturnCause != null">#{deliveryReturnCause},
            </if>
            <if test="deliveryReceptionTime != null">#{deliveryReceptionTime},
            </if>
            <if test="creatorId != null">#{creatorId},
            </if>
            <if test="creatorName != null">#{creatorName},
            </if>
            <if test="createTime != null">#{createTime},
            </if>
            <if test="eventNum != null">#{eventNum},
            </if>
            <if test="runStatus != null">#{runStatus},
            </if>
            <if test="executorId != null">#{executorId},
            </if>
            <if test="executorName != null">#{executorName},
            </if>
            <if test="dangerCmd != null">#{dangerCmd},
            </if>
            <if test="execFrom != null">#{execFrom},
            </if>
            <if test="tdAuditId != null">#{tdAuditId},
            </if>
            <if test="runningId != null">#{runningId},
            </if>
            <if test="classification != null">#{classification},
            </if>

            <if test="runTime != null">#{runTime},
            </if>
            <if test="runTime == null and type == 1">${@com.ideal.common.util.DbUtils@getCurrentTime()},
            </if>

            <if test="endTime != null">#{endTime},
            </if>
            <if test="endTime == null"> ${@com.ideal.common.util.DbUtils@getCurrentTime()},
            </if>
            <if test="sortTime != null">
                #{sortTime},
            </if>
<!--            ${@com.ideal.common.util.DbUtils@getCurrentTime()},-->
        </trim>
    </insert>

    <insert id="insertParams" parameterType="com.ideal.tools.model.entity.ExecuteHistoryParamsEntity">
        insert into ieai_tb_execute_history_params(iid, itools_id, itd_history_id, isort, iname, itype, ivalue, idescription) values
        <foreach collection="list" item="params" index="index" separator=",">
            (#{params.id}, #{params.toolsId}, #{params.tdHistoryId}, #{params.sort}, #{params.name}, #{params.type}, #{params.value}, #{params.description})
        </foreach>
    </insert>

    <insert id="insertProjects" parameterType="com.ideal.tools.model.entity.ExecuteHistoryProjectEntity">
        insert into ieai_tb_execute_history_project(iid, iparent_id, itd_history_id, iproject_id, iproject_name, iworkflow_id,
        iworkflow_name, inode_name, inode_status, itool_type, itool_id, iflow_id, itd_node_id, iact_status) values
        <foreach collection="list" item="projects" index="index" separator=",">
            (#{projects.id}, #{projects.parentId}, #{projects.tdHistoryId}, #{projects.projectId}, #{projects.projectName}, #{projects.workflowId},
            #{projects.workflowName}, #{projects.nodeName}, #{projects.nodeStatus}, #{projects.toolType}, #{projects.toolId}, #{projects.flowId}, #{projects.nodeId}, #{projects.actStatus})
        </foreach>
    </insert>

    <insert id="insertProjectXml" parameterType="com.ideal.tools.model.entity.ExecuteHistoryProjectXmlEntity">
        insert into ieai_tb_execute_history_project_xml(iid, itd_history_project_id, iworkflow_content) values
        <foreach collection="list" item="projectsXml" index="index" separator=",">
            (#{projectsXml.id}, #{projectsXml.tdHistoryProjectId}, #{projectsXml.workflowContent})
        </foreach>
    </insert>

    <insert id="insertFiles" parameterType="com.ideal.tools.model.entity.ExecuteHistoryFilesEntity">
        insert into ieai_tb_execute_history_files(iid, itd_tools_id, itd_history_id, iname, ifiles, icreator_id, icreator_name, icreate_time, isize) values
        <foreach collection="list" item="files" index="index" separator=",">
            (#{files.id}, #{files.toolsId}, #{files.tdHistoryId}, #{files.name}, #{files.files}, #{files.creatorId},#{files.creatorName}, #{files.createTime}, #{files.size})
        </foreach>
    </insert>

    <sql id="selectExecuteHistoryFiles">
        select iid, itd_tools_id, itd_history_id, iname, ifiles, icreator_id,icreator_name, icreate_time, isize
        from ieai_tb_execute_history_files
    </sql>

    <select id="selectExecuteHistoryList" parameterType="com.ideal.tools.model.bean.ExecuteHistoryBean" resultMap="ExecuteHistoryResult">
        select em.iid, em.itool_id, em.icode, em.iname, em.itype, em.iscript_name, em.iscript_ids, em.iscript_operating_user, em.iscript_type, em.ieffect, em.iusage_scenario,
            em.iscript_editing, em.iestimate_operational_risk, em.ikeyword, em.ibusiness_system_id, em.ibusiness_system_name, em.imatch_ip, em.ifirst_describe, em.idescribe,
            em.ione_type_id, (select iclassname from ieai_tb_category where iid = ione_type_id) as ione_type_name,
            em.itwo_type_id, (select iclassname from ieai_tb_category where iid = itwo_type_id) as itwo_type_name,
            em.ithree_type_id, (select iclassname from ieai_tb_category where iid = ithree_type_id) as ithree_type_name,
            em.ihigh_risk, em.ichild_ids, em.idelivery_status, em.idelivery_auditor_id, em.idelivery_return_cause, em.idelivery_reception_time, em.icreator_id, em.ios_type,
            em.icreator_name, em.icreate_time, em.ievent_num, em.irun_status, em.irun_time, em.iend_time, em.iexecutor_id, em.iexecutor_name, em.idanger_cmd, em.iexec_from,
            em.itd_audit_id, em.irunning_id, em.iclassification
        from ieai_tb_execute_history em
        <where>
            <if test="code != null ">
                <bind name="codeSql" value="'%'+code+'%'"/>
                and em.icode like #{codeSql}
            </if>
            <if test="name != null">
                <bind name="nameSql" value="'%'+name+'%'"/>
                and em.iname like #{nameSql}
            </if>
            <if test="businessSystemId != null">
                and em.ibusiness_system_id = #{businessSystemId}
            </if>
            <if test="type != null">
                and em.itype = #{type}
            </if>
            <if test="runStatus != null">
                and em.irun_status = #{runStatus}
            </if>
            <if test="executorId != null">
                and em.iexecutor_id = #{executorId}
            </if>
            <if test="executorName != null">
                <bind name="executorNameSql" value="'%'+executorName+'%'"/>
                and em.iexecutor_name like #{executorNameSql}
            </if>
            <if test="oneTypeId != null">
                and em.ione_type_id = #{oneTypeId}
            </if>
            <if test="twoTypeId != null">
                and em.itwo_type_id = #{twoTypeId}
            </if>
            <if test="threeTypeId != null">
                and em.ithree_type_id = #{threeTypeId}
            </if>
            <if test="classification != null">
                and em.iclassification = #{classification}
            </if>
            <if test="creatorName != null">
                <bind name="creatorNameSql" value="'%'+creatorName+'%'"/>
                and em.icreator_name like #{creatorName}
            </if>
            <if test="runTime != null">
                <![CDATA[and em.irun_time >= #{runTime} ]]>
            </if>
            <if test="endTime != null">
                <![CDATA[and em.irun_time <= #{endTime} ]]>
            </if>
        </where>
        order by em.irun_time desc
    </select>

    <sql id="selectExecuteHistorySql">
        select em.iid, em.itool_id, em.icode, em.iname, em.itype, em.iscript_name, em.iscript_ids, em.iscript_operating_user, em.iscript_type, em.ieffect, em.iusage_scenario,
               em.iscript_editing, em.iestimate_operational_risk, em.ikeyword, em.ibusiness_system_id, em.ibusiness_system_name, em.imatch_ip, em.ifirst_describe, em.idescribe,
               em.ione_type_id, (select iclassname from ieai_tb_category where iid = ione_type_id) as ione_type_name,
               em.itwo_type_id, (select iclassname from ieai_tb_category where iid = itwo_type_id) as itwo_type_name,
               em.ithree_type_id, (select iclassname from ieai_tb_category where iid = ithree_type_id) as ithree_type_name,
               em.ihigh_risk, em.ichild_ids, em.idelivery_status, em.idelivery_auditor_id, em.idelivery_return_cause, em.idelivery_reception_time, em.icreator_id, em.ios_type,
               em.icreator_name, em.icreate_time, em.ievent_num, em.irun_status, em.irun_time, em.iend_time, em.iexecutor_id, em.iexecutor_name, em.idanger_cmd, em.iexec_from,
               em.itd_audit_id, em.irunning_id, em.iclassification,
               emp.iname as iname_params, emp.isort as isort_params, emp.itype as itype_params, emp.ivalue as ivalue_params, emp.idescription as idescription_params,
               emf.iid as files_id, emf.itd_tools_id as files_tools_id, emf.itd_history_id as files_history_id, emf.iname as files_name, emf.ifiles as files_ifiles,
               emf.icreator_id as files_creator_id, emf.icreator_name as files_creator_name, emf.icreate_time as files_create_time, emf.isize as files_size,
               empro.iid as iid_projects, empro.iparent_id as iparent_id_projects, empro.inode_name as inode_name_projects, empro.inode_status as inode_status_projects,
               empro.iproject_id as iproject_id_projects, empro.iproject_name as iproject_name_projects, empro.itool_id as itool_id_projects, empro.itool_type as itool_type_projects,
               empro.iworkflow_id as iworkflow_id_projects, empro.iworkflow_name as iworkflow_name_projects, empro.iflow_id as iflow_id_projects,empro.itd_node_id as itd_node_id_projects,
               empro.iact_status as iact_status_projects, empx.iworkflow_content as ixml_projects
        from ieai_tb_execute_history em
        left join ieai_tb_execute_history_params emp on em.iid = emp.itd_history_id
        left join ieai_tb_execute_history_files emf on em.iid = emf.itd_history_id
        left join ieai_tb_execute_history_project empro on em.iid = empro.itd_history_id
        left join ieai_tb_execute_history_project_xml empx on empro.iid = empx.itd_history_project_id
    </sql>
    <select id="selectExecuteHistoryById" parameterType="Long" resultMap="ExecuteHistoryBeanResult">
        <include refid="selectExecuteHistorySql" />
        where em.iid = #{id}
    </select>
    <select id="selectNumBuAuditId" parameterType="Long" resultType="java.lang.Integer">
        select count(0) from ieai_tb_execute_history where itd_audit_id = #{auditId}
    </select>

    <select id="selectHistoryMonitorByToolsId" parameterType="Long" resultMap="ExecuteHistoryBeanResult">
        <include refid="selectExecuteHistorySql" />
        <where>
            <if test="execFrom != null">
                and em.iexec_from = #{execFrom}
            </if>
            <if test="toolsId != null">
                and em.itool_id = #{toolsId}
            </if>
        </where>
        order by em.isort_time desc
    </select>

    <select id="selectExecuteHistoryFilesById" parameterType="Long" resultMap="ExecuteHistoryFilesResult">
        <include refid="selectExecuteHistoryFiles"/>
        where iid = #{id}
    </select>

    <select id="selectHistoryMonitorSuccessByToolsId" parameterType="Long" resultMap="ExecuteHistoryBeanResult">
        <include refid="selectExecuteHistorySql" />
        <where>
            <if test="status != null">
                and em.irun_status = #{status}
            </if>
            <if test="toolsId != null">
                and em.itool_id = #{toolsId}
            </if>
        </where>
        order by em.isort_time desc
    </select>
</mapper>