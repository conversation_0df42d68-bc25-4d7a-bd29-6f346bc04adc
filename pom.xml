<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.ideal</groupId>
        <artifactId>ideal-dependencies</artifactId>
        <version>3.0.4-SNAPSHOT</version>
        <relativePath />
    </parent>

    <artifactId>ieai-script-service</artifactId>
    <version>${revision}</version>
    <packaging>pom</packaging>

    <modules>
        <module>script-starter</module>
        <module>script-tools-biz</module>
        <module>script-tools-api</module>
        <module>script-formatter</module>
        <module>scriptTools-spring-cloud-stream-subscriber</module>
        <module>ideal-sc-common</module>
        <module>script-spring-boot-starter</module>
    </modules>
    <!--当前脚本服务化包含了工具箱，为了区分不同模块，目前liquibase生成变更日志时使用module.name变量决定放到哪个目录，默认脚本服务化-->
    <profiles>
        <profile>
            <id>v9-script</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <module.name>script</module.name>
                <exclude.module.name>tools</exclude.module.name>
                <sonar.login.module.token>****************************************</sonar.login.module.token>
                <!--跳过工具箱单元测试-->
                <skip.tools-biz.tests>true</skip.tools-biz.tests>
            </properties>
        </profile>
        <profile>
            <id>v9-tools</id>
            <properties>
                <module.name>tools</module.name>
                <exclude.module.name>script</exclude.module.name>
                <sonar.login.module.token>****************************************</sonar.login.module.token>
                <!--跳过脚本服务化单元测试-->
                <skip.script-biz.tests>true</skip.script-biz.tests>
            </properties>
        </profile>
    </profiles>
    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven-jar-plugin.version>3.3.0</maven-jar-plugin.version>
        <revision>1.16.0-SNAPSHOT</revision>
        <maven.flatten.version>1.7.0</maven.flatten.version>
        <versions-maven-plugin.version>2.18.0</versions-maven-plugin.version>
        <!-- 公共组件依赖 -->
        <ideal-snowflake-spring-boot-starter.version>1.0.2-SNAPSHOT</ideal-snowflake-spring-boot-starter.version>
        <xxl-job-spring-boot-starter.version>1.0-SNAPSHOT</xxl-job-spring-boot-starter.version>
        <ideal-common.version>0.4.1-SNAPSHOT</ideal-common.version>
        <!--        平台管理基础数据api依赖-->
        <system-api.version>1.6.7-SNAPSHOT</system-api.version>
        <system-button-permission.version>1.1-SNAPSHOT</system-button-permission.version>
        <ideal-audit.version>1.3.1-SNAPSHOT</ideal-audit.version>
        <agent-gateway-formatter.version>2.1.0-SNAPSHOT</agent-gateway-formatter.version>
        <!--        agent管理服务api依赖-->
        <agent-management-api.version>1.0-SNAPSHOT</agent-management-api.version>
        <!--        agent-gateway服务依赖-->
        <agent-gateway-api.version>1.5.0-SNAPSHOT</agent-gateway-api.version>
        <!--        双人复合依赖-->
        <approval-api.version>1.4-SNAPSHOT</approval-api.version>
        <!--        脚本服务化API版本定义-->
        <script-api.version>2.1-SNAPSHOT</script-api.version>
        <!--        工具箱API版本定义-->
        <tools-api.version>1.1-SNAPSHOT</tools-api.version>
        <approval-client.version>1.2-SNAPSHOT</approval-client.version>
        <!--        平台管理工具类依赖-->
        <system-common-component.version>1.5-SNAPSHOT</system-common-component.version>
        <gateway-filter.version>1.0-SNAPSHOT</gateway-filter.version>
        <liquibase.core.version>4.27.0</liquibase.core.version>
        <ideal-file-generator-plugin.version>1.0.0</ideal-file-generator-plugin.version>
        <notification-api.version>1.0-SNAPSHOT</notification-api.version>
        <!--        工具箱使用，引擎依赖-->
        <engine-api.version>1.5-SNAPSHOT</engine-api.version>
        <engine-monitor.version>1.5-SNAPSHOT</engine-monitor.version>
        <studio-api.version>1.5-SNAPSHOT</studio-api.version>
        <!--         mq封装依赖-->
        <ideal-message-spring-cloud-stream-publisher.version>1.0-SNAPSHOT</ideal-message-spring-cloud-stream-publisher.version>
        <liquibase-maven-plugin.version>4.27.0</liquibase-maven-plugin.version>

        <sonar-maven-plugin.version>3.11.0.3922</sonar-maven-plugin.version>
        <maven-surefire-plugin.version>3.5.3</maven-surefire-plugin.version>
        <sonar.login>${sonar.login.module.token}</sonar.login>
        <sonar.projectKey>ieai-${module.name}-service</sonar.projectKey>
        <sonar.projectName>ieai-${module.name}-service</sonar.projectName>
        <sonar.host.url>http://*************:9000</sonar.host.url>
        <sonar.coverage.exclusions>
            **/com/ideal/${exclude.module.name}/**,
            **/annotation/**,
            **/mapper/**,
            **/model/**,
            **/config/**,
            **/aop/**,
            **/constant/**,
            **/interceptor/**,
            **/filter/**,
            **/dto/**,
            **/com/ideal/script/common/constant/permission/**
        </sonar.coverage.exclusions>
        <skip.script-biz.tests>false</skip.script-biz.tests>
        <skip.tools-biz.tests>false</skip.tools-biz.tests>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.apache.dubbo</groupId>
                <artifactId>dubbo-filter-validation</artifactId>
                <version>${dubbo.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ideal</groupId>
                <artifactId>ideal-snowflake-spring-boot-starter</artifactId>
                <version>${ideal-snowflake-spring-boot-starter.version}</version>
            </dependency>
            <!-- 支持zk的的profile依赖中没排除 slf4j-reload4j  -->
            <dependency>
                <groupId>org.apache.zookeeper</groupId>
                <artifactId>zookeeper</artifactId>
                <version>${zookeeper.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>slf4j-reload4j</artifactId>
                        <groupId>org.slf4j</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
                <version>${spring-cloud-starter-alibaba-nacos.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.alibaba.nacos</groupId>
                        <artifactId>nacos-client</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.alibaba.spring</groupId>
                        <artifactId>spring-context-support</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
                <version>${spring-cloud-starter-alibaba-nacos.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.alibaba.nacos</groupId>
                        <artifactId>nacos-client</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.alibaba.spring</groupId>
                        <artifactId>spring-context-support</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.ideal</groupId>
                <artifactId>xxl-job-spring-boot-starter</artifactId>
                <version>${xxl-job-spring-boot-starter.version}</version>
            </dependency>
            <!-- 当前工程中的内部依赖定义 start============================ -->
            <!--        脚本服务化、定时任务公共依赖-->
            <dependency>
                <groupId>com.ideal</groupId>
                <artifactId>ideal-sc-common</artifactId>
                <version>1.0.0</version>
            </dependency>
            <!-- 审计组件-->
            <dependency>
                <groupId>com.ideal</groupId>
                <artifactId>ideal-audit</artifactId>
                <version>${ideal-audit.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ideal</groupId>
                <artifactId>script-api</artifactId>
                <version>${script-api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ideal</groupId>
                <artifactId>notification-api</artifactId>
                <version>${notification-api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ideal</groupId>
                <artifactId>engine-api</artifactId>
                <version>${engine-api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ideal</groupId>
                <artifactId>scriptTools-spring-cloud-stream-subscriber</artifactId>
                <version>1.0.0</version>
            </dependency>
            <dependency>
                <groupId>com.ideal</groupId>
                <artifactId>ideal-message-spring-cloud-stream-publisher</artifactId>
                <version>${ideal-message-spring-cloud-stream-publisher.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ideal</groupId>
                <artifactId>engine-monitor</artifactId>
                <version>${engine-monitor.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ideal</groupId>
                <artifactId>script-biz</artifactId>
                <version>1.0.0</version>
            </dependency>
            <dependency>
                <groupId>com.ideal</groupId>
                <artifactId>script-spring-boot-starter</artifactId>
                <version>1.0.0</version>
            </dependency>

            <dependency>
                <groupId>com.ideal</groupId>
                <artifactId>tools-biz</artifactId>
                <version>1.0.0</version>
            </dependency>

            <dependency>
                <groupId>com.ideal</groupId>
                <artifactId>tools-api</artifactId>
                <version>${tools-api.version}</version>
            </dependency>
            <!-- 当前工程中的内部依赖定义 end============================ -->
            <dependency>
                <groupId>com.ideal</groupId>
                <artifactId>ideal-common</artifactId>
                <version>${ideal-common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ideal</groupId>
                <artifactId>gateway-filter</artifactId>
                <version>${gateway-filter.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ideal</groupId>
                <artifactId>agent-gateway-formatter</artifactId>
                <version>${agent-gateway-formatter.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ideal</groupId>
                <artifactId>system-api</artifactId>
                <version>${system-api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ideal</groupId>
                <artifactId>system-button-permission</artifactId>
                <version>${system-button-permission.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ideal</groupId>
                <artifactId>agent-management-api</artifactId>
                <version>${agent-management-api.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.ideal</groupId>
                        <artifactId>ideal-common</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.ideal</groupId>
                <artifactId>agent-gateway-api</artifactId>
                <version>${agent-gateway-api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ideal</groupId>
                <artifactId>approval-api</artifactId>
                <version>${approval-api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.ideal</groupId>
                <artifactId>system-common-component</artifactId>
                <version>${system-common-component.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ideal</groupId>
                <artifactId>studio-api</artifactId>
                <version>${studio-api.version}</version>
            </dependency>

            <dependency>
                <groupId>org.liquibase</groupId>
                <artifactId>liquibase-core</artifactId>
                <version>${liquibase.core.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ideal</groupId>
                <artifactId>approval-client</artifactId>
                <version>${approval-client.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-surefire-plugin</artifactId>
                    <version>${maven-surefire-plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.sonarsource.scanner.maven</groupId>
                    <artifactId>sonar-maven-plugin</artifactId>
                    <version>${sonar-maven-plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.liquibase</groupId>
                    <artifactId>liquibase-maven-plugin</artifactId>
                    <version>${liquibase-maven-plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>com.ideal</groupId>
                    <artifactId>ideal-file-generator-plugin</artifactId>
                    <version>${ideal-file-generator-plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <version>${spring.boot.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-jar-plugin</artifactId>
                    <version>${maven-jar-plugin.version}</version>
                    <configuration>
                        <excludes>
                            <exclude>rebel.xml</exclude>
                        </excludes>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>flatten-maven-plugin</artifactId>
                    <version>${maven.flatten.version}</version>
                    <configuration>
                        <updatePomFile>true</updatePomFile>
                        <flattenMode>resolveCiFriendliesOnly</flattenMode>
                    </configuration>
                    <executions>
                        <execution>
                            <id>flatten</id>
                            <phase>process-resources</phase>
                            <goals>
                                <goal>flatten</goal>
                            </goals>
                        </execution>
                        <execution>
                            <id>flatten-clean</id>
                            <phase>clean</phase>
                            <goals>
                                <goal>clean</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>versions-maven-plugin</artifactId>
                    <version>${versions-maven-plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>build-helper-maven-plugin</artifactId>
                </plugin>
            </plugins>
        </pluginManagement>
        <plugins>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>versions-maven-plugin</artifactId>
                <configuration>
                    <generateBackupPoms>false</generateBackupPoms>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.sonarsource.scanner.maven</groupId>
                <artifactId>sonar-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>pl.project13.maven</groupId>
                <artifactId>git-commit-id-plugin</artifactId>
                <configuration>
                    <failOnNoGitDirectory>false</failOnNoGitDirectory>
                    <offline>true</offline>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <id>jacoco-site</id>
                        <phase>test</phase>
                        <goals>
                            <goal>report</goal>
                        </goals>
                        <configuration>
                            <excludes>
                                <exclude>**/com/ideal/${exclude.module.name}/**</exclude>
                                <exclude>**/annotation/**</exclude>
                                <exclude>**/config/**</exclude>
                                <exclude>**/exception/**</exclude>
                                <exclude>**/mapper/**</exclude>
                                <exclude>**/model/**</exclude>
                                <exclude>**/dto/**</exclude>
                                <exclude>**/com/ideal/script/common/constant/permission/**</exclude>
                            </excludes>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
